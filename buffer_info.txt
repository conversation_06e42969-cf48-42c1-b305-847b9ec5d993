Buffer object properties:
  Type: <class 'arena_api.buffer._Buffer'>
  Width: 640
  Height: 480
  Data type: <class 'list'>
  Data length: 2457600

Buffer attributes:
  bits_per_pixel
  buffer_size
  compressed_image_data
  compressed_image_pbytes
  compressed_image_pdata
  compressed_image_pixel_format
  compressed_image_timestamp_ns
  data
  frame_id
  get_chunk
  has_chunkdata
  has_imagedata
  height
  is_compressedimage
  is_data_larger_than_buffer
  is_incomplete
  is_valid_crc
  offset_x
  offset_y
  padding_x
  padding_y
  payload_size
  payload_type
  pbytes
  pdata
  pixel_endianness
  pixel_format
  size_filled
  timestamp_ns
  width
  xbuffer

Trying to access data...
  buffer.data: <class 'list'>
  buffer.data length: 2457600
  Converted to bytes: 2457600 bytes
