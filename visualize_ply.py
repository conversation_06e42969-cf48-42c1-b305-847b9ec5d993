#!/usr/bin/env python3
"""
PLY File Visualization Tool using Open3D
Visualizes PLY files with option to thin the point cloud
"""

import open3d as o3d
import numpy as np
import argparse
import os
import sys

def load_and_visualize_ply(ply_file, thin_points=None, show_stats=True, stats_only=False):
    """
    Load and visualize a PLY file with Open3D

    Args:
        ply_file (str): Path to the PLY file
        thin_points (int): Number of points to randomly sample (None for all points)
        show_stats (bool): Whether to print point cloud statistics
        stats_only (bool): Only show statistics, don't open visualization window
    """
    
    # Check if file exists
    if not os.path.exists(ply_file):
        print(f"Error: PLY file '{ply_file}' not found!")
        return False
    
    print(f"Loading PLY file: {ply_file}")
    
    try:
        # Load the point cloud
        pcd = o3d.io.read_point_cloud(ply_file)
        
        if len(pcd.points) == 0:
            print("Error: No points found in the PLY file!")
            return False
            
        print(f"Successfully loaded point cloud with {len(pcd.points)} points")
        
        # Show statistics
        if show_stats:
            print_point_cloud_stats(pcd)
        
        # Thin the point cloud if requested
        if thin_points is not None and thin_points > 0:
            if len(pcd.points) > thin_points:
                print(f"Thinning point cloud from {len(pcd.points)} to {thin_points} points...")
                
                # Randomly sample points
                indices = np.random.choice(len(pcd.points), thin_points, replace=False)
                pcd = pcd.select_by_index(indices)
                
                print(f"Point cloud thinned to {len(pcd.points)} points")
            else:
                print(f"Point cloud already has {len(pcd.points)} points, no thinning needed")
        
        # Skip visualization if stats_only is True
        if stats_only:
            print("Statistics only mode - skipping visualization")
            return True
        
        # Cut ROI box
        pcd = pcd.crop(o3d.geometry.AxisAlignedBoundingBox(min_bound=(7860, 7500, 400), max_bound=(8430, 8700, 2000)))

        # Visualize the point cloud
        print("Opening 3D visualization...")
        print("Controls:")
        print("  - Mouse: Rotate view")
        print("  - Mouse wheel: Zoom")
        print("  - Ctrl+Mouse: Pan")
        print("  - Press 'Q' or close window to exit")

        try:
            # Set up visualization
            vis = o3d.visualization.Visualizer()  # type: ignore
            vis.create_window(window_name=f"PLY Viewer - {os.path.basename(ply_file)}")
            vis.add_geometry(pcd)

            # Set rendering options for better visualization
            render_option = vis.get_render_option()
            render_option.point_size = 2.0  # Make points more visible
            render_option.background_color = np.array([0.1, 0.1, 0.1])  # Dark background

            # Run the visualizer
            vis.run()
            vis.destroy_window()
        except Exception as viz_error:
            print(f"Warning: Could not open visualization window: {viz_error}")
            print("This might be due to running in a headless environment or missing display.")
            return True  # Still consider it successful since we loaded the data
        
        return True
        
    except Exception as e:
        print(f"Error loading or visualizing PLY file: {e}")
        return False

def print_point_cloud_stats(pcd):
    """
    Print statistics about the point cloud
    """
    print("\n" + "="*50)
    print("POINT CLOUD STATISTICS")
    print("="*50)
    
    points = np.asarray(pcd.points)
    
    print(f"Number of points: {len(points)}")
    
    if len(points) > 0:
        # Coordinate ranges
        min_coords = np.min(points, axis=0)
        max_coords = np.max(points, axis=0)
        
        print(f"X range: {min_coords[0]:.3f} to {max_coords[0]:.3f}")
        print(f"Y range: {min_coords[1]:.3f} to {max_coords[1]:.3f}")
        print(f"Z range: {min_coords[2]:.3f} to {max_coords[2]:.3f}")
        
        # Bounding box dimensions
        dimensions = max_coords - min_coords
        print(f"Bounding box dimensions: {dimensions[0]:.3f} x {dimensions[1]:.3f} x {dimensions[2]:.3f}")
        
        # Check if colors are available
        if pcd.has_colors():
            colors = np.asarray(pcd.colors)
            print(f"Colors: Available ({len(colors)} color values)")
            print(f"Color range: R[{np.min(colors[:,0]):.3f}-{np.max(colors[:,0]):.3f}] "
                  f"G[{np.min(colors[:,1]):.3f}-{np.max(colors[:,1]):.3f}] "
                  f"B[{np.min(colors[:,2]):.3f}-{np.max(colors[:,2]):.3f}]")
        else:
            print("Colors: Not available")
        
        # Check if normals are available
        if pcd.has_normals():
            print("Normals: Available")
        else:
            print("Normals: Not available")
    
    print("="*50 + "\n")

def main():
    parser = argparse.ArgumentParser(description="Visualize PLY files using Open3D")
    parser.add_argument("ply_file", nargs="?", default="Python_Helios_3D.ply",
                       help="Path to PLY file (default: Python_Helios_3D.ply)")
    parser.add_argument("--thin", "-t", type=int, default=None,
                       help="Randomly sample this many points (default: use all points)")
    parser.add_argument("--no-stats", action="store_true",
                       help="Don't show point cloud statistics")
    parser.add_argument("--stats-only", action="store_true",
                       help="Only show statistics, don't open visualization window")

    args = parser.parse_args()

    print("PLY File Visualization Tool")
    print("Using Open3D for 3D visualization")
    print("-" * 40)

    success = load_and_visualize_ply(
        args.ply_file,
        thin_points=args.thin,
        show_stats=not args.no_stats,
        stats_only=args.stats_only
    )

    if success:
        print("Visualization completed successfully!")
    else:
        print("Visualization failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
