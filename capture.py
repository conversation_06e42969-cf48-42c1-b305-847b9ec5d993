#!/usr/bin/env python3
"""
Helios 3D Camera Capture Script
Captures 3D data and creates similar outputs to the C examples
"""

import time
import sys
import numpy as np
from arena_api.system import system

def main():
    print("\nHelios Python Capture Example")
    print("=" * 40)
    
    # Create device
    print("  Creating device...")
    try:
        devices = system.create_device()
        if not devices:
            print("  No devices found!")
            return
            
        device = devices[0]
        print(f"  Found device: {device}")
        
        # Get device info
        device_info = device.nodemap['DeviceModelName'].value
        print(f"  Device model: {device_info}")
        
        # Check if this is a 3D camera
        try:
            # Try to access 3D-specific nodes
            pixel_format_node = device.nodemap['PixelFormat']
            available_formats = pixel_format_node.enumentry_names
            print(f"  Available pixel formats: {available_formats}")
            
            # Set to 3D format if available
            if 'Coord3D_ABCY16' in available_formats:
                print("  Setting pixel format to Coord3D_ABCY16")
                pixel_format_node.value = 'Coord3D_ABCY16'
                
                # Set operating mode if available
                try:
                    operating_mode_node = device.nodemap['Scan3dOperatingMode']
                    operating_mode_node.value = 'Distance3000mmSingleFreq'
                    print("  Set operating mode to Distance3000mmSingleFreq")
                except KeyError:
                    print("  Operating mode node not found, continuing...")
                    
            else:
                print("  3D format not available, using default format")
                
        except Exception as e:
            print(f"  Error configuring 3D settings: {e}")
            
        # Start stream
        print("  Starting stream...")
        device.start_stream()
        
        # Get an image
        print("  Acquiring image...")
        buffer = device.get_buffer()
        
        print(f"  Captured image: {buffer.width}x{buffer.height}")
        print(f"  Pixel format: {buffer.pixel_format}")
        print(f"  Buffer size: {len(buffer.data)} bytes")
        
        # Get image data as numpy array
        image_array = np.asarray(buffer.data, dtype=np.uint8)
        print(f"  Image array shape: {image_array.shape}")
        
        print(f"Device: {device_info}")
        print(f"Resolution: {buffer.width}x{buffer.height}")
        print(f"Pixel format: {buffer.pixel_format}")
        print(f"Buffer size: {len(buffer.data)} bytes")
        print(f"Timestamp: {buffer.timestamp_ns} ns")
            
        # Requeue buffer
        device.requeue_buffer(buffer)
        
        # Stop stream
        device.stop_stream()
        print("  Stream stopped")
        
    except Exception as e:
        print(f"  Error: {e}")
    finally:
        # Clean up
        system.destroy_device()
        print("  Cleanup complete")
        
    print("\nCapture completed successfully!")

if __name__ == "__main__":
    main()
