#!/usr/bin/env python3
"""
Helios 3D Camera Class
Provides a simple interface for capturing 3D point clouds using the Helios camera
"""

import numpy as np
import open3d as o3d
from arena_api.system import system


class Helios2Camera:
    """
    A camera class for Helios3 3D cameras with optimized settings.  
    Provides methods to start/stop capture and get point clouds as Open3D objects.
    
    ## Simple single capture
    ```
    camera = Helios2Camera()
    pcd = camera.get_pointcloud()  # Auto-start/stop
    ```

    ## Efficient multiple captures
    ```
    camera.start_capture()
    for i in range(10):
        pcd = camera.get_pointcloud()  # Fast capture
    camera.stop_capture()
    ```
    """
    
    def __init__(self):
        """
        Initialize the camera class (assumes Helios2 camera)
        """
        self.device = None
        self.is_streaming = False
        self.scale_x: float = 1.0
        self.scale_y: float = 1.0
        self.scale_z: float = 1.0
        self.offset_x: float = 0.0
        self.offset_y: float = 0.0
        self.offset_z: float = 0.0
        

                
    def _configure_optimized_settings(self):
        """Configure camera with optimized settings for 3D capture"""
        if not self.device:
            raise RuntimeError("No device available")

        nodemap = self.device.nodemap

        # Set pixel format to 3D
        print("  Setting pixel format to Coord3D_ABCY16")
        nodemap['PixelFormat'].value = 'Coord3D_ABCY16'

        # Set operating mode for Helios2 camera
        print("  Setting 3D operating mode for Helios2")
        try:
            operating_mode_node = nodemap['Scan3dOperatingMode']
            available_modes = operating_mode_node.enumentry_names
            print(f"  Available operating modes: {available_modes}")

            # Choose the best available mode (prefer 3000mm single freq for optimal performance)
            if 'Distance3000mmSingleFreq' in available_modes:
                operating_mode_node.value = 'Distance3000mmSingleFreq'
                print("  Set operating mode to Distance3000mmSingleFreq")
            elif 'Distance1250mmSingleFreq' in available_modes:
                operating_mode_node.value = 'Distance1250mmSingleFreq'
                print("  Set operating mode to Distance1250mmSingleFreq")
            elif available_modes:
                # Use the first available mode as fallback
                operating_mode_node.value = available_modes[0]
                print(f"  Set operating mode to {available_modes[0]} (fallback)")
            else:
                print("  Warning: No operating modes available")
        except Exception as e:
            print(f"  Warning: Could not set operating mode: {e}")
            
        # Set acquisition mode for continuous capture
        nodemap['AcquisitionMode'].value = 'Continuous'
        
        # Get coordinate scales and offsets for proper 3D conversion
        print("  Getting coordinate scales and offsets")
        
        # X coordinate
        nodemap["Scan3dCoordinateSelector"].value = "CoordinateA"
        self.scale_x = nodemap["Scan3dCoordinateScale"].value
        self.offset_x = nodemap["Scan3dCoordinateOffset"].value
        
        # Y coordinate  
        nodemap["Scan3dCoordinateSelector"].value = "CoordinateB"
        self.scale_y = nodemap["Scan3dCoordinateScale"].value
        self.offset_y = nodemap["Scan3dCoordinateOffset"].value
        
        # Z coordinate
        nodemap["Scan3dCoordinateSelector"].value = "CoordinateC"
        self.scale_z = nodemap["Scan3dCoordinateScale"].value
        self.offset_z = nodemap["Scan3dCoordinateOffset"].value
        
        print(f"  Coordinate scales: X={self.scale_x}, Y={self.scale_y}, Z={self.scale_z}")
        
    def start_capture(self):
        """
        Start the camera capture stream
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.is_streaming:
                print("  Camera is already streaming")
                return True
                
            # Create device if not already created
            if not self.device:
                print("  Creating device...")
                devices = system.create_device()
                if not devices:
                    print("  Error: No devices found!")
                    return False
                    
                self.device = devices[0]
                device_info = self.device.nodemap['DeviceModelName'].value
                print(f"  Found device: {device_info}")

                # Configure optimized settings for Helios2
                self._configure_optimized_settings()
            
            # Start streaming
            print("  Starting stream...")
            self.device.start_stream()
            self.is_streaming = True
            print("  Stream started successfully")
            return True
            
        except Exception as e:
            print(f"  Error starting capture: {e}")
            return False
    
    def stop_capture(self):
        """
        Stop the camera capture stream
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.is_streaming:
                print("  Camera is not streaming")
                return True
                
            if self.device:
                print("  Stopping stream...")
                self.device.stop_stream()
                self.is_streaming = False
                print("  Stream stopped successfully")
                return True
            else:
                print("  No device to stop")
                return False
                
        except Exception as e:
            print(f"  Error stopping capture: {e}")
            self.is_streaming = False  # Update status even on error
            return False
    
    def _process_3d_buffer(self, buffer):
        """
        Process the 3D buffer data into coordinate arrays
        
        Args:
            buffer: Camera buffer containing 3D data
            
        Returns:
            tuple: (points, colors) as numpy arrays
        """
        width = buffer.width
        height = buffer.height
        
        # Convert buffer data to numpy array
        data_bytes = bytes(buffer.data)
        data = np.frombuffer(data_bytes, dtype=np.uint16)
        
        # Reshape to (height, width, 4) for ABCY channels
        image_3d = data.reshape((height, width, 4))
        
        # Extract channels
        A_channel = image_3d[:, :, 0]  # X coordinate
        B_channel = image_3d[:, :, 1]  # Y coordinate  
        C_channel = image_3d[:, :, 2]  # Z coordinate (depth)
        Y_channel = image_3d[:, :, 3]  # Intensity
        
        # Convert to real-world coordinates using scales and offsets
        # Ensure scales and offsets are available
        if None in [self.scale_x, self.scale_y, self.scale_z, self.offset_x, self.offset_y, self.offset_z]:
            raise RuntimeError("Camera scales and offsets not properly initialized")

        X_mm = (A_channel.astype(np.float32) * self.scale_x + self.offset_x)
        Y_mm = (B_channel.astype(np.float32) * self.scale_y + self.offset_y)
        Z_mm = (C_channel.astype(np.float32) * self.scale_z + self.offset_z)
        
        # Create valid mask (filter out invalid points where Z is 0 or negative)
        valid_mask = Z_mm > 0
        
        # Extract valid points
        valid_points = np.column_stack([
            X_mm[valid_mask],
            Y_mm[valid_mask], 
            Z_mm[valid_mask]
        ])
        
        # Create colors based on intensity (normalized to 0-1 range)
        intensity_values = Y_channel[valid_mask].astype(np.float32)
        if len(intensity_values) > 0:
            intensity_min = intensity_values.min()
            intensity_max = intensity_values.max()
            if intensity_max > intensity_min:
                normalized_intensity = (intensity_values - intensity_min) / (intensity_max - intensity_min)
            else:
                normalized_intensity = np.ones_like(intensity_values) * 0.5
        else:
            normalized_intensity = np.array([])
            
        # Convert to RGB (grayscale based on intensity)
        colors = np.column_stack([
            normalized_intensity,
            normalized_intensity,
            normalized_intensity
        ])
        
        return valid_points, colors
    
    def get_pointcloud(self):
        """
        Capture a single frame and return as Open3D point cloud
        
        Returns:
            o3d.geometry.PointCloud: Point cloud object, or None if failed
        """
        was_streaming = self.is_streaming
        
        try:
            # Start capture if not already running
            if not self.start_capture():
                return None
                
            if not self.device:
                print("  Error: No device available")
                return None

            print("  Acquiring 3D data...")
            buffer = self.device.get_buffer()

            # Process the 3D data
            points, colors = self._process_3d_buffer(buffer)

            # Requeue the buffer
            self.device.requeue_buffer(buffer)
            
            # Create Open3D point cloud
            pcd = o3d.geometry.PointCloud()
            if len(points) > 0:
                pcd.points = o3d.utility.Vector3dVector(points)
                pcd.colors = o3d.utility.Vector3dVector(colors)
                print(f"  Created point cloud with {len(points)} points")
            else:
                print("  Warning: No valid points found in capture")
            
            # Stop capture if it wasn't running before
            if not was_streaming:
                self.stop_capture()
                
            return pcd
            
        except Exception as e:
            print(f"  Error getting point cloud: {e}")
            # Stop capture if it wasn't running before and we started it
            if not was_streaming and self.is_streaming:
                self.stop_capture()
            return None
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            if self.is_streaming:
                self.stop_capture()
            if self.device:
                system.destroy_device()
        except:
            pass  # Ignore cleanup errors


# Example usage
if __name__ == "__main__":
    print("Helios Camera Class Example")
    print("=" * 40)
    
    # Create camera instance
    camera = Helios2Camera()
    
    # Get a point cloud
    pcd = camera.get_pointcloud()
    
    if pcd and len(pcd.points) > 0:
        print(f"Successfully captured point cloud with {len(pcd.points)} points")
        
        # Optionally visualize (uncomment to enable)
        print("Visualizing point cloud...")
        o3d.visualization.draw_geometries([pcd])  # type: ignore
    else:
        print("Failed to capture point cloud")
