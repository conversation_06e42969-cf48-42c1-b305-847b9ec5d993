#!/usr/bin/env python3
"""
Inspect buffer properties and methods
"""

from arena_api.system import system

def inspect_buffer():
    try:
        devices = system.create_device()
        device = devices[0]
        
        # Set format
        device.nodemap['PixelFormat'].value = 'Coord3D_ABCY16'
        device.start_stream()
        
        # Get buffer
        buffer = device.get_buffer()
        
        print("Buffer object properties:")
        print(f"  Type: {type(buffer)}")
        print(f"  Width: {buffer.width}")
        print(f"  Height: {buffer.height}")
        print(f"  Data type: {type(buffer.data)}")
        print(f"  Data length: {len(buffer.data)}")
        
        # List all available methods/attributes
        print("\nBuffer attributes:")
        for attr in dir(buffer):
            if not attr.startswith('_'):
                print(f"  {attr}")
                
        # Try to access data directly
        print("\nTrying to access data...")
        try:
            print(f"  buffer.data: {type(buffer.data)}")
            print(f"  buffer.data length: {len(buffer.data)}")
            # Convert to bytes
            data_bytes = bytes(buffer.data)
            print(f"  Converted to bytes: {len(data_bytes)} bytes")
        except Exception as e:
            print(f"  Error accessing data: {e}")
            
        device.requeue_buffer(buffer)
        device.stop_stream()
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        system.destroy_device()

if __name__ == "__main__":
    inspect_buffer()
