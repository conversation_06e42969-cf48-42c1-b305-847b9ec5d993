import numpy as np


def get_obb(points):
    """
    Compute the Oriented Bounding Box (OBB) for a 2D point cloud.

    Args:
        points: numpy array of shape (N, 2) representing 2D points

    Returns:
        tuple: (center, size, rotation_matrix)
            - center: numpy array of shape (2,) - center of the OBB
            - size: numpy array of shape (2,) - [length, width] with length >= width
            - rotation_matrix: numpy array of shape (2, 2) - rotation matrix
    """
    # Compute covariance matrix
    cov_matrix = np.cov(points, rowvar=False, bias=True)

    # Get eigenvalues and eigenvectors
    eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

    # Sort eigenvalues and eigenvectors by eigenvalue magnitude (descending)
    # This ensures the first eigenvector corresponds to the direction of maximum variance
    sort_indices = np.argsort(eigenvalues)[::-1]
    eigenvalues = eigenvalues[sort_indices]
    eigenvectors = eigenvectors[:, sort_indices]

    # The rotation matrix is the eigenvectors matrix
    rotation_matrix = eigenvectors

    # Transform points to the aligned coordinate system
    # Use the inverse (transpose) of the rotation matrix
    aligned_points = np.dot(points, np.linalg.inv(rotation_matrix))

    # Get the minimum and maximum coordinates in the aligned space
    min_coords = np.min(aligned_points, axis=0)
    max_coords = np.max(aligned_points, axis=0)

    # Calculate size (full width and height, not half)
    size = max_coords - min_coords

    # Calculate center in aligned space
    center_aligned = (min_coords + max_coords) * 0.5

    # Transform center back to original coordinate system
    center = np.dot(center_aligned, rotation_matrix)

    # Ensure the longest dimension is first
    if size[1] > size[0]:
        # Swap dimensions and rotate the rotation matrix by 90 degrees
        size = size[::-1]  # Swap width and height
        # Rotate rotation matrix by 90 degrees (swap columns and negate first)
        rotation_matrix = rotation_matrix[:, [1, 0]]
        rotation_matrix[:, 0] *= -1

    return center, size, rotation_matrix
