{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------\n", "# -----------------------------------------------------------------------------\n", "# Warning:\n", "#\n", "# EVS examples support only on windows at the moment\n", "# -----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import ctypes\n", "import time\n", "\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.enums import PixelFormat\n", "from arena_api.__future__.save import Writer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### XYTP Heatmap\n", "\n", "> This example demonstrates saving a BGR heatmap of a XYPT frame. It captures\n", "> events as XYPT frame from EVS camera, interprets the XYPT data from the frame\n", "> to retrieve the time value for each event pixel and then converts this data into\n", "> a BGR buffer. The buffer is then used to create a jpg heatmap image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "TAB3 = \"\t \"\n", "RGB_MIN = 0\n", "RGB_MAX = 255\n", "IMAGE_TIMEOUT = 2000\n", "JPG_FILE_NAME = \"Images/Python_XYTP_Heatmap.jpg\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'{TAB1}Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{TAB1}{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "            tries += 1\n", "    else:\n", "        print(f'{TAB1}Created {len(devices)} device(s)')\n", "        device = system.select_device(devices)\n", "        nodemap = device.nodemap\n", "        tl_stream_nodemap = device.tl_stream_nodemap\n", "        break\n", "else:\n", "    raise Exception(f'{TAB1}No device found! Please connect a device and run '\n", "                    f'the example again.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["width = nodemap.get_node(\"Width\").value\n", "height = nodemap.get_node(\"Height\").value\n", "print(f'{TAB1}Image (w, h) = ({width} , {height})')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Configure device settings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Set acquisition mode to \\'Continuous\\'')\n", "initial_acquisition_mode = nodemap.get_node(\"AcquisitionMode\").value\n", "nodemap.get_node(\"AcquisitionMode\").value = \"Continuous\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Set buffer handling mode to \\'NewestOnly\\'')\n", "tl_stream_nodemap[\"StreamBufferHandlingMode\"].value = \"NewestOnly\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: EVS\n", ">\tThe EventFormat node determines whether the camera can use the EVS datastream engine. \n", ">   When set to EVS, Arena switches to the EVS engine. If EVS is not supported, \n", ">   the acquisition mode is restored to its original setting, and the process is exited."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Set Event Format to EVT3.0')\n", "\n", "try:\n", "    event_format_initial = nodemap.get_node('EventFormat').value\n", "    nodemap[\"EventFormat\"].value = \"EVT3_0\"\n", "except:\n", "    print(f'{TAB1}Connected camera does not support any EventFormats\\n')\n", "    nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode\n", "    system.destroy_device()\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set camera event rate to 10 Mev/s"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Set Camera Event Rate to 10 Mev/s')\n", "erc_enable_initial = nodemap.get_node('ErcEnable').value\n", "nodemap[\"ErcEnable\"].value = True\n", "\n", "camera_event_rate_initial = nodemap.get_node('ErcRateLimit').value\n", "nodemap[\"ErcRateLimit\"].value = 10.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#####  Set evs output format to XYTPFrame"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Set EVS output format to XYTPFrame')\n", "tl_stream_nodemap[\"StreamEvsOutputFormat\"].value = \"XYTPFrame\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Create BGR Heatmap And Save Function\n", "\n", "Collects time pixel channel for time range and sets pixel color buffers based on time bound \n", "then writes output buffer as jpg heatmap."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_bgr_heatmap_and_save(buffer, filepath):\n", "\t# XYTP frame buffer info\n", "\t# \"LUCID_LucidXYTP128f\" pixelformats have 4 channels pre pixel.\n", "\t#  Each channel is 32 bits float and they represent:\n", "\t#   - x position\n", "\t#   - y postion\n", "\t#   - t time \n", "\t#   - p event \n", "\t# the value can be dynamically calculated this way:\n", "\t#   int(buffer.bits_per_pixel/32) # 32 is the size of each channel\n", "\tLUCID_LucidXYTP128f_channels_per_pixel = xytp_frame_step_size = 4\n", "\twidth, height = buffer.width, buffer.height\n", "\tnum_pixels = width * height\n", " \n", "\t# Note that xytp buffer will only contain pixel with events\n", "\t# the number valid pixel size can be dynamically calculated this way:\n", "\t# \tint(buffer.size_filled / byte_per_pixle)\n", "\tbytes_per_pixle = buffer.bits_per_pixel / 8\n", "\tvalid_event_size = int(buffer.size_filled / bytes_per_pixle)\n", "\tsrc_data = ctypes.cast(buffer.pdata, ctypes.POINTER(ctypes.c_float))\n", "\tprint(f\"{TAB2}Number of valid event pixel is: {valid_event_size}\")\n", "\n", "\t# find minT and maxT for time channel (t)\n", "\tminT = float('inf')\n", "\tmaxT = float('-inf')\n", "\tfor i in range(valid_event_size):\n", "\t\tt = src_data[i * xytp_frame_step_size + 2]  # accessing the 't' time channel in XYTP\n", "\t\tminT = min(minT, t)\n", "\t\tmaxT = max(maxT, t)\n", "  \n", "\t# init output array\n", "\tBGR8_channels_per_pixel = bgr8_step_size = 3  # Blue, <PERSON>, Red\n", "\tarray_BGR8_size_in_bytes = BGR8_channels_per_pixel * num_pixels\n", "\tCustomArrayType = (ctypes.c_byte * array_BGR8_size_in_bytes)\n", "\tdst_bgr8_array = CustomArrayType()\n", " \n", "\t# set color bound base on time value range\n", "\trange_t = maxT - minT\n", "\tyellow_border = minT + range_t / 4\n", "\tgreen_border = minT + range_t / 2\n", "\tcyan_border = minT + 3 * range_t / 4\n", "\tblue_border = maxT\n", "\n", "\t# assign colors based on the time (t) values\n", "\tfor i in range(valid_event_size):\n", "\t\tx, y, t = src_data[i * xytp_frame_step_size], src_data[i * xytp_frame_step_size + 1], src_data[i * xytp_frame_step_size + 2]\n", "\n", "\t\t# color mapping logic based on time value\n", "\t\tif t <= yellow_border:\n", "\t\t\tpercentage = (t - minT) / (yellow_border - minT)\n", "\t\t\tred = RGB_MAX\n", "\t\t\tgreen = int(RGB_MAX * percentage)\n", "\t\t\tblue = RGB_MIN\n", "\t\telif t <= green_border:\n", "\t\t\tpercentage = (t - yellow_border) / (green_border - yellow_border)\n", "\t\t\tred = int(RGB_MAX * (1 - percentage))\n", "\t\t\tgreen = RGB_MAX\n", "\t\t\tblue = RGB_MIN\n", "\t\telif t <= cyan_border:\n", "\t\t\tpercentage = (t - green_border) / (cyan_border - green_border)\n", "\t\t\tred = RGB_MIN\n", "\t\t\tgreen = RGB_MAX\n", "\t\t\tblue = int(RGB_MAX * percentage)\n", "\t\telif t <= blue_border:\n", "\t\t\tpercentage = (t - cyan_border) / (blue_border - cyan_border)\n", "\t\t\tred = RGB_MIN\n", "\t\t\tgreen = int(RGB_MAX * (1 - percentage))\n", "\t\t\tblue = RGB_MAX\n", "\t\telse:\n", "\t\t\tred = RGB_MIN\n", "\t\t\tgreen = RGB_MIN\n", "\t\t\tblue = RGB_MAX\n", "\n", "\t\t# calculate pixel offset in BGR format for JPG\n", "\t\toffset = int((int(y) * width + int(x)) * bgr8_step_size)\n", "\t\tdst_bgr8_array[offset] = blue\n", "\t\tdst_bgr8_array[offset + 1] = green\n", "\t\tdst_bgr8_array[offset + 2] = red\n", "  \n", "\t# create and save the heatmap image\n", "\tprint(f\"{TAB2}Create BGR heatmap from XYPT Frame\")\n", "\tbuffer_bgr8 = BufferFactory.create(ctypes.cast(dst_bgr8_array, ctypes.POINTER(ctypes.c_ubyte)),\n", "\t\t\t\t\t\t\t\t\t\tarray_BGR8_size_in_bytes, width, height, PixelFormat.BGR8)\n", "\twriter = Writer.from_buffer(buffer_bgr8)\n", "\twriter.save(buffer_bgr8, filepath)\n", "\tprint(f'{TAB2}Save heatmap image as jpg to {writer.saved_images[-1]}')\n", "\tBufferFactory.destroy(buffer_bgr8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Start stream')\n", "device.start_stream(1)\n", "\n", "print(f'{TAB1}Get one image')\n", "\n", "buffer = device.get_buffer()\n", "\n", "\n", "'''\n", "Print image buffer info\n", "    Buffers contain image data.\n", "    Image data can also be copied and converted using BufferFactory.\n", "    That is necessary to retain image data, as we must also requeue the buffer.\n", "'''\n", "\n", "if buffer.is_incomplete:\n", "\tprint(f'{TAB3}Image {buffer.frame_id} is incomplete')\n", "else:\n", "\tcreate_bgr_heatmap_and_save(buffer,JPG_FILE_NAME)\n", "\t\n", "device.requeue_buffer(buffer)\n", "print(f'{TAB1}Image buffer requeued')\n", "\n", "device.stop_stream()\n", "print(f'{TAB1}Stream stopped')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap.get_node(\"ErcEnable\").value = erc_enable_initial\n", "nodemap.get_node(\"ErcRateLimit\").value = camera_event_rate_initial\n", "nodemap.get_node(\"EventFormat\").value = event_format_initial"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode\n", "\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}