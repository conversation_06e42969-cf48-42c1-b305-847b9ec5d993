# -----------------------------------------------------------------------------
# Copyright (c) 2024, Lucid Vision Labs, Inc.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
# OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
# NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
# BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
# ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.
# -----------------------------------------------------------------------------

import os
import time
from pathlib import Path

from arena_api import enums
from arena_api.__future__.save import Writer
from arena_api.system import system

'''
Sequencer HDR
	This example demonstrates saving a set of images using the sequencer. It will
	cycle through 3 sequencer states each one using a different exposure time.
	The images could then be used to generate an HDR image. Much like a state
	machine, in order to use the sequencer we must initialize each set
	appropriately. Each set can have its own exposure time and gain, and will
	contain information such as the sequencer starting position as well as paths
	to other sets. A set can have multiple paths where each path has its own next
	set, trigger source and trigger activation. In this example the sequencer has
	3 sets where set 0 goes to set 1, set 1 goes to set 2 and set 2 goes back to
	set 0, all being triggered on Frame Start.
'''
TAB1 = "  "
TAB2 = "    "

def create_devices_with_tries():
	'''
	This function waits for the user to connect a device before raising
		an exception
	'''

	tries = 0
	tries_max = 6
	sleep_time_secs = 10
	while tries < tries_max:  # Waits for devices
		devices = system.create_device()
		if not devices:
			print(
				f'{TAB1}Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '
				f'secs for a device to be connected!')
			for sec_count in range(sleep_time_secs):
				time.sleep(1)
				print(f'{TAB1}{sec_count + 1 } seconds passed ',
					'.' * sec_count, end='\r')
			tries += 1
		else:
			print(f'{TAB1}Created {len(devices)} device(s)')
			return devices
	else:
		raise Exception(f'{TAB1}No device found! Please connect a device and run '
						f'the example again.')


def set_sequencer_set(nodemap, set_number, exposure_time, path_next_set,
					trigger_source):
	'''
	Create configurations for a sequencer set
		and save to a given set number
	'''

	'''
	Set Sequencer Set Selector to sequence number
	'''
	nodemap['SequencerSetSelector'].value = set_number
	print(f'{TAB1}Updating set {set_number} :')

	# Set Exposure Time to the desired value
	nodemap['SequencerFeatureSelector'].value = 'ExposureTime'
	nodemap['ExposureTime'].value = exposure_time
	print(f'{TAB2}Exposure time value = {exposure_time}')

	'''
	Select the path we want it to follow from this set to the next set. There
		can be multiple paths so the first path will always be set to 0
	'''
	nodemap['SequencerPathSelector'].value = 0

	# Set next state in the sequence, ensure it does not exceed the maximum
	nodemap['SequencerSetNext'].value = path_next_set
	print(f'{TAB2}Set next            = {path_next_set}')

	# Set Sequencer Trigger Source to Frame Start
	nodemap['SequencerTriggerSource'].value = trigger_source
	print(f'{TAB2}Trigger source      = {trigger_source}')

	'''
	Save current state
		Once all appropriate settings have been configured, make sure to save the
		state to the sequence. Notice that these settings will be lost when the
		camera is power-cycled.
	'''
	print(f'{TAB2}Save sequence set {set_number}')
	nodemap['SequencerSetSave'].execute()


def acquire_and_save_buffers(device):

	# Get width, height, and pixel format nodes
	width_node = device.nodemap['Width']
	height_node = device.nodemap['Height']
	pixelformat_node = device.nodemap['PixelFormat']

	if not width_node.is_readable or \
			not height_node.is_readable or \
			not pixelformat_node.is_readable:
		raise Exception('Width, Height, or PixelFormat node is not readable')

	pixelformat_node.value = 'Mono8'

	# Starting the stream allocates buffers, which can be passed in as
	# an argument (default: 10), and begins filling them with data.
	print(f'{TAB1}Start streaming')
	with device.start_stream(3):

		# Get an image buffer in each set of sequencer
		print(f'{TAB1}Getting 3 image buffers')

		# Save images so we can view them later
		writer = Writer()

		# Run our 3 sets one time
		for count in range(3):
			print(f'{TAB1}Converting and saving image {count}')

			# Get image
			buffer = device.get_buffer()

			# Save images with default name
			writer.save(buffer, f"images/py_sequencer_HDR/image{count}.png")
			print(f'{TAB1}Image saved {writer.saved_images[-1]}')

			# Requeue image buffer
			device.requeue_buffer(buffer)
		print(f'{TAB1}Requeued {count + 1} buffers')

	# Stream stops automatically when the scope of the context manager ends
	print(f'{TAB1}Stream stopped')


def set_exposure_auto_to_off(nodemap):
	'''
	Disable ExposureAuto, which has some prerequisites
	'''

	# If Sequencer Configuration Mode is 'On', it makes 'ExposureAuto'
	# a read-only node
	if nodemap['SequencerConfigurationMode'].value == 'On':
		print(f'{TAB1}Turn \'SequencerConfigurationMode\' Off')
		nodemap['SequencerConfigurationMode'].value = 'Off'
		print(f'{TAB2}\'SequencerConfigurationMode\' is '
			f'''{nodemap['SequencerConfigurationMode'].value} now''')

	print(f'{TAB1}Turn \'ExposureAuto\' Off')
	nodemap['ExposureAuto'].value = 'Off'
	print(f'{TAB2}\'ExposureAuto\' is {nodemap["ExposureAuto"].value} now')


def set_sequencer_configuration_mode_on(nodemap):
	'''
	Enable sequencer configuration mode
		which has some prerequisites
	'''

	# If Sequencer Mode is 'On', it makes 'SequencerConfigurationMode'
	# a read-only node
	if nodemap['SequencerMode'].value == 'On':
		print(f'{TAB1}Turn \'SequencerMode\' Off')
		nodemap['SequencerMode'].value = 'Off'
		print(f'{TAB2}\'SequencerMode\' is {nodemap["SequencerMode"].value} now')

	print(f'{TAB1}Turn \'SequencerConfigurationMode\' On')
	nodemap['SequencerConfigurationMode'].value = 'On'
	print(f'{TAB2}\'SequencerConfigurationMode\' is '
		  f'{nodemap["SequencerConfigurationMode"].value} now')


def example_entry_point():

	# Create a device
	devices = create_devices_with_tries()
	device = system.select_device(devices)

	nodemap = device.nodemap
	tl_stream_nodemap = device.tl_stream_nodemap

	#  Set up nodes -----------------------------------------------------------

	# Enable stream auto negotiate packet size
	tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True

	# Enable stream packet resend
	tl_stream_nodemap['StreamPacketResendEnable'].value = True

	# Disable automatic exposure and gain before setting an exposure time.
	# Automatic exposure and gain controls whether they are set manually or
	# automatically by the device. Setting automatic exposure and gain to
	# 'Off' stops the device from automatically updating the exposure time
	# while streaming.
	set_exposure_auto_to_off(nodemap)

	'''
	If 'SequencerMode' is on, turn it off so the sequencer becomes
		configurable through 'SequencerConfigurationMode'. Put sequencer in
		configuration mode. Sequencer configuration mode must be on while making
		changes to the sequencer sets.
	'''
	set_sequencer_configuration_mode_on(nodemap)

	# Set up sequencer sets ---------------------------------------------------

	# From device.nodemap['SequencerSetSelector'].max gives the maximum
	# of sequencer sets can be set on the device.

	# Make sure the example works with all devices.
	# Take the smaller value to set a long exposure time of some devices
	exposure_time_long = min(nodemap['ExposureTime'].max, 100000.0)

	print(f'{TAB1}Set up sequencer sets')
	sets_settings = [
		{
			'set_number': 0,
			'exposure_time': exposure_time_long / 40,
			'path_next_set': 1,
			'trigger_source': 'FrameStart'
		},
		{
			'set_number': 1,
			'exposure_time': exposure_time_long / 20,
			'path_next_set': 2,
			'trigger_source': 'FrameStart'
		},
		{
			'set_number': 2,
			'exposure_time': exposure_time_long,
			'path_next_set': 0,  # Means it goes back to the set in index 0
			'trigger_source': 'FrameStart'
		}
	]

	for set_settings in sets_settings:
		set_sequencer_set(nodemap, **set_settings)

	'''
	Sets the sequencer starting set to 0
	'''
	print(f'{TAB1}Set stream to start from sequencer set 0')
	nodemap['SequencerSetStart'].value = 0

	'''
	Turn off configuration mode
	'''
	print(f'{TAB1}Turn \'SequencerConfigurationMode\' Off')
	nodemap['SequencerConfigurationMode'].value = 'Off'
	print(f'{TAB2}\'SequencerConfigurationMode\' is '
		  f'{nodemap["SequencerConfigurationMode"].value} now')

	'''
	Turn on sequencer
		When sequencer mode is on and the device is streaming, it will follow the
		sequencer sets according to their saved settings.
	'''
	print(f'{TAB1}Turn \'SequencerMode\' On')
	nodemap['SequencerMode'].value = 'On'
	print(f'{TAB2}\'SequencerMode\' is {nodemap["SequencerMode"].value} now')

	# Acquire and Save image buffers ------------------------------------------

	'''
	This will start the stream, acquire a buffer in each set
		of the sequencer using its corresponding settings, save each buffer and
		then stop the stream.
	'''
	acquire_and_save_buffers(device)

	# Clean up ------------------------------------------------------------

	# Turn sequencer mode off so the device is set to the original settings
	print(f'{TAB1}Turn \'SequencerMode\' Off')
	nodemap['SequencerMode'].value = 'Off'
	print(f'{TAB2}\'SequencerMode\' is {nodemap["SequencerMode"].value} now')

	# Destroy all created devices. This call is optional and will
	# automatically be called for any remaining devices when the system module
	# is unloading.
	system.destroy_device()
	print(f'{TAB1}Destroyed all created devices')


if __name__ == '__main__':
	print('\nWARNING:\nTHIS EXAMPLE MIGHT CHANGE THE DEVICE(S) SETTINGS!')
	print('\nExample started\n')
	example_entry_point()
	print('\nExample finished successfully')
