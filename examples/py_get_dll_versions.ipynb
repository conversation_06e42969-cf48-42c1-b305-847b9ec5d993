{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pprint import pprint\n", "\n", "import arena_api\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get DLL Version: Introduction\n", ">    The example introduces the arena api library functions to get the\n", "    version of the package, dll, and binaries. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Get package version -----------------------------------------------------\n", "> - Method 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'arena_api.__version__ = {arena_api.__version__}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> - Method 2\n", "> > - the same can be obtained from 'version.py' module as well <br>\n", "Code: print(f'arena_api.version.__version__ = {arena_api.version.__version__}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Get dll versions -----------------------------------------------------\n", "> Arena_api is a wrapper built on top of ArenaC library, so the package\n", "uses 'ArenaCd_v140.dll' or libarenac.so. The ArenaC binary has different\n", "versions for different platforms. <br>\n", "Here is a way to know the minimum and maximum version of ArenaC supported by the current package. This could help in deciding whether to update arena_api or ArenaC."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('\\nsupported_dll_versions')\n", "pprint(arena_api.version.supported_dll_versions)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### For the current platform the key 'this_platform' key can be used"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('\\nsupported_dll_versions for this platform')\n", "pprint(arena_api.version.supported_dll_versions['this_platform'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Get loaded ArenaC and SaveC binaries versions ---------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('\\nloaded_binary_versions')\n", "pprint(arena_api.version.loaded_binary_versions)"]}], "metadata": {"language_info": {"name": "python"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}