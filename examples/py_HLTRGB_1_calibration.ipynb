{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2023, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "import numpy as np # pip3 install numpy\n", "import cv2  # pip3 install opencv-python\n", "from matplotlib import pyplot as plt # pip3 install matplotlib\n", "# pip3 install pillow\n", "from PIL import Image as PIL_Image\n", "from PIL import ImageTk as PIL_ImageTk\n", "# pip3 install tk / or 'sudo apt-get install python3-tk' for linux\n", "from tkinter import *\n", "from enum import Enum\n", "import math\n", "from ast import In\n", "\n", "from arena_api import enums\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.core.interactiveshell import InteractiveShell\n", "InteractiveShell.ast_node_interactivity = \"all\"\n", "\n", "# Adjust display settings to increase the output limits\n", "from IPython.display import display, HTML\n", "display(HTML(\"<style>.output_wrapper, .output {height:auto !important; max-height:10000px;}</style>\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Helios RGB: TritonCalibration\n", ">This example is part 1 of a 3-part example on color overlay over 3D images.\n", "\n", ">Before the data between the two cameras can be combined,\n", "we must first calibrate the lens on the Triton color camera to find its\n", "optical center and focal length (intrinsics), and lens distortion\n", "coefficients (pinhole model). We can achieve this by printing a target with\n", "a checkerboard pattern or you can download our calibration target here\n", "(15kB, PDF, 8.5 x 11 in)\n", "https:arenasdk.s3-us-west-2.amazonaws.com/LUCID_target_whiteCircles.pdf\n", "\n", ">Before calibrating the Triton camera you must focus its lens. Place the\n", "target at your application's working distance and focus the Triton's\n", "lens so that the calibration target is in focus. Calibrating the Triton\n", "camera requires grabbing several images of the calibration chart at\n", "different positions within the camera's field of view. At least 3 images\n", "are required but 4 to 8 images are typically used to get a better - quality\n", "calibration."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# image timeout\n", "TIMEOUT = 2000\n", "\n", "# number of calibration points to compare\n", "NUM_IMAGES = 10\n", "\n", "# calibration values file name\n", "FILE_NAME = 'tritoncalibration.yml'\n", "\n", "# time to sleep between images (in seconds)\n", "SLEEP_SECOND = 1\n", "\n", "TAB1 = \"  \"\n", "TAB2 = \"    \""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "HELPERS\n", "'''\n", "# Helper class\n", "class Settings:\n", "    class Pattern(Enum):\n", "        NOT_EXISTING = 1\n", "        CHESSBOARD = 2\n", "        CIRCLES_GRID = 3\n", "        ASYMMETRIC_CIRCLES_GRID = 4\n", "    \n", "    class InputType(Enum):\n", "        INVALID = 1\n", "        CAMERA = 2\n", "        VIDEO_FILE = 3\n", "        IMAGE_LIST = 4\n", "\n", "    def __init__(self):\n", "        self.good_input = False\n", "\n", "        self.board_size = {'width': 0, 'height': 0} # The size of the board -> number of items by width and height\n", "        self.calibration_pattern = Settings.Pattern.NOT_EXISTING # One of the Chessboard, circles, or asymmetric circle pattern\n", "        self.square_size = None # The size of a square in your defined unit (point, millimeter, etc).\n", "        self.nr_frames = None # The number of frames to use from the input for calibration\n", "        self.aspect_ratio = None # The aspect ratio\n", "        self.delay = 0 # In case of a video input\n", "        self.writePoints = False # Write detected feature points\n", "        self.calib_zero_target_dist = False # Assume zero tangential distortion\n", "        self.calib_fix_principal_point = False # Fix the principal piont at the center\n", "        self.flip_vertical = False # Flip the captured images around the horizontal axis\n", "        self.output_filename = None\n", "        self.show_undistorsed = None # Show undistorted images after calibration\n", "        self.input = None # The input ->\n", "\n", "        self.use_fisheye = False # Use fisheye camera model for calibration\n", "        self.fix_k1 = False # Fix K1 distortion coefficient\n", "        self.fix_k2 = False # Fix K2 distortion coefficient\n", "        self.fix_k3 = False # Fix K3 distortion coefficient\n", "        self.fix_k4 = False # Fix K4 distortion coefficient\n", "        self.fix_k5 = False # Fix K5 distortion coefficient\n", "\n", "        self.camera_ID = None\n", "        self.image_list = []\n", "        self.at_image_list = None\n", "        self.input_capture = None\n", "        self.input_type = None\n", "        self.good_input = False\n", "        self.flag = 0\n", "\n", "        self.pattern_to_use = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Devices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Wait for the user to connect a device before raising an exception\n", "'''\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "print(devices)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get applicable Triton devices"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Helper function to verify applicable device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_applicable_device(device):\n", "    '''\n", "    Return True if a device is a Triton camera, False otherwise\n", "    '''\n", "    model_name = device.nodemap.get_node('DeviceModelName').value\n", "    return 'TRI' in model_name and '-C' in model_name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get a list of applicable Triton devices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Return a list of applicable Triton devices\n", "'''\n", "applicable_devices = []\n", "\n", "for device in devices:\n", "    if is_applicable_device(device):\n", "        applicable_devices.append(device)\n", "\n", "if not len(applicable_devices):\n", "    raise Exception(f'No applicable device found! Please connect an Triton device and run '\n", "                    f'the example again.')\n", "\n", "print(f'Detected {len(applicable_devices)} applicable device(s)')\n", "applicable_devices"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Choose a Triton device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device = system.select_device(applicable_devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate and save calibration values"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get initial node values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap = device.nodemap\n", "acquisition_mode_initial = nodemap['AcquisitionMode'].value\n", "pixel_format_initial = nodemap['PixelFormat'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Set stream node values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Set device node values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set pixel format\n", "nodemap.get_node('PixelFormat').value = enums.PixelFormat.Mono8\n", "\n", "# Set acquisitiom mode\n", "nodemap.get_node('AcquisitionMode').value = 'Continuous'\n", "\n", "# Set buffer handling mode\n", "tl_stream_nodemap['StreamBufferHandlingMode'].value = 'NewestOnly'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Start Stream"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.start_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get sets of calibration points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Helper function to convert buffer to Mono8 format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def convert_buffer_to_Mono8(buffer):\n", "    '''\n", "    Convert bufferto Mono8 format\n", "    '''\n", "    if buffer.pixel_format == enums.PixelFormat.Mono8:\n", "        return buffer\n", "    print(f'Converting image buffer pixel format to Mono8 ')\n", "    return BufferFactory.convert(buffer, enums.PixelFormat.Mono8)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Helper function to find calibration points in a Mono8 image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_calibration_points(image_in_orig):\n", "    '''\n", "    Returns an array of calibration points found in the given image\n", "    '''\n", "\n", "    scaling = 1.0\n", "    image_in = image_in_orig\n", "    num_cols_orig = image_in_orig.shape[1] # width\n", "    num_rows_orig = image_in_orig.shape[0] # height\n", "\n", "    # Create blob detector ------------------------------------------------------------------\n", "    bright_params = cv2.SimpleBlobDetector_Params()\n", "    bright_params.filterByColor = True\n", "    bright_params.blobColor = 255 # White circles in the calibration target\n", "    bright_params.filterByCircularity = True\n", "    bright_params.minCircularity = 0.8\n", "\n", "    blob_detector = cv2.SimpleBlobDetector.create(bright_params)\n", "\n", "    # Find calibration points --------------------------------------------------------\n", "    pattern_size = (5, 4) # (pattern_per_row, pattern_per_column)\n", "    is_found, grid_centers = cv2.findCirclesGrid(image_in, pattern_size, flags=cv2.CALIB_CB_SYMMETRIC_GRID, blobDetector=blob_detector)\n", "\n", "    scaled_nrows = 2400.0\n", "\n", "    while not is_found and scaled_nrows >= 100:\n", "        scaled_nrows /= 2.0\n", "        scaling = float(num_rows_orig / scaled_nrows)\n", "\n", "        image_in = cv2.resize(image_in_orig, (int(num_cols_orig/scaling), int(num_rows_orig/scaling))) # cv2.resize(image, (width, height))\n", "\n", "        is_found, grid_centers = cv2.findCirclesGrid(image_in, pattern_size, flags=cv2.CALIB_CB_SYMMETRIC_GRID, blobDetector=blob_detector)\n", "\n", "    if is_found:\n", "        for center in grid_centers:\n", "            center[0][0] *= scaling\n", "            center[0][1] *= scaling\n", "\n", "    return is_found, grid_centers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Iterate until we get a sufficient number of images"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calibration_points = []\n", "image_size = [0] * 2\n", "attempts = 0\n", "images = 0\n", "grid_centers_found = 0\n", "successes = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["while successes < NUM_IMAGES:\n", "    try:\n", "        attempts += 1\n", "\n", "        # get image\n", "        buffer = device.get_buffer()\n", "        images += 1\n", "        \n", "        if buffer.is_incomplete:\n", "            raise RuntimeError('Incomplete image')\n", "\n", "        # Copy image buffer into an OpenCV matrix (a numpy array) \n", "        buffer_Mono8 = convert_buffer_to_Mono8(buffer)\n", "        buffer_bytes_per_pixel = int(len(buffer_Mono8.data)/(buffer_Mono8.width * buffer_Mono8.height))\n", "        image_matrix = np.asarray(buffer_Mono8.data, dtype=np.uint8)\n", "        image_matrix_reshaped = image_matrix.reshape(buffer_Mono8.height, buffer_Mono8.width, buffer_bytes_per_pixel)\n", "\n", "        image_size[0] = buffer_Mono8.height\n", "        image_size[1] = buffer_Mono8.width\n", "\n", "        device.requeue_buffer(buffer)\n", "\n", "        # Find calibration circles\n", "        points_found, grid_centers = find_calibration_points(image_matrix_reshaped)\n", "\n", "        grid_centers_found = 0 if not points_found else len(grid_centers)\n", "\n", "        # If 20 calibration points are found, success\n", "        if grid_centers_found == 20:\n", "            calibration_points.append(grid_centers)\n", "            successes += 1\n", "            print(f'{TAB1}Calibration image has been successful.')\n", "        else:\n", "            print(f'{TAB1}Found {grid_centers_found} circles. Please adjust the calibration target!')\n", "\n", "    except Exception as e:\n", "        print(f'Exception [{e}] happened. Retry')\n", "        \n", "    print(f'{TAB2}{attempts} attempts, '\n", "            f'{images} images, {grid_centers_found} circles found, '\n", "            f'{successes} calibration points')\n", "        \n", "    print(f'{TAB2}Please move the dot-pattern to a new position and press Enter to continue.')\n", "    input()\n", "\n", "    time.sleep(SLEEP_SECOND)\n", "\n", "print(f'{attempts} attempts, '\n", "      f'{images} images, {grid_centers_found} circles found, '\n", "      f'{successes} calibration points')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calculate camera matrix and distance coefficients"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Helper functions for the calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_reprojection_err(object_points, image_points, rvecs, tvecs, camera_matrix, dist_coeffs, fisheye):\n", "    '''\n", "    Compute reprojection error and its total average value\n", "    '''\n", "    \n", "    per_view_errors = [0] * len(object_points)\n", "    image_points2 = np.array([])\n", "    total_points = 0\n", "    total_error = 0\n", "\n", "    for i in range(len(object_points)):\n", "        if fisheye:\n", "            image_points2, _ = cv2.fisheye.projectPoints(object_points[i], image_points2, rvecs[i], tvecs[i], camera_matrix, dist_coeffs)\n", "        else:\n", "            image_points2, _ = cv2.projectPoints(object_points[i], rvecs[i], tvecs[i], camera_matrix, dist_coeffs)\n", "\n", "        error = cv2.norm(image_points[i], image_points2, cv2.NORM_L2)\n", "\n", "        n = len(object_points[i])\n", "        per_view_errors[i] = math.sqrt(error * error / n)\n", "        total_error += error * error\n", "        total_points += n\n", "    \n", "    return math.sqrt(total_error / total_points), per_view_errors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calc_board_corner_positions(board_size, square_size):\n", "    '''\n", "    Returns an array that indicates the position of the grids\n", "    '''\n", "    corners = []\n", "\n", "    for i in range(board_size['height']):\n", "        for j in range(board_size['width']):\n", "            corners.append(np.array((j*square_size, i*square_size, 0), dtype=np.float32))\n", "\n", "    return np.array(corners)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate(s: Settings, image_size, image_points):\n", "    '''\n", "    Calculate camera matrix, dist coefficients and total average error\n", "    '''\n", "    \n", "    # ! [fixed_aspect]\n", "    camera_matrix = np.eye(3, dtype=np.float64)\n", "    if s.flag & cv2.CALIB_FIX_ASPECT_RATIO:\n", "        camera_matrix[0, 0] = s.aspect_ratio\n", "\n", "    # ! [fixed_aspect]\n", "    np_shape = (4, 1) if s.use_fisheye else (8, 1)\n", "    dist_coeffs = np.zeros(np_shape, dtype=np.float64)\n", "\n", "    # Specify the size of the calibration board and distance between grid circles\n", "    s.board_size['width'] = 5\n", "    s.board_size['height'] = 4\n", "    s.square_size = 50 # distance between grids in mm\n", "\n", "    # Find the grid point positions and make the size of the object_points array the same as the image_points array\n", "    object_point = calc_board_corner_positions(s.board_size, s.square_size)\n", "    object_points = [object_point] * len(image_points)\n", "\n", "    # Ensure the size of elements in object_points and image_points matches\n", "    for i in range(len(object_points)):\n", "        if len(object_points[i]) != len(image_points[i]):\n", "            raise ValueError('object point and image point do not share shape')\n", "\n", "    # Find intrinsic and extrinsic camera parameters\n", "    if s.use_fisheye:\n", "        _, camera_matrix, dist_coeffs, _rvecs, _tvecs = cv2.fisheye.calibrate(object_points, image_points, image_size, camera_matrix, dist_coeffs) \n", "        rvecs = [_rvecs[i, :] for i in range(len(object_points))]\n", "        tvecs = [_tvecs[i, :] for i in range(len(object_points))]\n", "    else:\n", "        _, camera_matrix, dist_coeffs, rvecs, tvecs = cv2.calibrateCamera(object_points, image_points, image_size, camera_matrix, dist_coeffs)\n", "\n", "    success = cv2.checkRange(camera_matrix) and cv2.checkRange(dist_coeffs)\n", "\n", "    total_average_errors, _ = compute_reprojection_err(object_points, image_points, rvecs, tvecs, camera_matrix, dist_coeffs, s.use_fisheye)\n", "\n", "    # Find intrinsic and extrinsic camera parameters\n", "    return success, camera_matrix, dist_coeffs, total_average_errors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s = Settings()\n", "s.nr_frames = NUM_IMAGES\n", "s.input_type = Settings.InputType.IMAGE_LIST\n", "s.calibration_pattern = Settings.Pattern.CIRCLES_GRID\n", "s.flag = cv2.CALIB_RATIONAL_MODEL\n", "print(\"Calibration flag set to:\", s.flag)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calculation_succeeded, camera_matrix, dist_coeffs, total_average_error = calculate(s, image_size, np.array(calibration_points))\n", "    \n", "print(f'Calibration succeeded' if calculation_succeeded else f'Calibration failed')\n", "print(f'Calculated reprojection error is {total_average_error}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Save calibration information"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'Save camera matrix and distance coefficients to file {FILE_NAME}')    \n", "fs = cv2.FileStorage(FILE_NAME, cv2.FileStorage_WRITE)\n", "fs.write('cameraMatrix', camera_matrix)\n", "fs.write('distCoeffs', dist_coeffs)\n", "fs.release()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stop stream and destroy created device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.stop_stream()\n", "\n", "# Return nodes to their initial values\n", "nodemap.get_node('PixelFormat').value = pixel_format_initial\n", "nodemap.get_node('AcquisitionMode').value = acquisition_mode_initial\n", "\n", "system.destroy_device()\n", "print(f'Destroyed all created devices')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculated values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('cameraMatrix')\n", "print(camera_matrix)\n", "print('dist<PERSON>oeffs')\n", "print(dist_coeffs)"]}], "metadata": {"kernelspec": {"display_name": "ve_win_dev_py64", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.8"}}, "nbformat": 4, "nbformat_minor": 2}