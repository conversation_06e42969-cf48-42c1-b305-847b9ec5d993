{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import sys\n", "\n", "from arena_api.__future__.save import Writer\n", "from arena_api.enums import PixelFormat\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save: PLY\n", "> This example introduces the save capabilities of the save library in the PLY file format. It verifies that a 3D-capable Helios device is being used, shows the construction of an image parameters object and an image writer, and saves a single image in the PLY file format."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "pixel_format = PixelFormat.BGR8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### validate if Scan3dCoordinateSelector node exists.\n", "> If not, it is (probably) not a Helios Camera running the example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "\tscan_3d_operating_mode_node = device.nodemap['Scan3dOperatingMode'].value\n", "except KeyError:\n", "\tprint(f'Scan3dCoordinateSelector node is not found. ' \\\n", "\t\tf'Please make sure that Helios device is used for the example.\\n')\n", "\tquit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap = device.tl_stream_nodemap\n", "print(f'Device used in the example:\\n\\t{device}')\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### choose a 3d pixel format. here unsigned pixelformat is chosen. the\n", ">signed pixelformat version of this would have the same name with an 's' at the end"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.nodemap['PixelFormat'].value = PixelFormat.Coord3D_ABC16"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with device.start_stream():\n", "\tprint('Stream started')\n", "\n", "\tbuffer = device.get_buffer()\n", "\tprint(f'Image buffer received')\n", "\n", "\t# create an image writer\n", "\t# The writer, optionally, can take width, height, and bits per pixel\n", "\t# of the image(s) it would save. if these arguments are not passed\n", "\t# at run time, the first buffer passed to the Writer.save()\n", "\t# function will configure the writer to the arguments buffer's width,\n", "\t# height, and bits per pixel\n", "\twriter = Writer.from_buffer(buffer)\n", "\n", "\t# save function\n", "\t# buffer :\n", "\t#   buffer to save.\n", "\t# pattern :\n", "\t#   default name for the image is 'image_<count>.jpg' where count\n", "\t#   is a pre-defined tag that gets updated every time a buffer image\n", "\t#   is saved. More custom tags can be added using\n", "\t#   Writer.register_tag() function\n", "\t# kwargs (optional args) ignored if not an .ply image:\n", "\t#   - 'filter_points' default is True.\n", "\t#       Filters NaN points (A = B = C = -32,678)\n", "\t#   - 'is_signed' default is False.\n", "\t#       If pixel format is signed for example PixelFormat.Coord3D_A16s\n", "\t#       then this arg must be passed to the save function else\n", "\t#       the results would not be correct\n", "\t#   - 'scale' default is 0.25.\n", "\t#   - 'offset_a', 'offset_b' and 'offset_c' default to 0.00\n", "\twriter.save(buffer, 'I_AM_A_3D_BECAUSE_OF_MY_EXTENSION.ply')\n", "\n", "\tprint(f'Image saved {writer.saved_images[-1]}')\n", "\n", "\tdevice.requeue_buffer(buffer)\n", "\tprint(f'Image buffer requeued')\n", "\n", "\t# read the point cloud then display it using one of many packages on\n", "\t# pypi. For example:\n", "\t#   import open3d\n", "\t#   pc_file = open3d.io.read_point_cloud(writer.saved_images[-1])\n", "\t#   open3d.visualization.draw_geometries([pc_file])\n", "\t#\n", "\t# Note:\n", "\t# open3d package does not support some\n", "\t# os/architerctures (Raspbian for exapmle)\n", "\n", "# device.stop_stream() is automatically called at the end of the\n", "# context manger scope"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This function call with no arguments will destroy all of the\n", "# created devices. Having this call here is optional, if it is not\n", "# here it will be called automatically when the system module is unloading.\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}