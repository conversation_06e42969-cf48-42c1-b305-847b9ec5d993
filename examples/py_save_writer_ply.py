
# -----------------------------------------------------------------------------
# Copyright (c) 2024, Lucid Vision Labs, Inc.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
# OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
# NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
# BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
# ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.
# -----------------------------------------------------------------------------

import sys

from arena_api.__future__.save import Writer
from arena_api.enums import PixelFormat
from arena_api.system import system

'''
Save: PLY
	This example introduces the save capabilities of the save library in the PLY
	file format. It verifies that a 3D-capable Helios device is being used, shows
	the construction of an image parameters object and an image writer, and saves
	a single image in the PLY file format.
'''
TAB1 = "  "
TAB2 = "    "

def validate_device(device):

	# validate if Scan3dCoordinateSelector node exists.
	# If not, it is (probably) not a Helios Camera running the example
	try:
		scan_3d_operating_mode_node = device. \
			nodemap['Scan3dOperatingMode'].value
	except KeyError:
		print(f'{TAB1}Scan3dCoordinateSelector node is not found. ' \
			f'{TAB1}Please make sure that a Helios device is used for the example.\n')
		sys.exit()


def example_entry_point():

	# Get connected devices ---------------------------------------------------

	# create_device function with no arguments would create a list of
	# device objects from all connected devices
	devices = system.create_device()
	if not len(devices):
		raise Exception(f'{TAB1}No device found!\n'
						f'{TAB1}Please connect a device and run the example again.')
	print(f'{TAB1}Created {len(devices)} device(s)')

	device = system.select_device(devices)

	validate_device(device)

	tl_stream_nodemap = device.tl_stream_nodemap

	# Enable stream auto negotiate packet size
	tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True

	# Enable stream packet resend
	tl_stream_nodemap['StreamPacketResendEnable'].value = True

	# choose a 3d pixel format. here unsigned pixelformat is chosen. the
	# signed pixelformat version of this would have the same name with
	# an 's' at the end
	device.nodemap['PixelFormat'].value = PixelFormat.Coord3D_ABC16

	with device.start_stream():
		print(f'{TAB1}Stream started')

		buffer = device.get_buffer()
		print(f'{TAB1}Image buffer received')

		# create an image writer
		# When saving as .ply file, the writer optionally can take width, 
		# height, and bits per pixel of the image(s) it would save. 
		# if these arguments are not passed at run time, the first buffer passed 
		# to the Writer.save() function will configure the writer to the arguments 
		# buffer's width, height, and bits per pixel
		writer = Writer.from_buffer(buffer)
		# writer.pattern = 'images/image_<count>.jpg'

		# save function for .ply file
		# buffer :
		#   buffer to save.
		# pattern :
		#   default name for the image is 'image_<count>.jpg' where count
		#   is a pre-defined tag that gets updated every time a buffer image
		#   is saved. More custom tags can be added using
		#   Writer.register_tag() function
		# kwargs (optional args) ignored if not applicable to an .ply image:
		#   - 'filter_points' default is True.
		#       Filters NaN points (A = B = C = -32,678)
		#   - 'is_signed' default is False.
		#       If pixel format is signed for example PixelFormat.Coord3D_A16s
		#       then this arg must be passed to the save function else
		#       the results would not be correct
		#   - 'scale' default is 0.25.
		#   - 'offset_a', 'offset_b' and 'offset_c' default to 0.00
		writer.save(buffer, 'images/py_save_writer_ply/I_AM_A_3D_BECAUSE_OF_MY_EXTENSION_<count>.ply')

		print(f'{TAB1}Image saved {writer.saved_images[-1]}')

		device.requeue_buffer(buffer)
		print(f'{TAB1}Image buffer requeued')

		# read the point cloud then display it using one of many packages on
		# pypi. For example:
		#   import open3d
		#   pc_file = open3d.io.read_point_cloud(writer.saved_images[-1])
		#   open3d.visualization.draw_geometries([pc_file])
		#
		# Note:
		# open3d package does not support some
		# os/architerctures (Raspbian for exapmle)

	# device.stop_stream() is automatically called at the end of the
	# context manger scope

	# clean up ----------------------------------------------------------------

	# This function call with no arguments will destroy all of the
	# created devices. Having this call here is optional, if it is not
	# here it will be called automatically when the system module is unloading.
	system.destroy_device()
	print(f'{TAB1}Destroyed all created devices')


if __name__ == '__main__':
	print('\nWARNING:\nTHIS EXAMPLE MIGHT CHANGE THE DEVICE(S) SETTINGS!\n')
	print('Example started\n')
	example_entry_point()
	print('\nExample finished successfully')
