{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from arena_api.system import system\n", "from arena_api.callback import callback, callback_function\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Callbacks: On Device Disconnected\n", ">    This example demonstrates how to register a callback to get\n", "    notified when a device has disconnected. At first this example\n", "    will enumerate devices then if there is any device found it will\n", "    register a disconnect callback for a discovered device.\n", "    Next the program will wait until a user inputs an exit\n", "    command. While this example waits for input, feel free to\n", "    disconnect the device. When the device is disconnected the callback\n", "    will be triggered and it will print out info of the device\n", "    that was removed by using print_disconnected_device_info function."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Must have the decorator on the callback function\n", "> system.on_device_disconnected requires device as its first parameter<br>\n", "This function is triggered on device disconnet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@callback_function.system.on_device_disconnected\n", "def disconnect_callback_function(device):\n", "    print(f'\\nDevice was disconnected:\\n\\t{device}\\n')\n", "    print(\"Press <ENTER> to continue\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Register the callback on the device\n", ">   Note: if you do not access device information\n", "    at some point before triggering the callback,\n", "    then the callback function will time out."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Register callback function with device {device}\")\n", "\n", "handle = callback.register(system, disconnect_callback_function, watched_device = device)\n", "print(\"Press <ENTER> to exit\")\n", "input()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Deregister each handle in the handle list\n", "> Must be called before device is destroyed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["callback.deregister(handle)\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}