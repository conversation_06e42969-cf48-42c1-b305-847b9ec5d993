{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from arena_api.system import system\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Pixel Correction\n", "> This example introduces the basics of pixel correction. A single arbitrary pixel is chosen and added to the device's pixel correction list. These changes are then saved to the camera before being removed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Pixel values\n", "> This example does not search for pixels to correct, but instead just uses arbitrary pixels to demonstrate what would be done if a bad pixel were found."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PIXEL_X = 333\n", "PIXEL_Y = 444"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap = device.nodemap\n", "nodes = nodemap.get_node(['DefectCorrectionEnable','DefectCorrectionCount',\n", "'DefectCorrectionPositionX','DefectCorrectionPositionY',\n", "'DefectCorrectionGetNewDefect', 'DefectCorrectionIndex', \n", "'DefectCorrectionApply', 'DefectCorrectionSave', 'DefectCorrectionRemove'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Enable pixel correction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['DefectCorrectionEnable'] = True\n", "\n", "pixel_correction_count = nodes['DefectCorrectionCount'].value\n", "\n", "print(\"Initial pixel correction count: \" + str(pixel_correction_count))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Add new pixel to end of correction list and get its index\n", ">Getting a new defect automatically updates the pixel correction index."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['DefectCorrectionGetNewDefect'].execute()\n", "\n", "pixel_correction_updated_index = nodes['DefectCorrectionIndex'].value\n", "\n", "print(\"Pixel correction updated index: \" + str(pixel_correction_updated_index))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set the position of pixels to be corrected"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['DefectCorrectionPositionX'] = PIXEL_X\n", "\n", "nodes['DefectCorrectionPositionY'] = PIXEL_Y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Apply correction list\n", "> Once the pixel corrections are applied, they will take effect immediately. However, by power-cycling the camera, the defect list will go back to its default values."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['DefectCorrectionApply'].execute()\n", "print(\"Applied correction list\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Save correction to camera\n", ">Optionally write the correction to the camera to make the changes persistent (the camera can still be set to default by executing the DefectCorrectionRestoreDefault node)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['DefectCorrectionSave'].execute()\n", "print(\"Saved correction to camera\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Remove all pixels set through this example\n", "> The index is updated so that there are no empty indices after removal regardless of position"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["i = pixel_correction_updated_index\n", "while i >= pixel_correction_count:\n", "    nodes['DefectCorrectionIndex'] = i;\n", "\n", "    x = nodes['DefectCorrectionPositionX']\n", "    y = nodes['DefectCorrectionPositionY']\n", "    print(\"Pixel index: \" + str(nodes['DefectCorrectionIndex'])\n", "    + \" (x: \" + str(x) + \", y:\" + str(y) + \")\")\n", "\n", "    if x == PIXEL_X and y == PIXEL_Y:\n", "        print(\"Pixels match. Remove Pixel\")\n", "        nodes['DefectCorrectionRemove'].execute()\n", "        print(\"Pixel Removed\")\n", "    else:\n", "        print(\"Does not match\")\n", "    \n", "    i = i-1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.11"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}