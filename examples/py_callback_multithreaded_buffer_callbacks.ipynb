{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import threading\n", "from datetime import datetime\n", "from arena_api.callback import callback, callback_function\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Callback: Multithreaded Image Callbacks\n", ">    This example demonstrates configuring a callback within a thread.\n", "    Once the thread has been launched, each new image is acquired and\n", "    the callback is triggered to retrieve the image's frame ID. Once\n", "    the callback function exits, the image buffer is requeued. After\n", "    all images have been acquired, the thread exits and memory is\n", "    cleaned up."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "num_buffers = 25"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Waits for the user to connect a device \n", "before raising an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Must have the decorator on the callback function\n", "> device.on_buffer decorator requires buffer as its first parameter<br>\n", "Buffer should only be accessed by a single thread at a time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@callback_function.device.on_buffer\n", "def print_buffer(buffer, *args, **kwargs):\n", "    \n", "    with threading.Lock():\n", "        print(f'{TAB2}{TAB1}Buffer callback triggered'\n", "              f'(frame id is {buffer.frame_id})')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Grabbing buffers from device will trigger the callback where the buffer information can then be safely printed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_multiple_image_buffers(device):\n", "    print(f\"\\n{TAB1}Stream Started\")\n", "    device.start_stream(1)\n", "\n", "    print(f'{TAB1}Getting {num_buffers} buffer(s)')\n", "\n", "    for i in range(num_buffers):\n", "        \n", "        # As buffer is retreived, the callback is triggered\n", "        print(f'{TAB2}Buffer Retrieved')\n", "        buffer = device.get_buffer()\n", "\n", "        device.requeue_buffer(buffer)\n", "\n", "    device.stop_stream()\n", "    print(f\"{TAB1}Stream Stopped\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates callback on buffer: multithreading\n", "> - Configure pre stream nodes for all devices\n", "> - Initialize handle and threads on get_multiple_image_buffers\n", "    for all devices\n", "> - Start all threads in the list\n", "> - Join all threads in the list\n", "> - Joining threads starts the get_multiple_image_buffers function,\n", "    which in turn triggers callbacks, after stream is started\n", "    and buffers are received\n", "> - Der<PERSON><PERSON> all handles in the list before destroying device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["thread_list = []\n", "handle_list = []\n", "for device in devices:\n", "    \"\"\"\n", "    Setup stream values\n", "    \"\"\"\n", "    tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "    tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "    tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Register callback handles and initialize threads"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for device in devices:\n", "\n", "    handle = callback.register(device, print_buffer)\n", "\n", "    print(f'{TAB1}Registered \\'{print_buffer.__name__}\\' function '\n", "            f'on {device}\\'')\n", "\n", "    thread = threading.Thread(target=get_multiple_image_buffers,\n", "                                args=(device,))\n", "\n", "    handle_list.append(handle)\n", "    thread_list.append(thread)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Start and join all threads in the thread list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for thread in thread_list:\n", "    thread.start()\n", "\n", "for thread in thread_list:\n", "    thread.join()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Deregister each handle in the handle list\n", "> Must be called before device is destroyed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for handle in handle_list:\n", "    callback.deregister(handle)\n", "\n", "system.destroy_device(devices)"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}