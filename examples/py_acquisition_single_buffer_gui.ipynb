{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "import numpy as np # pip3 install numpy\n", "import cv2  # pip3 install opencv-python\n", "from matplotlib import pyplot as plt # pip3 install matplotlib\n", "# pip3 install pillow\n", "from PIL import Image as PIL_Image\n", "from PIL import ImageTk as PIL_ImageTk\n", "\n", "# pip3 install tk / or 'sudo apt-get install python3-tk' for linux\n", "# tk has troubles with latest python releases\n", "# from tkinter import *\n", "\n", "from arena_api import enums\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.socket import Socket"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: Introduction\n", ">\tThis example introduces the basics of image acquisition. This\n", "\tincludes setting image acquisition and buffer handling modes,\n", "\tsetting the device to automatically negotiate packet size, and\n", "\tsetting the stream packet resend node before starting the image\n", "\tstream. The example then acquires an image by grabbing and\n", "\trequeuing a single buffer and retrieving its data, before stopping\n", "\tthe stream. It then displays the image using Matplotlib, OpenCV and Tkinter."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Waits for the user to connect a device before raising an exception if it fails\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "devices = None\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "nodemap = device.nodemap\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Store initial values\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes = device.nodemap.get_node(['Width', 'Height', 'PixelFormat'])\n", "width_initial = nodes['Width'].value\n", "height_initial = nodes['Height'].value\n", "pixel_format_initial = nodes['PixelFormat'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set features before streaming.-------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get stream nodemap\n", "stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "# Set width and height to their max values\n", "print('Setting \\'Width\\' and \\'Height\\' Nodes value to their max values')\n", "nodes['Width'].value = nodes['Width'].max\n", "nodes['Height'].value = nodes['Height'].max\n", "\n", "# Setting pixel format \n", "new_pixel_format = 'Mono8'\n", "print(f'Setting \\'PixelFormat\\' to \\'{new_pixel_format}\\'')\n", "nodes['PixelFormat'].value = new_pixel_format"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Convert to BGR8 format\n", "> Allows images to be easily displayed using matplotlib and opencv-python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def convert_buffer_to_BGR8(buffer):\n", "\n", "    if (buffer.pixel_format == enums.PixelFormat.BGR8):\n", "        return buffer\n", "    print('Converting image buffer pixel format to BGR8 ')\n", "    return BufferFactory.convert(buffer, enums.PixelFormat.BGR8)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Intialize the command socket"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["command_socket = Socket()\n", "command_socket.open_sender()\n", "command_socket.add_destination(12345)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Initialize the image socket"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image_socket = Socket()\n", "image_socket.open_sender()\n", "image_socket.add_destination(54000)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates Acquisition\n", "> - sets acquisition mode\n", "> - sets buffer handling mode\n", "> - enables auto negotiate packet size\n", "> - enables packet resend\n", "> - starts the stream\n", "> - gets a number of images\n", "> - prints information from images\n", "> - requeues buffers\n", "> - stops the stream"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Grab images -------------------------------------------------------------\n", "> - Starting the stream allocates buffers, which can be passed in as\n", " an argument (default: 10), and begins filling them with data.\n", " Buffers must later be requeued to avoid memory leaks.<br>\n", "> - 'device.get_buffer()' with no arguments returns only one buffer\n", "     the buffer is in the rgb layout\n", "> - 'device.requeue_buffer()' takes a buffer or many buffers in a list or tuple"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true, "tags": []}, "outputs": [], "source": ["buffer_BGR8 = None\n", "np_array_reshaped = None\n", "\n", "with device.start_stream(1):\n", "    print(f'Stream started with 1 buffers')\n", "    \n", "    print('\\tGet one buffer')\n", "    buffer = device.get_buffer()\n", "\n", "    # Print some info about the image in the buffer\n", "    print(f'\\t\\tbuffer received   | '\n", "        f'Width = {buffer.width} pxl, '\n", "        f'Height = {buffer.height} pxl, '\n", "        f'Pixel Format = {buffer.pixel_format.name}')\n", "\n", "    # Converting to BGR8 format\n", "    print('\\tConverting to BGR8 format')\n", "    buffer_BGR8 = convert_buffer_to_BGR8(buffer)\n", "\n", "    # Requeue to release buffer memory\n", "    print('Requeuing device buffer')\n", "    device.requeue_buffer(buffer)\n", "\n", "    # Get a copy so it can be used after the buffer is requeued\n", "    print('\\tConvert image buffer to a numpy array')\n", "    buffer_bytes_per_pixel = int(len(buffer_BGR8.data)/(buffer_BGR8.width * buffer_BGR8.height))\n", "    np_array = np.asarray(buffer_BGR8.data, dtype=np.uint8)\n", "    np_array_reshaped = np_array.reshape(buffer_BGR8.height, buffer_BGR8.width, buffer_bytes_per_pixel)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Display image in the correct format for matplot\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np_array_shaped_rgb = cv2.cvtColor(np_array_reshaped, cv2.COLOR_BGR2RGB)\n", "plt.imshow(np_array_shaped_rgb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Display image using ArenaViewMP"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Blow is the code for diplaying using opencv*\n", "\n", "# cv2.imshow(\"window_title\", np_array_reshaped)\n", "\n", "# # wait for user key before closing it\n", "# cv2.<PERSON><PERSON><PERSON>(0)\n", "\n", "# cv2.destroyAllWindows()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Send the inital command message to turn on SocketView button"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_message = 'option update socket_view --value=1 --async'\n", "command_socket.send_message(start_message)\n", "print(f'message: \"{start_message}\" sent successfully!\\n')\n", "\n", "image_socket.send_singleImage(buffer_BGR8)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Display image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image_socket.send_singleImage(buffer_BGR8)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Open image in a sperate window using tkinter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print('Creating \\'PIL.Image\\' instance from Numpy array')\n", "# tk has troubles with latest python releases\n", "\n", "# pil_image = PIL_Image.fromarray(np_array_reshaped)\n", "\n", "# print('Creating a Tkinter readable image from \\'PIL.Image\\' instance')\n", "# root = Tk()\n", "# pil_imagetk_photoimage = PIL_ImageTk.PhotoImage(pil_image)\n", "\n", "# label = Label(root, image=pil_imagetk_photoimage)\n", "# label.pack()\n", "# root.mainloop()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### The buffer factory gives a converted copy of the device buffer, so destroy the image copy to prevent memory leaks\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BufferFactory.destroy(buffer_BGR8)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Turn off the socket_view Plugin on ArenaViewMP"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["end_message = 'option update socket_view --value=0'\n", "command_socket.send_message(end_message)\n", "print(f'message: \"{end_message}\" sent successfully!\\n')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Restore initial values to the device.\n", "> - Stop stream.\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Stream stopped\")\n", "device.stop_stream()\n", "\n", "print(\"Return nodes to initial values\")\n", "nodes['Width'].value = width_initial\n", "nodes['Height'].value = height_initial\n", "nodes['PixelFormat'].value = pixel_format_initial\n", "\n", "image_socket.close_sender()\n", "command_socket.close_sender()\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}