{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from arena_api.system import system\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Enumeration MAC: Introduction\n", ">    This example enumerates a device and obtains its MAC address.\n", "    It then verifies it against the MAC address stored in the node map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Demonstrates creation of connected devices\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Dynamically get the MAC address of a device that exists so the example passes\n", "> This function can be avoided if\n", "\tthe user wants to hard code the MAC address instead"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["address = \"\"\n", "\n", "device_infos = system.device_infos\n", "if len(device_infos) == 0:\n", "\traise BaseException('No device is found!')\n", "else:\n", "\taddress = device_infos[0]['mac']\n", "print(f'\\'{address}\\' is the MAC address to find')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Find a specific mac address in device infos then return that device info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["found_device = False\n", "\n", "selected_device_info = {}\n", "\n", "for device_info in system.device_infos:\n", "\tif device_info['mac'] == address:\n", "\t\tfound_device = True\n", "\t\tselected_device_info = device_info\n", "\t\tprint(f'\\'{address}\\' device was found')\n", "if not found_device:\n", "\traise BaseException(f'No device with MAC Address '\n", "\t\t\t\t\t\tf'\\'{mac_to_find}\\' was found!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Creates selected device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Creating device from device info')\n", "devices_list = system.create_device(device_infos=selected_device_info)\n", "device = system.select_device(devices_list)\n", "print(f'Device created: {device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Simple verification\n", "> Read device's MAC address from the device to verify"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device_mac_int = device.nodemap.get_node('GevMACAddress').value\n", "\n", "# Converts int value to MAC address of format XX:XX:XX:XX:XX:XX\n", "\n", "hex_mac = f'{device_mac_int:012x}'\n", "device_mac = ':'.join(hex_mac[i:i+2] for i in range(0, len(hex_mac), 2))\n", "\n", "# Verify\n", "\n", "print(f'Device MAC Address = {device_mac}')\n", "if device_mac != address:\n", "\traise BaseException('The wrong device has been created')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### System is closed when module is closed.\n", ">    Explicitly closing it would prevent it from being reopened, \n", "    as it is only supposed to be opened with \"from arena_api.system import system\"\n"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}