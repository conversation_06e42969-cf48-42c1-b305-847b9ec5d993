{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from datetime import datetime\n", "\n", "from arena_api.callback import callback, callback_function\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["#### Callback: Image Callbacks\n", ">    This example demonstrates configuring an image callback for a device. Once\n", "    a callback is registered and the device is streaming, the user-implemented\n", "    print_buffer function will be called. print_buffer will receive the buffer\n", "    with the image and will display the frame id and timestamp of the\n", "    image before returning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Waits for the user to connect a device before raising an exception\n", "if it fails\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Must have the decorator on the callback function\n", "> device.on_buffer decorator requires buffer as its first parameter<br>\n", "Buffer should only be accessed by a single thread at a time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@callback_function.device.on_buffer\n", "def print_buffer(buffer, *args, **kwargs):\n", "    \n", "    now = kwargs['now_func']()\n", "    print(f'{TAB2}Buffer: [{buffer.width} X {buffer.height}] pixels, '\n", "          f'TimeStamp: [{now}]')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates callback on buffer\n", "> - Connect device, setup stream\n", "> - Register the callback function using callback.register\n", "> - Start the stream\n", "> - As the buffers on the device get filled, the callback is triggered\n", "> - Stop stream and deregister the callback before destroying the device"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Setup stream values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Register the callback on the device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["handle = callback.register(\n", "    device, print_buffer, now_func=datetime.now)\n", "print(f'{TAB1}Registered \\'{print_buffer.__name__}\\' function '\n", "        f'on {device}\\'')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### As stream starts it will grab buffers and pass them to the callback"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "device.start_stream(1)\n", "print(f'{TAB1}Stream started')\n", "\n", "time.sleep(5)\n", "\n", "device.stop_stream()\n", "print(f'{TAB1}Stream stopped')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Deregister each handle in the handle list\n", "> Must be called before device is destroyed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["callback.deregister(handle)\n", "\n", "system.destroy_device(device)"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}