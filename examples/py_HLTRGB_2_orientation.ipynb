{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2023, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import ctypes\n", "from os.path import exists\n", "\n", "import numpy as np # pip3 install numpy\n", "import cv2  # pip3 install opencv-python\n", "# pip3 install tk / or 'sudo apt-get install python3-tk' for linux\n", "from tkinter import *\n", "\n", "from arena_api import enums\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.enums import PixelFormat"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Helios RGB: Orientation\n", ">This example is part 2 of a 3-part example on color overlay over 3D images.\n", "    Color data can be overlaid over 3D images by reading the \n", "    3D points ABC (XYZ) from the Helios and projecting them onto\n", "    the Triton color (RGB) camera directly. This requires first solving for the\n", "    orientation of the Helios coordinate system relative to the Triton’s\n", "    native coordinate space (rotation and translation wise). This step can be\n", "    achieved by using the open function solvePnP(). Solving for orientation of\n", "    the Helios relative to the Triton requires a single image of the\n", "    calibration target from each camera. Place the calibration target near the\n", "    center of both cameras field of view and at an appropriate distance from\n", "    the cameras. Make sure the calibration target is placed at the same\n", "    distance you will be imaging in your application. Make sure not to move the\n", "    calibration target or cameras in between grabbing the Helios image and\n", "    grabbing the Triton image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# image timeout\n", "TIMEOUT = 2000\n", "\n", "# calibration values file name\n", "FILE_NAME_IN = 'tritoncalibration.yml'\n", "\n", "# orientation values file name\n", "FILE_NAME_OUT = \"orientation.yml\"\n", "\n", "TRITON = 'Triton'\n", "HELIOS2 = 'Helios2'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check for input file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not exists(FILE_NAME_IN):\n", "    print(f'File \\'{FILE_NAME_IN}\\' not found. Please run example \\'py_HLTRGB_1_calibration\\' prior to this one.')\n", "    raise FileNotFoundError('Input file not found')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Devices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Wait for the user to connect a device before raising an exception\n", "'''\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "print(devices)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get a Triton and Helios2 device"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Helper functions to verify <PERSON>ton and Helios device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_applicable_device_triton(device):\n", "    '''\n", "    Return True if a device is a Triton camera, False otherwise\n", "    '''\n", "    model_name = device.nodemap.get_node('DeviceModelName').value\n", "    return \"TRI\" in model_name and \"-C\" in model_name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_applicable_device_helios2(device):\n", "    '''\n", "    Return True if a device is a Helios2 camera, False otherwise\n", "    '''\n", "    model_name = device.nodemap.get_node('DeviceModelName').value\n", "    return \"HLT\" in model_name or \"HT\" in model_name "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Helper function to get a list of applicable Triton or Helios2 devices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_applicable_devices(devices, type):\n", "    '''\n", "    Return a list of applicable Triton devices\n", "    '''\n", "    applicable_devices = []\n", "\n", "    for device in devices:\n", "        if type == TRITON and is_applicable_device_triton(device):\n", "            applicable_devices.append(device)\n", "        elif type == HELIOS2 and is_applicable_device_helios2(device):\n", "            applicable_devices.append(device)\n", "    \n", "    if not len(applicable_devices):\n", "        raise Exception(f'No applicable device found! Please connect an Triton and Helios2 device and run '\n", "                        f'the example again.')\n", "\n", "    print(f'Detected {len(applicable_devices)} applicable {type} device(s)')\n", "    return applicable_devices"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get a list of applicable Triton or Helios2 devices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["applicable_devices_triton = get_applicable_devices(devices, TRITON)\n", "applicable_devices_helios2 = get_applicable_devices(devices, HELIOS2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Select a Triton and Helios2 device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device_triton = system.select_device(applicable_devices_triton)\n", "device_helios2 = system.select_device(applicable_devices_helios2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate and save orientation values"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get initial node values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [" # Get node values that will be changed in order to return their values at the end of the example\n", "nodemap_triton = device_triton.nodemap\n", "nodemap_helios2 = device_helios2.nodemap\n", "pixel_format_initial_triton = nodemap_triton.get_node(\"PixelFormat\").value\n", "pixel_format_initial_helios2 = nodemap_helios2.get_node(\"PixelFormat\").value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Read in camera matrix and distance coefficient"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'Read camera matrix and distance coefficients from file {FILE_NAME_IN}')\n", "fs = cv2.FileStorage(FILE_NAME_IN, cv2.FileStorage_READ)\n", "camera_matrix = fs.getNode('cameraMatrix').mat()\n", "dist_coeffs = fs.getNode('distCoeffs').mat()\n", "fs.release()\n", "\n", "print('cameramatrix')\n", "print(camera_matrix)\n", "print('dist<PERSON>oeffs')\n", "print(dist_coeffs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get an image from triton\n", "as `image_matrix_TRI`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Helper function to convert beffer to Mono8 format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def convert_buffer_to_Mono8(buffer):\n", "    '''\n", "    Convert to Mono8 format\n", "    '''\n", "    if buffer.pixel_format == enums.PixelFormat.Mono8:\n", "        return buffer\n", "    print(f'Converting image buffer pixel format to Mono8 ')\n", "    return BufferFactory.convert(buffer, enums.PixelFormat.Mono8)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set nodes --------------------------------------------------------------\n", "# - pixelformat to RGB8\n", "# - 3D operating mode\n", "nodemap = device_triton.nodemap\n", "nodemap.get_node('PixelFormat').value = PixelFormat.RGB8\n", "\n", "# Set device stream nodemap --------------------------------------------\n", "tl_stream_nodemap = device_triton.tl_stream_nodemap\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "# Get image ---------------------------------------------------\n", "device_triton.start_stream()\n", "buffer = device_triton.get_buffer()\n", "buffer_Mono8 = convert_buffer_to_Mono8(buffer)\n", "buffer_bytes_per_pixel = int(len(buffer_Mono8.data)/(buffer_Mono8.width * buffer_Mono8.height))\n", "image_matrix = np.asarray(buffer_Mono8.data, dtype=np.uint8)\n", "image_matrix_TRI = image_matrix.reshape(buffer_Mono8.height, buffer_Mono8.width, buffer_bytes_per_pixel)\n", "\n", "# Stop stream -------------------------------------------------\n", "device_triton.requeue_buffer(buffer)\n", "device_triton.stop_stream()\n", "\n", "image_matrix_TRI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get image from Helios2\n", "as `image_matrix_HLT_intensity`, `image_matrix_HLT_XYZ`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Helper function to convert buffer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def convert_buffer_to_Coord3D_ABCY16(buffer):\n", "    '''\n", "    Convert to Coord3DD_ABCY16 format\n", "    '''\n", "    if buffer.pixel_format == enums.PixelFormat.Coord3D_ABCY16:\n", "        return buffer\n", "    print(f'Converting image buffer pixel format to Coord3D_ABCY16')\n", "    return BufferFactory.convert(buffer, enums.PixelFormat.Coord3D_ABCY16)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set device stream nodemap --------------------------------------------\n", "tl_stream_nodemap = device_helios2.tl_stream_nodemap\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "# Set nodes --------------------------------------------------------------\n", "# - pixelformat to Coord3D_ABCY16\n", "# - 3D operating mode\n", "nodemap = device_helios2.nodemap\n", "nodemap.get_node('PixelFormat').value = PixelFormat.Coord3D_ABCY16\n", "\n", "# Get node values ---------------------------------------------------------\n", "# Read the scale factor and offsets to convert from unsigned 16-bit values \n", "# in the Coord3D_ABCY16 pixel format to coordinates in mm\n", "\n", "    # \"Coord3D_ABCY16s\" and \"Coord3D_ABCY16\" pixelformats have 4\n", "    # channels per pixel. Each channel is 16 bits and they represent:\n", "    #   - x position\n", "    #   - y postion\n", "    #   - z postion\n", "    #   - intensity\n", "\n", "# get the coordinate scale in order to convert x, y and z values to millimeters as\n", "# well as the offset for x and y to correctly adjust values when in an\n", "# unsigned pixel format\n", "print(f'Get xyz coordinate scales and offsets from nodemap')\n", "xyz_scale_mm = nodemap[\"Scan3dCoordinateScale\"].value # Coordinate scale to convert x, y, and z values to mm\n", "nodemap[\"Scan3dCoordinateSelector\"].value = \"CoordinateA\"\n", "x_offset_mm = nodemap[\"Scan3dCoordinateOffset\"].value # offset for x to adjust values when in unsigned pixel format\n", "nodemap[\"Scan3dCoordinateSelector\"].value = \"CoordinateB\"\n", "y_offset_mm = nodemap[\"Scan3dCoordinateOffset\"].value # offset for y\n", "nodemap[\"Scan3dCoordinateSelector\"].value = \"CoordinateC\"\n", "z_offset_mm = nodemap[\"Scan3dCoordinateOffset\"].value # offset for z\n", "\n", "\n", "# Start stream and get image\n", "device_helios2.start_stream()\n", "buffer = device_helios2.get_buffer()\n", "\n", "# Copy image buffer into the Coord3d_ABCY16 format\n", "buffer_Coord3D_ABCY16 = convert_buffer_to_Coord3D_ABCY16(buffer)\n", "\n", "# get height and width\n", "height = int(buffer_Coord3D_ABCY16.height)\n", "width = int(buffer_Coord3D_ABCY16.width)\n", "channels_per_pixel = int(buffer_Coord3D_ABCY16.bits_per_pixel / 16)\n", "\n", "image_matrix_HLT_XYZ = np.zeros((height, width, 3), dtype=np.float32)\n", "image_matrix_HLT_intensity = np.zeros((height, width), dtype=np.uint16)\n", "\n", "# get input data\n", "# Buffer.pdata is a (uint8, ctypes.c_ubyte) pointer.\n", "# This pixelformat has 4 channels, and each channel is 16 bits.\n", "# It is easier to deal with Buffer.pdata if it is cast to 16bits\n", "# so each channel value is read correctly.\n", "# The pixelformat is suffixed with \"S\" to indicate that the data\n", "# should be interpereted as signed. This one does not have \"S\", so\n", "# we cast it to unsigned.\n", "pdata_as_uint16 = ctypes.cast(buffer_Coord3D_ABCY16.pdata, ctypes.POINTER(ctypes.c_uint16))\n", "\n", "i = 0\n", "\n", "for ir in range(height):\n", "    for ic in range(width):\n", "\n", "        # Get unsigned 16 bit values for X,Y,Z coordinates\n", "        x_u16 = pdata_as_uint16[i]\n", "        y_u16 = pdata_as_uint16[i + 1]\n", "        z_u16 = pdata_as_uint16[i + 2]\n", "\n", "        # Convert 16-bit X,Y,Z to float values in mm\n", "        image_matrix_HLT_XYZ[ir, ic][0] = float(x_u16 * xyz_scale_mm + x_offset_mm)\n", "        image_matrix_HLT_XYZ[ir, ic][1] = float(y_u16 * xyz_scale_mm + y_offset_mm)\n", "        image_matrix_HLT_XYZ[ir, ic][2] = float(z_u16 * xyz_scale_mm + z_offset_mm)\n", "\n", "        image_matrix_HLT_intensity[ir, ic] = pdata_as_uint16[i + 3]\n", "\n", "        i += channels_per_pixel\n", "\n", "\n", "# Stop stream\n", "device_helios2.requeue_buffer(buffer)\n", "device_helios2.stop_stream()\n", "\n", "image_matrix_HLT_intensity, image_matrix_HLT_XYZ"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate orientation values"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Find points in TRI image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_calibration_points_TRI(image_in_orig):\n", "    '''\n", "    Returns an array of calibration points found in the given image captured by Triton\n", "    '''\n", "\n", "    scaling = 1.0\n", "    image_in = image_in_orig\n", "    num_cols_orig = image_in_orig.shape[1] # width\n", "    num_rows_orig = image_in_orig.shape[0] # height\n", "\n", "    # Create blob detector ------------------------------------------------------------------\n", "    bright_params = cv2.SimpleBlobDetector_Params()\n", "    bright_params.filterByColor = True\n", "    bright_params.blobColor = 255 # White circles in the calibration target\n", "    bright_params.filterByCircularity = True\n", "    bright_params.minCircularity = 0.8\n", "\n", "    blob_detector = cv2.SimpleBlobDetector.create(bright_params)\n", "\n", "    # Find calibration points --------------------------------------------------------\n", "    pattern_size = (5, 4) # (pattern_per_row, pattern_per_column)\n", "    is_found, grid_centers = cv2.findCirclesGrid(image_in, pattern_size, flags=cv2.CALIB_CB_SYMMETRIC_GRID, blobDetector=blob_detector)\n", "\n", "    scaled_nrows = 2400.0\n", "\n", "    while not is_found and scaled_nrows >= 100:\n", "        scaled_nrows /= 2.0\n", "        scaling = float(num_rows_orig / scaled_nrows)\n", "\n", "        image_in = cv2.resize(image_in_orig, (int(num_cols_orig/scaling), int(num_rows_orig/scaling))) # cv2.resize(image, (width, height))\n", "\n", "        is_found, grid_centers = cv2.findCirclesGrid(image_in, pattern_size, flags=cv2.CALIB_CB_SYMMETRIC_GRID, blobDetector=blob_detector)\n", "\n", "    if is_found:\n", "        for center in grid_centers:\n", "            center[0][0] *= scaling\n", "            center[0][1] *= scaling\n", "\n", "    return is_found, grid_centers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["count = 0\n", "while True:\n", "    grid_centers_TRI_found, grid_centers_TRI = find_calibration_points_TRI(image_matrix_TRI)\n", "    if not grid_centers_TRI_found or len(grid_centers_TRI) != 20:\n", "        print(f\"Unable to find points in TRI image. {count} seconds passed\", end='\\r')\n", "        count += 1\n", "        time.sleep(1)\n", "    else:\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Find points in HLT Image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_calibration_points_HLT(image_in_orig):\n", "    '''\n", "    Returns an array of calibration points found in the given image captured by Helios2\n", "    '''\n", "\n", "    image_in = image_in_orig\n", "\n", "    # Create blob detector ------------------------------------------------------------------\n", "    bright_params = cv2.SimpleBlobDetector_Params()\n", "    bright_params.filterByColor = True\n", "    bright_params.blobColor = 255 # White circles in the calibration target\n", "    bright_params.thresholdStep = 2\n", "    bright_params.minArea = 10.0 # Min/max area can be adjusted based on size of dots in image\n", "    bright_params.maxArea = 1000.0\n", "\n", "    blob_detector = cv2.SimpleBlobDetector.create(bright_params)\n", "\n", "    # pattern_size(num_cols, num_rows) num_cols: number of columns (number of\n", "    # circles in a row) of the calibration target viewed by the camera num_rows:\n", "    # number of rows (number of circles in a column) of the calibration target\n", "    # viewed by the camera Specify according to the orientation of the\n", "    # calibration target\n", "    pattern_size = (5, 4)\n", "\n", "    # Find min and max values in the input image\n", "    _, max_value, _, _ = cv2.minMaxLoc(image_in)\n", "    \n", "    # Scale image to 8-bit, using full 8-bit range\n", "    image_8bit = cv2.convertScaleAbs(image_in, alpha=255.0/max_value)\n", "\n", "    is_found, grid_centers = cv2.findCirclesGrid(image_8bit, pattern_size, flags=cv2.CALIB_CB_SYMMETRIC_GRID, blobDetector=blob_detector)\n", "\n", "    return is_found, grid_centers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["count = 0\n", "while True:\n", "    grid_centers_HLT_found, grid_centers_HLT = find_calibration_points_HLT(image_matrix_HLT_intensity)\n", "    if not grid_centers_HLT_found or len(grid_centers_HLT) != 20:\n", "        print(f'Unable to find points in HLT intensity image. {count} seconds passed', end='\\r')\n", "        count += 1\n", "        time.sleep(1)\n", "    else:\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Prepare for PnP"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target_points_3d_mm = []\n", "target_points_3d_pixels = []\n", "target_points_2d_pixels = []\n", "\n", "\n", "for i in range(len(grid_centers_TRI)):\n", "    c1 = round(grid_centers_HLT[i][0][0])\n", "    r1 = round(grid_centers_HLT[i][0][1])\n", "    c2 = round(grid_centers_TRI[i][0][0])\n", "    r2 = round(grid_centers_TRI[i][0][1])\n", "\n", "    [x, y, z] = [image_matrix_HLT_XYZ[r1, c1][j] for j in range(3)]\n", "    pt = [x, y, z]\n", "    \n", "    print(f'Point {i}: {pt}')\n", "\n", "    target_points_3d_mm.append(pt)\n", "    target_points_3d_pixels.append(grid_centers_HLT[i][0])\n", "    target_points_2d_pixels.append(grid_centers_TRI[i][0])\n", "\n", "target_points_3d_mm = np.array(target_points_3d_mm)\n", "target_points_3d_pixels = np.array(target_points_3d_pixels)\n", "target_points_2d_pixels = np.array(target_points_2d_pixels)\n", "orientation_succeeded, rotation_vector, translation_vector = cv2.solvePnP(target_points_3d_mm, target_points_2d_pixels, camera_matrix, dist_coeffs)\n", "\n", "print(f'Orientation succeeded' if orientation_succeeded else f'Orientation failed')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save calculated orientation information"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'Save camera matrix, distance coefficients, and rotation and translation vectors to file {FILE_NAME_OUT}')\n", "fs = cv2.FileStorage(FILE_NAME_OUT, cv2.FileStorage_WRITE)\n", "fs.write('cameraMatrix', camera_matrix)\n", "fs.write('distCoeffs', dist_coeffs)\n", "fs.write('rotationVector', rotation_vector)\n", "fs.write('translationVector', translation_vector)\n", "fs.release()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Return nodes to their original values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap_triton.get_node(\"PixelFormat\").value = pixel_format_initial_triton\n", "nodemap_helios2.get_node(\"PixelFormat\").value = pixel_format_initial_helios2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Destroy all created device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()\n", "print(f'Destroyed all created devices')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculated values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('cameraMatrix')\n", "print(camera_matrix)\n", "print('dist<PERSON>oeffs')\n", "print(dist_coeffs)\n", "print('rotationVector')\n", "print(rotation_vector)\n", "print('translationVector')\n", "print(translation_vector)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}