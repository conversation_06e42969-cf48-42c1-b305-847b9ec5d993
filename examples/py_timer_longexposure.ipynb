{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system\n", "from arena_api.__future__.save import Writer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Timer: Introduction\n", "> This example introduces how the timer can be used to control specific features\n", "    within the camera. In this case, we use the exposure as an example. Most cameras have a\n", "    maxiumum exposure limit, but this provides the ability to bypass the exposure limit\n", "    through the use of timer and trigger. First, the timer and trigger settings are configured \n", "    so that the timer is connected to the trigger, which is set to activate the exposure. As\n", "    the timer begins, the trigger activates and the device starts to expose an image. Once \n", "    the timer duration expires, the exposure stops, the device grabs the image, and the image\n", "    is saved to disk."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "TIMER_DURATION = 15000 # in milliseconds\n", "FILE_NAME = 'Images/py_timer/image.jpg'"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 1 device(s)\n", "  Only one device detected:  ('1c:0f:af:29:f2:a4', 'TRI120S-M', '', '169.254.0.41')\n", "    Automatically selecting this device.\n", "Device used in the example:\n", "\t('1c:0f:af:29:f2:a4', 'TRI120S-M', '', '169.254.0.41')\n"]}], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "            tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "nodemap = device.nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Stores intial node values, return their values at the end\n", "\"\"\"\n", "nodes = nodemap.get_node(['TriggerSelector', 'TriggerMode', 'TriggerSource', \n", "                          'TriggerActivation', 'TimerSelector', 'TimerTriggerSource', \n", "                          'TimerDuration', 'ExposureAuto', 'SoftwareSignalSelector'])\n", "\n", "trigger_selector_initial = nodes['TriggerSelector'].value\n", "trigger_mode_initial = nodes['TriggerMode'].value\n", "trigger_source_initial = nodes['TriggerSource'].value\n", "trigger_activation_initial = nodes['TriggerActivation'].value\n", "timer_selector_initial = nodes['TimerSelector'].value\n", "timer_trigger_source_initial = nodes['TimerTriggerSource'].value\n", "timer_duration_initial = nodes['TimerDuration'].value\n", "exposure_auto_initial = nodes['ExposureAuto'].value\n", "software_signal_selector_initial = nodes['SoftwareSignalSelector'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### demonstrates the use of timer to configure exposure time\n", "1. configures the timer and trigger\n", "2. starts stream\n", "3. starts timer, which triggers the exposure\n", "4. once timer duration is up, acquire image\n", "5. save long exposure image to disk\n", "\n", ">Setting required timer and trigger settings\n", "If any of these do not work, it may be due to the camera type\n", "or camera firmware. Try updating the firmware or using a\n", "different camera."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Set trigger selector to Exposure Active\n", "  Set trigger source to Timer 0 Active\n", "  Set trigger activation to Level High\n", "  Set trigger mode to On\n", "  Set timer selector to Timer 0\n", "  Set timer trigger source to Software Signal 0\n", "  Set timer duration to 15000 milliseconds\n", "  Set exposure auto to Off\n", "    Exposure auto not a feature available\n", "  Set software signal selector to Software Signal 0\n"]}], "source": ["\"\"\"\n", "Set trigger selector\n", "\tSet the trigger selector to Exposure Active. When triggered,\n", "\tthe device will start exposing an image.\n", "\"\"\"\n", "print(f'{TAB1}Set trigger selector to Exposure Active')\n", "nodes['TriggerSelector'].value = 'ExposureActive'\n", "\n", "\"\"\"\n", "Set trigger source\n", "\tSet the trigger source to Timer 0 Active. This connects\n", "\tthe trigger to the timer.\n", "\"\"\"\n", "print(f'{TAB1}Set trigger source to Timer 0 Active')\n", "nodes['TriggerSource'].value = 'Timer0Active'\n", "\n", "\"\"\"\n", "Set trigger activation\n", "\tSet the trigger source to Level High. This ensures\n", "\tthat the trigger is activated when the timer is running.\n", "\"\"\"\n", "print(f'{TAB1}Set trigger activation to Level High')\n", "nodes['TriggerActivation'].value = 'LevelHigh'\n", "\n", "\"\"\"\n", "Set trigger mode\n", "\tSet trigger mode to on. This enables trigger mode\n", "\ton the camera.\n", "\"\"\"\n", "print(f'{TAB1}Set trigger mode to On')\n", "nodes['TriggerMode'].value = 'On'\n", "\n", "# set timer selector\n", "print(f'{TAB1}Set timer selector to Timer 0')\n", "nodes['TimerSelector'].value = 'Timer0'\n", "\n", "\"\"\"\n", "Set timer trigger source\n", "\tSet timer trigger source to Software Signal 0. Timer is only\n", "\tactivated through software by calling the SoftwareSignal node.\n", "\"\"\"\n", "print(f'{TAB1}Set timer trigger source to Software Signal 0')\n", "nodes['TimerTriggerSource'].value = 'SoftwareSignal0'\n", "\n", "\"\"\"\n", "Set timer duration\n", "\tSet the timer duration to user-set duration. This gives user\n", "\tability to control the duration of specific camera events\n", "\twithout limitations. The timer duration node is configured \n", "\tin microseconds.\n", "\"\"\"\n", "print(f'{TAB1}Set timer duration to {TIMER_DURATION} milliseconds')\n", "# convert TIMER_DURATION to milliseconds\n", "timer_duration = float(TIMER_DURATION * 1000)\n", "nodes['TimerDuration'].value = timer_duration\n", "\n", "\"\"\"\n", "Set exposure auto\n", "\tSet exposure auto to off. Ensures the exposure time is only\n", "\tmanipulated by user. Encapsulated in a try/catch block because\n", "\tsome cameras do not have exposure auto.\n", "\"\"\"\n", "print(f'{TAB1}Set exposure auto to Off')\n", "try:\n", "\tnodes['ExposureAuto'].value = 'Off'\n", "except:\n", "\tprint(f'{TAB2}Exposure auto not a feature available')\n", "\n", "\"\"\"\n", "Set software signal selector\n", "\tSet software signal selector to Software Signal 0. Connects software\n", "\tsignal to timer trigger.\n", "\"\"\"\n", "print(f'{TAB1}Set software signal selector to Software Signal 0')\n", "nodes['SoftwareSignalSelector'].value = 'SoftwareSignal0'\n", "\n", "\n", "\"\"\"\n", "Setup stream values\n", "\"\"\"\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Start stream"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Start stream\n", "    Wait until trigger is armed\n", "    Start timer for 15.0 seconds\n", "    Get image\n", "    Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\Images\\py_timer\\image_0.jpg\n", "  Stop stream\n"]}], "source": ["print(f'{TAB1}Start stream')\n", "device.start_stream()\n", "\n", "\"\"\"\n", "<PERSON><PERSON>\n", "    Continually checks until trigger is armed. Once the trigger is\n", "    armed, it is ready to be executed.\n", "\"\"\"\n", "print(f'{TAB2}Wait until trigger is armed')\n", "trigger_armed = False\n", "\n", "while trigger_armed is False:\n", "    trigger_armed = bool(nodemap['TriggerArmed'].value)\n", "\n", "\"\"\"\n", "Start timer\n", "\tThis is done through a software signal. This also triggers the \n", "\texposure active trigger, which starts to expose the image.\n", "\"\"\"\n", "print(f'{TAB2}Start timer for {TIMER_DURATION / 1e3} seconds')\n", "nodemap['SoftwareSignalPulse'].execute()\n", "\n", "\"\"\"\n", "Acquire image\n", "\tThis waits for the length of the timer duration to get an image.\n", "\tIf an image buffer is not ready by the timeout duration, it fails.\n", "\tThe result should be an image exposed to the duration of the timer.\n", "\"\"\"\n", "print(f'{TAB2}Get image')\n", "timeout = int(timer_duration)\n", "buffer = device.get_buffer(timeout=timeout)\n", "\n", "# save image\n", "writer = Writer.from_buffer(buffer)\n", "writer.pattern = FILE_NAME\n", "writer.save(buffer)\n", "print(f'{TAB2}Image saved {writer.saved_images[-1]}')\n", "\n", "# Requeue buffer\n", "device.requeue_buffer(buffer)\n", "\n", "# Stop stream\n", "print(f'{TAB1}Stop stream')\n", "device.stop_stream()\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Exposure auto not a feature available\n"]}], "source": ["\"\"\"\n", "Return nodes to their initial values\n", "\"\"\"\n", "nodes['TriggerSelector'].value = trigger_selector_initial\n", "nodes['TriggerMode'].value = trigger_mode_initial\n", "nodes['TriggerSource'].value = trigger_source_initial\n", "nodes['TriggerActivation'].value = trigger_activation_initial\n", "nodes['TimerSelector'].value = timer_selector_initial\n", "nodes['TimerTriggerSource'].value = timer_trigger_source_initial\n", "nodes['TimerDuration'].value = timer_duration_initial\n", "try:\n", "    nodes['ExposureAuto'].value = exposure_auto_initial\n", "except:\n", "    print(f'{TAB1}Exposure auto not a feature available')\n", "nodes['SoftwareSignalSelector'].value = software_signal_selector_initial"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["system.destroy_device()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}