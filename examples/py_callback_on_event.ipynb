{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "from arena_api.callback import callback, callback_function\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Callbacks: On Event\n", ">    This example demonstrates configuring a callback with events. Events are a\n", "    subset of nodes that invoke callbacks through the underlying events engine.\n", "    The events engine is first initialized to listen for events, then the\n", "    callback is registered using the timestamp test event node. The event is\n", "    generated, along with any data generated from the event. The example then\n", "    waits for the event to process in order to invoke the callback. Registered\n", "    callbacks must also be deregistered before deinitializing the events engine\n", "    in order to avoid memory leaks.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Must have the decorator on the callback function\n", "> node.on_update requires node as its first parameter<br>\n", "This function is triggered when the callback event is triggered"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@callback_function.node.on_update\n", "def print_node_value(node, *args, **kwargs):\n", "\n", "    print(f'Message from callback')\n", "    print(f'\\'{node.name}\\' event has triggered this callback')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Store initial values\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["streamBufferHandlingMode_initial = \\\n", "    device.tl_stream_nodemap['StreamBufferHandlingMode'].value\n", "triggerSource_initial = device.nodemap['TriggerSource'].value\n", "triggerSelector_initial = device.nodemap['TriggerSelector'].value\n", "triggerMode_initial = device.nodemap['TriggerMode'].value\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Register the callback on event node"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["event_node = device.nodemap.get_node('EventExposureEnd')\n", "\n", "handle = callback.register(event_node, print_node_value)\n", "print(f\"Registered '{print_node_value.__name__}' function \"\n", "      f\"in '{event_node.name}' node\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Enable trigger mode before setting the source and selector and before starting the stream. \n", ">   Trigger mode cannot be turned\n", "    on and off while the device is streaming.<br>\n", "    Make sure <PERSON>gger <PERSON> set to 'Off' after finishing this example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.nodemap.get_node('TriggerMode').value = 'On'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set the trigger source to software in order to trigger buffers without the use of any additional hardware.\n", ">  Lines of the GPIO can also be used to trigger"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.nodemap.get_node('TriggerSource').value = 'Software'\n", "\n", "device.nodemap.get_node('TriggerSelector').value = 'FrameStart'\n", "\n", "device.tl_stream_nodemap.get_node(\n", "    'StreamBufferHandlingMode').value = 'OldestFirst'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Initialize events\n", ">    Turn event notification on\n", "    Select the event type to be notified about\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.initialize_events()\n", "device.nodemap.get_node('EventSelector').value = 'ExposureEnd'\n", "device.nodemap.get_node('EventNotification').value = 'On'\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Register callback function on event node\n", ">      Allows for manual triggering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["event_node = device.nodemap.get_node('EventExposureEnd')\n", "\n", "handle = callback.register(event_node, print_node_value)\n", "print(f\"Registered '{print_node_value.__name__}' function \"\n", "      f\"in '{event_node.name}' node\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Grab images -------------------------------------------------------------\n", "> - Starting the stream allocates buffers, which can be passed in as\n", " an argument (default: 10), and begins filling them with data.\n", "> - Continually check until trigger is armed. Once the trigger is armed, it is ready to be executed.\n", ">  Trigger an image buffer manually, since trigger mode is enabled.\n", "> > -    This triggers the camera to acquire a single image buffer.\n", "> > -    A buffer is then filled and moved to the output queue, where it will wait to be retrieved.\n", "> > -    Before the image buffer is sent, the exposure end event will occur. This will happen on every iteration\n", "> - Wait on the event to process it, invoking the registered callback.\n", "> >  -   The data is created from the event generation, not from waiting on it. \n", "> > -    If the exposure time is long a Timeout exception may occur unless large timeout value is passed to the 'Device.wait_on_event()'\n", "   "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.start_stream()\n", "for _ in range(10):\n", "\n", "    while not device.nodemap.get_node('TriggerArmed').value:\n", "        continue\n", "        \n", "    device.nodemap.get_node('TriggerSoftware').execute()\n", "    \n", "    device.wait_on_event()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Deregister each handle in the handle list\n", "> Must be called before device is destroyed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["callback.deregister(handle)\n", "device.deinitialize_events()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Restore initial values to the device.\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.stop_stream()\n", "\n", "device.nodemap['TriggerSource'].value = triggerSource_initial\n", "device.nodemap['TriggerSelector'].value = triggerSelector_initial\n", "device.nodemap['TriggerMode'].value = triggerMode_initial\n", "\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}