{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Streamables\n", "> This example introduces streamables, which uses files to pass settings around between devices. This example writes all streamable features from a source device to a file, and then writes them from the file to all other connected devices."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "# The name of the file to stream features to/from\n", "FILE_NAME = 'all_streamable_features.txt'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### This example:\n", "1. reads all streamable features from source device\n", "2. writes features to file\n", "3. reads features from file\n", "4. writes features to destination devices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if (len(devices) is 1):\n", "    print(f\"WARNING: Only 1 device is connected, Example runs best with \"\n", "            \"atleast 2 devices\\n\")\n", "\n", "print(f'{TAB1}Save streamable features from \\n{TAB2}{device}\\n'\n", "        f'{TAB1} to {os.getcwd()}{os.path.sep}{FILE_NAME}\\n')\n", "\n", "device.nodemap.write_streamable_node_values_to(FILE_NAME)\n", "\n", "print(\n", "    f'{TAB1}Load streamable features from \\n'\n", "    f'{TAB1}{os.getcwd()}{os.path.sep}{FILE_NAME}\\n'\n", "    f'{TAB1} to all connected devices\\n')\n", "\n", "for device in devices:\n", "\n", "    print(f'{TAB1}Loading to {device}')\n", "    device.nodemap.read_streamable_node_values_from(FILE_NAME)\n", "    print(f'{TAB1}Loaded Successfully')\n", "\n", "system.destroy_device()\n"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}