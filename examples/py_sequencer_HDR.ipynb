{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import time\n", "from pathlib import Path\n", "\n", "from arena_api import enums\n", "from arena_api.__future__.save import Writer\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Sequencer HDR\n", "> This example demonstrates saving a set of images using the sequencer. It will cycle through 3 sequencer states each one using a different exposure time. The images could then be used to generate an HDR image. Much like a state machine, in order to use the sequencer we must initialize each set appropriately. Each set can have its own exposure time and gain, and will contain information such as the sequencer starting position as well as paths to other sets. A set can have multiple paths where each path has its own next set, trigger source and trigger activation. In this example the sequencer has 3 sets where set 0 goes to set 1, set 1 goes to set 2 and set 2 goes back to set 0, all being triggered on Frame Start."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Create configurations for a sequencer set and save to a given set number"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def set_sequencer_set(nodemap, set_number, exposure_time, path_next_set,\n", "                      trigger_source):\n", "    '''\n", "    Set Sequencer Set Selector to sequence number\n", "    '''\n", "    nodemap['SequencerSetSelector'].value = set_number\n", "    print(f'Updating set {set_number} :')\n", "\n", "    # Set Exposure Time to the desired value\n", "    nodemap['SequencerFeatureSelector'].value = 'ExposureTime'\n", "    nodemap['ExposureTime'].value = exposure_time\n", "    print(f'\\texposure time value = {exposure_time}')\n", "\n", "    '''\n", "    Select the path we want it to follow from this set to the next set. There\n", "        can be multiple paths so the first path will always be set to 0\n", "    '''\n", "    nodemap['SequencerPathSelector'].value = 0\n", "\n", "    # Set next state in the sequence, ensure it does not exceed the maximum\n", "    nodemap['SequencerSetNext'].value = path_next_set\n", "    print(f'\\tset next            = {path_next_set}')\n", "\n", "    # Set Sequencer Trigger Source to Frame Start\n", "    nodemap['SequencerTriggerSource'].value = trigger_source\n", "    print(f'\\ttrigger source      = {trigger_source}')\n", "\n", "    '''\n", "    Save current state\n", "    Once all appropriate settings have been configured, make sure to\n", "        save the state to the sequence. Notice that these settings will be\n", "        lost when the camera is power-cycled.\n", "    '''\n", "    print(f'\\tSave sequence set {set_number}')\n", "    nodemap['SequencerSetSave'].execute()\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Sets 'ExposureAuto' node to off\n", ">the node is read-only when Sequencer Configuration Mode is 'On'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def set_exposure_auto_to_off(nodemap):\n", "    if nodemap['SequencerConfigurationMode'].value == 'On':\n", "        print('Turn \\'SequencerConfigurationMode\\' Off')\n", "        nodemap['SequencerConfigurationMode'].value = 'Off'\n", "        print(f'\\t\\'SequencerConfigurationMode\\' is '\n", "              f'''{nodemap['SequencerConfigurationMode'].value} now''')\n", "\n", "    print('Turn \\'ExposureAuto\\' Off')\n", "    nodemap['ExposureAuto'].value = 'Off'\n", "    print(f'''\\t\\'ExposureAuto\\' is {nodemap['ExposureAuto'].value} now''')\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Enable sequencer configuration mode\n", "> must disable sequencer mode first"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def set_sequencer_configuration_mode_on(nodemap):\n", "\n", "    # If Sequencer Mode is 'On', it makes 'SequencerConfigurationMode'\n", "    # a read-only node\n", "    if nodemap['SequencerMode'].value == 'On':\n", "        print('Turn \\'SequencerMode\\' Off')\n", "        nodemap['SequencerMode'].value = 'Off'\n", "        print(\n", "            f'''\\t\\'SequencerMode\\' is {nodemap['SequencerMode'].value} now''')\n", "\n", "    print('Turn \\'SequencerConfigurationMode\\' On')\n", "    nodemap['SequencerConfigurationMode'].value = 'On'\n", "    print(f'\\t\\'SequencerConfigurationMode\\' is '\n", "          f'''{nodemap['SequencerConfigurationMode'].value} now''')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'Device used in the example:\\n\\t{device}')\n", "\n", "nodemap = device.nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "#  Set up nodes -----------------------------------------------------------\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "# Disable automatic exposure and gain before setting an exposure time.\n", "set_exposure_auto_to_off(nodemap)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Set up sequencer sets\n", "> using the function that we created earlier"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "'''\n", "If 'SequencerMode' is on, turn it off so the sequencer becomes\n", "    configurable through 'SequencerConfigurationMode'.\n", "    Put sequencer in configuration mode.\n", "    Sequencer configuration mode must be on while making changes to\n", "    the sequencer sets.\n", "'''\n", "set_sequencer_configuration_mode_on(nodemap)\n", "\n", "# From device.nodemap['SequencerSetSelector'].max gives the maximum\n", "# of sequencer sets can be set on the device.\n", "\n", "# Make sure the example works with all devices.\n", "# Take the smaller value to set a long exposure time of some devices\n", "exposure_time_long = min(nodemap['ExposureTime'].max, 100000.0)\n", "\n", "print('Set up sequencer sets')\n", "sets_settings = [\n", "    {\n", "        'set_number': 0,\n", "        'exposure_time': exposure_time_long / 40,\n", "        'path_next_set': 1,\n", "        'trigger_source': 'FrameStart'\n", "    },\n", "    {\n", "        'set_number': 1,\n", "        'exposure_time': exposure_time_long / 20,\n", "        'path_next_set': 2,\n", "        'trigger_source': 'FrameStart'\n", "    },\n", "    {\n", "        'set_number': 2,\n", "        'exposure_time': exposure_time_long,\n", "        'path_next_set': 0,  # Means it goes back to the set in index 0\n", "        'trigger_source': 'FrameStart'\n", "    }\n", "]\n", "\n", "for set_settings in sets_settings:\n", "    set_sequencer_set(nodemap, **set_settings)\n", "\n", "# Sets the sequencer starting set to 0\n", "print('Set stream to start from sequencer set 0')\n", "nodemap['SequencerSetStart'].value = 0\n", "\n", "# Turn off configuration mode\n", "print('Turn \\'SequencerConfigurationMode\\' Off')\n", "nodemap['SequencerConfigurationMode'].value = 'Off'\n", "print(f'\\t\\'SequencerConfigurationMode\\' is '\n", "      f'''{nodemap['SequencerConfigurationMode'].value} now''')\n", "\n", "'''\n", "Turn on sequencer\n", "   When sequencer mode is on and the device is streaming it will\n", "   follow the sequencer sets according to their saved settings.\n", "'''\n", "print('Turn \\'SequencerMode\\' On')\n", "nodemap['SequencerMode'].value = 'On'\n", "print(f'''\\t\\'SequencerMode\\' is {nodemap['SequencerMode'].value} now''')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Use sequencer\n", "> obtains three image buffers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get width, height, and pixel format nodes\n", "width_node = device.nodemap['Width']\n", "height_node = device.nodemap['Height']\n", "pixelformat_node = device.nodemap['PixelFormat']\n", "\n", "if not width_node.is_readable or \\\n", "        not height_node.is_readable or \\\n", "        not pixelformat_node.is_readable:\n", "    raise Exception('Width, Height, or PixelFormat node is not readable')\n", "\n", "pixelformat_node.value = 'Mono8'\n", "\n", "# Starting the stream allocates buffers, which can be passed in as\n", "# an argument (default: 10), and begins filling them with data.\n", "print('\\nStart streaming')\n", "with device.start_stream(3):\n", "\n", "    # Get an image buffer in each set of sequencer\n", "    print('Getting 3 image buffers')\n", "\n", "    # Save images so we can view them later\n", "    writer = Writer()\n", "\n", "    # Run our 3 sets one time\n", "    for count in range(3):\n", "        print(f'\\tConverting and saving image {count}')\n", "\n", "        # Get image\n", "        buffer = device.get_buffer()\n", "\n", "        # Save images with default name\n", "        writer.save(buffer, f\"Images/image{count}.png\")\n", "        print(f'Image saved {writer.saved_images[-1]}')\n", "\n", "        # Requeue image buffer\n", "        device.requeue_buffer(buffer)\n", "    print(f'Requeued {count + 1} buffers')\n", "\n", "# Stream stops automatically when the scope of the context manager ends\n", "print('Stream stopped')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Clean up"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Turn sequencer mode off so the device is set to the original settings\n", "print('Turn \\'SequencerMode\\' Off')\n", "nodemap['SequencerMode'].value = 'Off'\n", "print(f'''\\t\\'SequencerMode\\' is {nodemap['SequencerMode'].value} now''')\n", "\n", "# Destroy all created devices. This call is optional and will\n", "# automatically be called for any remaining devices when the system module\n", "# is unloading.\n", "system.destroy_device()\n", "print('Destroyed all created devices')\n"]}], "metadata": {"interpreter": {"hash": "3da5db85d9f5f7fce7af321096f4072067948d3445eede98938ef98c3c692858"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}