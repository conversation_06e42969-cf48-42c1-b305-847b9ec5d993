{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from arena_api.system import system\n", "\n", "# for printing IP addresses\n", "import ipaddress"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### IP Config Manual\n", "> This example sets persistent IP on the camera. 5 parts:\n", ">1. Persistent IP address to ***********\n", ">2. Subnet mask to ***********\n", ">3. Enables persistent IP\n", ">4. Disables DHCP\n", ">5. Disables ARP conflict detection\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('\\nWARNING:\\nTHIS EXAMPLE MIGHT CHANGE THE DEVICE(S) SETTINGS!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Discover devices on network')\n", "devices = system.create_device()\n", "print(f'{len(devices)} devices found')\n", "\n", "# Choose the first device for this example\n", "device = system.select_device(devices)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Sets persistent IP on the camera. 5 parts:\n", "1. Persistent IP address to ***********\n", "2. Subnet mask to ***********\n", "3. Enables persistent IP\n", "4. Disables DHCP\n", "5. Disables ARP conflict detection\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "nodemap = device.nodemap\n", "\n", "'''\n", "Calculate IPv4 addresses:\n", "    4 sets of eight bits for addresses\n", "    can calculate by shifting with 2^(8x)\n", "'''\n", "\n", "new_ip_address = ipaddress.IPv4Address(\"***********\")\n", "new_subnet_mask = ipaddress.IPv4Address(\"***********\")\n", "\n", "'''\n", "Find previous IPv4\n", "'''\n", "old_ip_address = \\\n", "    ipaddress.IPv4Address(nodemap.get_node(\"GevPersistentIPAddress\").value)\n", "\n", "old_subnet_mask = \\\n", "    ipaddress.IPv4Address(nodemap.get_node(\n", "        \"GevPersistentSubnetMask\").value)\n", "\n", "# Set device configurations ------------------------------------------------\n", "\n", "print(f\"Change persistent IP from {old_ip_address} to {new_ip_address}\")\n", "nodemap.get_node(\"GevPersistentIPAddress\").value = int(new_ip_address)\n", "\n", "print(f\"Change subnet mask from {old_subnet_mask} to {new_subnet_mask}\")\n", "nodemap.get_node(\"GevPersistentSubnetMask\").value = int(new_subnet_mask)\n", "\n", "print(\"Enable persistent IP\")\n", "nodemap.get_node(\"GevCurrentIPConfigurationPersistentIP\").value = True\n", "\n", "print(\"Disable DHCP\")\n", "nodemap.get_node(\"GevCurrentIPConfigurationDHCP\").value = False\n", "\n", "print(\"Disable ARP conflict resolution\")\n", "nodemap.get_node(\"GevPersistentARPConflictDetectionEnable\").value = False\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Example complete.\")"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}