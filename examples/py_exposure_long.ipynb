{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Long Exposure: Introduction\n", ">   This example depicts the code to increase the maximum exposure time. By default,\n", "\tLucid cameras are prioritized to achieve maximum frame rate. However, due to the\n", "    high frame rate configuration, the exposure time will be limited as it is a dependant value. <br>\n", "    If the frame rate is 30 FPS, the maximum allowable exposure would be 1/30 = 0.0333 seconds = 33.3 milliseconds. <br>\n", "    So, a decrease in the frame rate is necessary for increasing the exposure time."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "num_images = 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Waits for the user to connect a device before\n", "raising an exception if it fails\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "devices = None\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "nodemap = device.nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "print(f'Device used in the example:\\n{TAB1}{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Store initial values\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes = nodemap.get_node(['ExposureAuto', 'ExposureTime', 'AcquisitionFrameRateEnable', 'AcquisitionFrameRate'])\n", "\n", "exposure_auto_initial = nodes['ExposureAuto'].value\n", "exposure_time_initial = nodes['ExposureTime'].value\n", "acquisition_fr_enable_initial = nodes['AcquisitionFrameRateEnable'].value\n", "acquisition_fr_initial = nodes['AcquisitionFrameRate'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates exposure: long\n", "> - Set Acquisition Frame Rate Enable to true\n", "> - Decrease Acquisition Frame Rate\n", "> - Set Exposure Auto to OFF\n", "> - Increase Exposure Time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['AcquisitionFrameRateEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### For the maximum exposure, the acquisition frame rate is set to the lowest value allowed by the camera."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['AcquisitionFrameRate'].value = nodes['AcquisitionFrameRate'].min"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Disable automatic exposure\n", ">   Disable automatic exposure before setting an exposure time. Automatic\n", "    exposure controls whether the exposure time is set manually or\n", "    automatically by the device. Setting automatic exposure to 'Off' stops\n", "    the device from automatically updating the exposure time."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['ExposureAuto'].value = 'Off'\n", "print(f'{TAB1}Disable Auto Exposure')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Get exposure time node\n", ">   In order to get the exposure time maximum and minimum values, get the\n", "    exposure time node. Failed attempts to get a node return null, so check\n", "    that the node exists. And because we expect to set its value, check\n", "    that the exposure time node is writable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if nodes['ExposureTime'] is None:\n", "    raise Exception(\"ExposureTime node not found\")\n", "if not nodes['ExposureTime'].is_writable:\n", "    raise Exception(\"ExposureTime node is not writable\")\n", "\n", "print(f'{TAB1}Minimizing Acquisition FrameRate and Maximizing Exposure')\n", "\n", "print(f'{TAB2}Changed Acquisition Frame Rate from {acquisition_fr_initial}'\n", "        f''' to {nodes['AcquisitionFrameRate'].value}''')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set exposure time to the maximum value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['ExposureTime'].value = nodes['ExposureTime'].max\n", "\n", "print(f'{TAB2}Changed Exposure Time from {exposure_time_initial}'\n", "        f''' to {nodes['ExposureTime'].value}''')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Setup stream values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap = device.tl_stream_nodemap\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Start stream and grab long exposure images"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'\\n{TAB1}Getting {num_images} image(s)')\n", "\n", "device.start_stream()\n", "\n", "for i in range(num_images):\n", "    buffer = device.get_buffer()\n", "\n", "    print(f'{TAB2}Long Exposure Image {i} Retrieved')\n", "\n", "    device.requeue_buffer(buffer)\n", "\n", "device.stop_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Return nodes to intial values\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['ExposureTime'].value = exposure_time_initial\n", "nodes['ExposureAuto'].value = exposure_auto_initial\n", "nodes['AcquisitionFrameRate'].value = acquisition_fr_initial\n", "nodes['AcquisitionFrameRateEnable'].value = acquisition_fr_enable_initial\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}