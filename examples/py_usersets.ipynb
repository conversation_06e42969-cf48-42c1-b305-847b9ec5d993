{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### User sets\n", "> This example introduces user sets, a feature which allows for the saving and loading of multiple configurations. The example starts by changing two features, width and height. The device configuration is then saved to user set 1. The default user set is then loaded, followed by user set 1 again to demonstrate the the device configuration changing back and forth."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save to user set 1\n", ">Saving the user set saves the new width and height values. Saving a user set involves selecting the user set to save on the selector node and then executing the save on the command node."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap = device.nodemap\n", "WIDTH = 576\n", "HEIGHT = 512\n", "\n", "nodemap.get_node(\"Width\").value = WIDTH\n", "nodemap.get_node(\"Height\").value = HEIGHT\n", "\n", "'''\n", "Save configurations to UserSet1\n", "'''\n", "print(\"Saved configurations to UserSet1\")\n", "user_set_selector_node = nodemap.get_node(\"UserSetSelector\")\n", "user_set_selector_node.value = \"UserSet1\"\n", "nodemap.get_node(\"UserSetSave\").execute()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Display Height and Width for UserSet1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Height: {nodemap.get_node('Height').value}\")\n", "print(f\"Width: {nodemap.get_node('Width').value}\")\n", "print()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Load and display Height and Width for Default user set"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Loaded Default user set\")\n", "user_set_selector_node.value = \"Default\"\n", "nodemap.get_node(\"UserSetLoad\").execute()\n", "print(f\"Height: {nodemap.get_node('Height').value}\")\n", "print(f\"Width: {nodemap.get_node('Width').value}\")\n", "print()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Reload and display Height and Width for UserSet1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Loaded UserSet1\")\n", "user_set_selector_node.value = \"UserSet1\"\n", "nodemap.get_node(\"UserSetLoad\").execute()\n", "print(f\"Height: {nodemap.get_node('Height').value}\")\n", "print(f\"Width: {nodemap.get_node('Width').value}\")\n", "print()\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}