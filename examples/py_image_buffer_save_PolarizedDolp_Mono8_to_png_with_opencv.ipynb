{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import time\n", "from pathlib import Path\n", "\n", "import cv2  # pip install opencv-python\n", "import numpy as np  # pip install numpy\n", "\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save: PolarizedDoIp_Mono8 to PNG\n", "> This example demonstrates saving PolarizedDoIp_Mono8 buffers to a PNG image using OpenCV and NumPy. This includes configuring stream settings, getting buffer data as c pointers, converting the data to a numpy array, and configuring and saving the data with OpenCV."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Setup stream nodes\n", "\"\"\"\n", "# Get device stream nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Configure camera nodes\n", "\"\"\"\n", "nodes = device.nodemap.get_node(['Width', 'Height', 'PixelFormat'])\n", "\n", "# Nodes\n", "print('Setting Width to its maximum value')\n", "nodes['Width'].value = nodes['Width'].max\n", "\n", "print('Setting Height to its maximum value')\n", "height = nodes['Height']\n", "height.value = height.max\n", "\n", "# Set pixel format to PolarizedDolp_Mono8\n", "pixel_format_name = 'PolarizedDolp_Mono8'\n", "print(f'Setting Pixel Format to {pixel_format_name}')\n", "nodes['PixelFormat'].value = pixel_format_name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### demonstrates Save: PolarizedDoIp_Mono8 to PNG\n", "1. Setup stream nodes\n", "2. Configure camera nodes\n", "3. Get image buffer data as c pointers\n", "4. Convert the data to numpy array using np.ctypeslib\n", "5. Saving raw image with OpenCV\n", "6. Applying a color map to the image\n", "7. Saving image as <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "print('Starting stream')\n", "with device.start_stream(1):\n", "    print('Grabbing an image buffer')\n", "    image_buffer = device.get_buffer()  # Optional args\n", "\n", "    print(f' Width X Height = '\n", "        f'{image_buffer.width} x {image_buffer.height}')\n", "\n", "    \"\"\"\n", "    np.ctypeslib.as_array() detects that Buffer.pdata is (uint8, c_ubyte)\n", "    type so it interprets each byte as an element.\n", "    For 16Bit images Buffer.pdata must be cast to (uint16, c_ushort)\n", "    using ctypes.cast(). After casting, np.ctypeslib.as_array() can\n", "    interpret every two bytes as one array element (a pixel).\n", "    \"\"\"\n", "    print('Converting image buffer to a numpy array')\n", "    nparray_reshaped = np.ctypeslib.as_array(image_buffer.pdata,\n", "                                            (image_buffer.height,\n", "                                            image_buffer.width))\n", "\n", "    # Saving --------------------------------------------------------------\n", "    print('Saving image')\n", "\n", "    # RAW\n", "    png_raw_name = f'from_{pixel_format_name}_raw_to_png_with_opencv.png'\n", "    cv2.imwrite(png_raw_name, nparray_reshaped)\n", "    print(f'Saved image path is: {Path(os.getcwd()) / png_raw_name}')\n", "\n", "    # HSV\n", "    png_hsv_name = f'from_{pixel_format_name}_hsv_to_png_with_opencv.png'\n", "    cm_nparray = cv2.applyColorMap(nparray_reshaped, cv2.COLORMAP_HSV)\n", "    cv2.imwrite(png_hsv_name, cm_nparray)\n", "    print(f'Saved image path is: {Path(os.getcwd()) / png_hsv_name}')\n", "\n", "    device.requeue_buffer(image_buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}