{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Trigger: Introduction\n", "> This example introduces basic trigger configuration and use. In order to configure trigger, enable trigger mode and set the source and selector. The trigger must be armed before it is prepared to execute. Once the trigger is armed, execute the trigger and retrieve an image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "nodemap = device.nodemap\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Stores intial node values, return their values at the end\n", "\"\"\"\n", "nodes = nodemap.get_node(['TriggerSelector', 'TriggerMode', 'TriggerSource'])\n", "\n", "trigger_selector_initial = nodes['TriggerSelector'].value\n", "trigger_mode_initial = nodes['TriggerMode'].value\n", "trigger_source_initial = nodes['TriggerSource'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### demonstrates basic trigger configuration and use\n", "1. sets trigger mode, source, and selector\n", "2. starts stream\n", "3. waits until trigger is armed\n", "4. triggers image\n", "5. gets image\n", "6. requeues buffer\n", "7. stops stream"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Set trigger selector\n", "    Set the trigger selector to FrameStart. When triggered, the\n", "    device will start acquiring a single frame. This can also be\n", "    set to AcquisitionStart or FrameBurstStart.\n", "\"\"\"\n", "print(f'{TAB1}Set trigger selector to FrameStart')\n", "nodes['TriggerSelector'].value = 'FrameStart'\n", "\n", "\"\"\"\n", "Set trigger selector\n", "    Set the trigger selector to FrameStart. When triggered, the\n", "    device will start acquiring a single frame. This can also be\n", "    set to AcquisitionStart or FrameBurstStart.\n", "\"\"\"\n", "print(f'{TAB1}Enable trigger mode')\n", "nodes['TriggerMode'].value = 'On'\n", "\n", "\"\"\"\n", "Set trigger mode\n", "    Enable trigger mode before setting the source and selector and\n", "    before starting the stream. Trigger mode cannot be turned on\n", "    and off while the device is streaming.\n", "\"\"\"\n", "print(f'{TAB1}Set trigger source to software')\n", "nodes['TriggerSource'].value = 'Software'\n", "\n", "\"\"\"\n", "Setup stream values\n", "\"\"\"\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Start stream\n", "> When trigger mode is off and the acquisition mode is set to stream continuously, starting the stream will have the camera begin acquiring a steady stream of images. However, with trigger mode enabled, the device will wait for the trigger before acquiring any."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Start stream')\n", "device.start_stream()\n", "\n", "\"\"\"\n", "<PERSON><PERSON>\n", "    Continually checks until trigger is armed. Once the trigger is\n", "    armed, it is ready to be executed.\n", "\"\"\"\n", "print(f'{TAB2}Wait until trigger is armed')\n", "trigger_armed = False\n", "\n", "while trigger_armed is False:\n", "    trigger_armed = bool(nodemap['TriggerArmed'].value)\n", "\n", "\"\"\"\n", "Trigger an image\n", "    Trigger an image manually, since trigger mode is enabled. This\n", "    triggers the camera to acquire a single image. A buffer is then\n", "    filled and moved to the output queue, where it will wait to be\n", "    retrieved.\n", "\"\"\"\n", "print(f'{TAB2}Trigger Image')\n", "nodemap['TriggerSoftware'].execute()\n", "\n", "\"\"\"\n", "Get image\n", "    Once an image has been triggered, it can be retrieved. If no\n", "    image has been triggered, trying to retrieve an image will hang\n", "    for the duration of the timeout and then throw an exception.\n", "\"\"\"\n", "buffer = device.get_buffer()\n", "\n", "# Print some info about the image in the buffer\n", "print(f'{TAB2}Buffer received | ['\n", "        f'Width = {buffer.width} pxl, '\n", "        f'Height = {buffer.height} pxl]')\n", "\n", "# Requeue buffer\n", "print(f'{TAB2}Requeue Buffer')\n", "device.requeue_buffer(buffer)\n", "\n", "# Stop stream\n", "print(f'{TAB1}Stop stream')\n", "device.stop_stream()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Return nodes to their initial values\n", "\"\"\"\n", "nodes['TriggerSelector'].value = trigger_selector_initial\n", "nodes['TriggerMode'].value = trigger_mode_initial\n", "nodes['TriggerSource'].value = trigger_source_initial\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}