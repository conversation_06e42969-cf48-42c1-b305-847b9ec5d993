{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.callback import callback, callback_function\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Callbacks: Polling\n", ">    This example demonstrates configuring a callback with polling. Polling\n", "    allows for callbacks to be invoked over time."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Must have the decorator on the callback function\n", "> node.on_update requires node as its first parameter<br>\n", "This function is triggered when the callback event is triggered"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@callback_function.node.on_update\n", "def print_node_value(node):\n", "    print(f\"\\tTemperature: {node.value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Register the callback on DeviceTemperature node\n", ">  Nodes are polled through their node maps. This example demonstrates\n", "    polling the device temperature node. It has a polling time of 1 second,\n", "    which means that its callback will not be invoked within 1 second of\n", "    the last time it has been polled."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Registering callback on DeviceTemperature node\")\n", "\n", "nodemap = device.nodemap\n", "node = nodemap.get_node(\"DeviceTemperature\")\n", "\n", "handle = callback.register(node, print_node_value)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Poll at regular intervals\n", "> - The callback will only be invoked if the cumulative elapsed time since\n", "    the last callback is larger than the polling time.\n", "> - We calculate the elapsed\n", "    time since the last callback, and pass it to the function maually, in miliseconds."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["curr_time = time.time()\n", "time_of_last_call = time.time()\n", "\n", "print(\"Polling at regular intervals:\")\n", "for i in range(6):\n", "    time.sleep(0.5)\n", "    curr_time = time.time()\n", "    print(\"\\tPolled\")\n", "    nodemap.poll(int(1000 * curr_time - 1000 * time_of_last_call))\n", "    time_of_last_call = curr_time\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Deregister each handle in the handle list\n", "> Must be called before device is destroyed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Deregistering callback\")\n", "callback.deregister(handle)\n", "\n", "system.destroy_device()\n", "print(\"Destroyed device(s)\")"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}