{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from arena_api.system import system\n", "from arena_api.buffer import *\n", "\n", "import ctypes\n", "import numpy as np\n", "import cv2\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Live Stream: Introduction\n", ">This example introduces the basics of running a live stream from a single device. This includes creating a device, selecting up stream dimensions, getting buffer cpointer data, creating an array of the data and reshaping it to fit image dimensions using NumPy and displaying using OpenCV-Python."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Setup stream dimensions and stream nodemap\n", ">num_channels changes based on the PixelFormat<br>\n", ">Mono 8 would has 1 channel, RGB8 has 3 channels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap = device.nodemap\n", "nodes = nodemap.get_node(['Width', 'Height', 'PixelFormat'])\n", "\n", "nodes['Width'].value = 1280\n", "nodes['Height'].value = 720\n", "nodes['PixelFormat'].value = 'RGB8'\n", "\n", "num_channels = 3\n", "\n", "# Stream nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "tl_stream_nodemap[\"StreamBufferHandlingMode\"].value = \"NewestOnly\"\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### demonstrates live stream\n", "1. Start device stream\n", "2. Get a buffer and create a copy\n", "3. Re<PERSON>ue the buffer\n", "4. Calculate bytes per pixel for reshaping\n", "5. Create array from buffer cpointer data\n", "6. Create a NumPy array with the image shape\n", "7. Display the NumPy array using OpenCV\n", "8. When Esc is pressed, stop stream and destroy OpenCV windows"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["curr_frame_time = 0\n", "prev_frame_time = 0\n", "\n", "with device.start_stream():\n", "    \"\"\"\n", "    Infinitely fetch and display buffer data until esc is pressed\n", "    \"\"\"\n", "    while True:\n", "        # Used to display FPS on stream\n", "        curr_frame_time = time.time()\n", "\n", "        buffer = device.get_buffer()\n", "        \"\"\"\n", "        Copy buffer and requeue to avoid running out of buffers\n", "        \"\"\"\n", "        item = BufferFactory.copy(buffer)\n", "        device.requeue_buffer(buffer)\n", "\n", "        buffer_bytes_per_pixel = int(len(item.data)/(item.width * item.height))\n", "        \"\"\"\n", "        Buffer data as cpointers can be accessed using buffer.pbytes\n", "        \"\"\"\n", "        array = (ctypes.c_ubyte * num_channels * item.width * item.height).from_address(ctypes.addressof(item.pbytes))\n", "        \"\"\"\n", "        Create a reshaped NumPy array to display using OpenCV\n", "        \"\"\"\n", "        npndarray = np.ndarray(buffer=array, dtype=np.uint8, shape=(item.height, item.width, buffer_bytes_per_pixel))\n", "        \n", "        fps = str(1/(curr_frame_time - prev_frame_time))\n", "        cv2.putText(npndarray, fps, (7, 70), cv2.FONT_HERSHEY_SIMPLEX, 3, (100, 255, 0), 3, cv2.LINE_AA)\n", "        \n", "\n", "        cv2.imshow('Lucid', npndarray)\n", "        \"\"\"\n", "        Destroy the copied item to prevent memory leaks\n", "        \"\"\"\n", "        BufferFactory.destroy(item)\n", "\n", "        prev_frame_time = curr_frame_time\n", "\n", "        \"\"\"\n", "        Break if esc key is pressed\n", "        \"\"\"\n", "        key = cv2.<PERSON><PERSON><PERSON>(1)\n", "        if key == 27:\n", "            break\n", "        \n", "    device.stop_stream()\n", "    cv2.destroyAllWindows() "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}