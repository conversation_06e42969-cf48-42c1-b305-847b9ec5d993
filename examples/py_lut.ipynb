{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system\n", "from arena_api.__future__.save import Writer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Lookup Tables: Introduction\n", "\n", ">This example introduces the lookup tables (LUT), which are used to transform image data into a desired output format. LUTs give an output value for each of a range of index values. This example enables a lookup table node to invert the intensity of a single image. This is done by accessing the LUT index node and setting the LUT node values to the newly calculated pixel intensity value. It takes some time to update each pixel with the new value. The example then saves the new image by saving to the image writer."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Slope for calculating the new intensity value to set\n", "SLOPE = -1\n", "\n", "# timeout for detecting camera devices (in milliseconds).\n", "SYSTEM_TIMEOUT = 100\n", "\n", "# image timeout\n", "IMAGE_TIMEOUT = 2000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Store initial settings, to restore later\n", "nodemap = device.nodemap\n", "initial_lut_enable = nodemap.get_node(\"LUTEnable\").value\n", "initial_acquisition_mode = nodemap.get_node(\"AcquisitionMode\").value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get device stream nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True   \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Acquires a single buffer and saves it to disk\n", "'''\n", "def save_one_image(device, filename):\n", "    with device.start_stream():\n", "        writer = Writer()\n", "        print(f'Stream started with 10 buffers')\n", "\n", "        # 'Device.get_buffer()' with no arguments returns only one buffer\n", "        print('\\tGet one buffer')\n", "        buffer = device.get_buffer()\n", "        # Print some info about the image in the buffer\n", "        print(f'\\t\\tbuffer received   | '\n", "              f'Width = {buffer.width} pxl, '\n", "              f'Height = {buffer.height} pxl, '\n", "              f'Pixel Format = {buffer.pixel_format.name}')\n", "\n", "        print(\"\\t\\tSave buffer\")\n", "        writer.save(buffer, filename)\n", "        # Requeue the image buffer\n", "        device.requeue_buffer(buffer)\n", "        print(\"\\tBuffer requeued\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save one image with LUT disabled"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap.get_node(\"LUTEnable\").value = False\n", "\n", "save_one_image(device, \"Images/lut_no.png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Enable and configure LUT"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["node_lut_index = nodemap.get_node(\"LUTIndex\")\n", "node_lut_value = nodemap.get_node(\"LUTValue\")\n", "\n", "if node_lut_index is None or node_lut_value is None:\n", "\traise Exception(\"Requisite node(s) LUTIndex and/or LUTValue do(es) not exist\")\n", "\n", "nodemap.get_node(\"LUTEnable\").value = True\n", "\n", "for i in range(node_lut_index.max):\n", "\t'''\n", "    Select each pixel's intesity, and map it to its inversion\n", "        i -> max - i, using example's original settings (SLOPE = -1)\n", "    '''\n", "\tnode_lut_index.value = i\n", "\tnode_lut_value.value = SLOPE * i + node_lut_index.max\n", "\n", "\tif i % 1024 == 0:\n", "\t\tprint(\"\\t\", end=\"\")\n", "\n", "\tif i % 256 == 255:\n", "\t\tprint(\".\", end=\"\")\n", "\n", "\tif i % 1024 == 1023:\n", "\t\tprint()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save image with LUT enabled: with inverted intensity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_one_image(device, \"Images/lut_yes.png\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap.get_node(\"LUTEnable\").value = initial_lut_enable\n", "nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}