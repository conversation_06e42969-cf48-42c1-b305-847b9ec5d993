{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from datetime import datetime\n", "\n", "from arena_api.enums import PixelFormat\n", "from arena_api import enums as _enums\n", "from arena_api.__future__.save import Writer\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Image Processing: Split Channels\n", ">This example introduces splitting channels of an image and saving split images as JPEG images."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "pixel_format = PixelFormat.BGR8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Setup stream values\n", "\"\"\"\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.start_stream()\n", "buffer = device.get_buffer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Function to save a JPEG image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save(buffer, count):\n", "\t'''\n", "\tdemonstrates saving a JPEG image\n", "\t(1) converts image to a displayable pixel format\n", "\t(2) prepares image parameters\n", "\t(3) prepares image writer\n", "\t(4) saves image with configuration parameters\n", "\t(5) destroys converted image\n", "\t'''\n", "\n", "\t'''\n", "\tconvert image\n", "\t'''\n", "\tconverted = BufferFactory.convert(buffer, pixel_format)\n", "\tprint(f\"{TAB1}Converted image to {pixel_format.name}\")\n", "\n", "\t'''\n", "\tprepare image writer\n", "\t'''\n", "\tprint(f'{TAB1}Prepare Image Writer')\n", "\twriter = Writer()\n", "\twriter.pattern = 'images/py_imageprocessing_splitchannels/image_'+ str(count)+'.jpg'\n", "\t\n", "\twriter.save(converted, quality=75, progressive=False, subsampling=_enums.ScJpegSubsamplingList.SC_NO_JPEG_SUBSAMPLING, optimize=False)\n", "\tprint(f'{TAB1}Image saved {writer.saved_images[-1]}')\n", "\n", "\t# Des<PERSON>y converted buffer to avoid memory leaks\n", "\tBufferFactory.destroy(converted)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Split Channels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["buffers = BufferFactory.split_channels(buffer)\n", "\n", "for count, buf in enumerate(buffers):\n", "\tsave(buf, count)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.requeue_buffer(buffer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Clean up"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.stop_stream()\n", "\n", "# Destroy Device\n", "system.destroy_device()"]}], "metadata": {"kernelspec": {"display_name": "Python 3.10.7 ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "9a831188c03c7caeebd3251c40fdaa86517461ff286f09cf3194138e4e5369b6"}}}, "nbformat": 4, "nbformat_minor": 2}