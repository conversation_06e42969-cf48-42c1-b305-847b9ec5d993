{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import time\n", "from datetime import datetime\n", "\n", "from arena_api.enums import PixelFormat\n", "from arena_api import enums as _enums\n", "from arena_api.__future__.save import Writer\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save: Bmp\n", ">This example introduces saving BMP image data in the saving library. It\n", "   shows the construction of image writer, and saves a single BMP image."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "pixel_format = PixelFormat.BGR8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Setup stream values\n", "\"\"\"\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["device.start_stream()\n", "buffer = device.get_buffer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### demonstrates saving a BMP image\n", "1. converts image to a displayable pixel format\n", "2. prepares image parameters\n", "3. prepares image writer\n", "4. saves image\n", "5. destroys converted image"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Convert image\n", "> Convert the image to a displayable pixel format. It is worth keeping in mind the best pixel and file formats for your application. This example converts the image so that it is displayable by the operating system."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["converted = BufferFactory.convert(buffer, pixel_format)\n", "print(f\"{TAB1}Converted image to {pixel_format.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Prepare image writer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Prepare Image Writer')\n", "writer = Writer.from_buffer(converted)\n", "writer.pattern = 'images/image_<count>.bmp'\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save converted buffer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["''' \n", "Save function for .bmp file\n", "    buffer: buffer to save.\n", "'''\n", "writer.save(converted)\n", "print(f'{TAB1}Image saved')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### <PERSON><PERSON><PERSON> converted buffer to avoid memory leaks"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["BufferFactory.destroy(converted)\n", "\n", "device.requeue_buffer(buffer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Clean up"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["device.stop_stream()\n", "\n", "# Destroy Device\n", "system.destroy_device()"]}], "metadata": {"kernelspec": {"display_name": "Python 3.10.7 ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "9a831188c03c7caeebd3251c40fdaa86517461ff286f09cf3194138e4e5369b6"}}}, "nbformat": 4, "nbformat_minor": 2}