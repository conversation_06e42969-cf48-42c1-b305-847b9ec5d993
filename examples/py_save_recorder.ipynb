{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "from arena_api.__future__.save import Recorder\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.enums import PixelFormat\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save Recorder: Introduction\n", ">This example demonstrates creating a save recorder to save videos from image buffer data. This includes, configuring and initializing save recorder, getting and appending buffers to the recorder, saving the video."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Setup stream nodes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap = device.nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "print(f'Device used in the example:\\n\\t{device}')\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Configure camera nodes\n", "\"\"\"\n", "\"\"\"\n", "set width and height to max values might make the video frame rate low\n", "The larger the height of the buffer the lower the fps\n", "\"\"\"\n", "width_node = nodemap['Width']\n", "width = nodemap['Width'].max\n", "\n", "height_node = nodemap['Height']\n", "height = nodemap['Height'].max\n", "\n", "\"\"\"\n", "if the images from the device are already in the format expected by the\n", "recorder then no need to convert received buffers which results in better\n", "performance\n", "\"\"\"\n", "nodemap['PixelFormat'].value = PixelFormat.BGR8"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### demonstrates Save Recorder\n", "1. Setup stream nodes\n", "2. Configure device nodes\n", "3. Create a recorder object and configure its width, height, and acquisition frame rate\n", "4. Open recorder, start stream and get buffers\n", "5. Append buffers to the recorder\n", "6. Close the recorder to save the video\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with device.start_stream(100):\n", "    print('Stream started')\n", "\n", "    \"\"\"\n", "    create a recorder\n", "    The recorder, takes width, height, and frames per seconds.\n", "    These argument can be deferred until Recorder.open is called\n", "    \"\"\"\n", "    recorder = Recorder(nodemap['Width'].value,\n", "                        nodemap['Height'].value,\n", "                        nodemap['AcquisitionFrameRate'].value)\n", "    print('fps',  nodemap['AcquisitionFrameRate'].value)\n", "\n", "    recorder.codec = ('h264', 'mp4', 'bgr8')  # order does not matter\n", "\n", "    \"\"\"\n", "    recorder settings can not be changed after open is called util\n", "    close is called\n", "    \"\"\"\n", "    recorder.open()\n", "    print('recorder opened')\n", "\n", "    TOTAL_IMAGES = 100\n", "    for count in range(TOTAL_IMAGES):\n", "        buffer = device.get_buffer()\n", "        print(f'Image buffer received')\n", "\n", "        \"\"\"\n", "        After recorder.open() add image to the open recorder stream by\n", "            appending buffers to the video.\n", "        The buffers are already BGR8, because we set 'PixelFormat'\n", "            node to 'BGR8', so no need to convert buffers using\n", "            BufferFactory.convert() from arena_api.buffer\n", "        \"\"\"\n", "\n", "        \"\"\"\n", "        default name for the video is 'video<count>.mp4' where count\n", "        is a pre-defined tag that gets updated every time open()\n", "        is called. More custom tags can be added using\n", "        Recorder.register_tag() function\n", "        \"\"\"\n", "        recorder.append(buffer)\n", "        print(f'Image buffer {count} appended to video')\n", "\n", "        device.requeue_buffer(buffer)\n", "        print(f'Image buffer requeued')\n", "\n", "    recorder.close()\n", "    print('recorder closed')\n", "    print(f'video saved {recorder.saved_videos[-1]}')\n", "\n", "    video_length_in_secs = (TOTAL_IMAGES /\n", "                            nodemap['AcquisitionFrameRate'].value)\n", "    print(f'video length is {video_length_in_secs} seconds')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}