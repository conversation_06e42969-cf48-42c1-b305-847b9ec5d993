{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,<br>EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES<br>OF ME<PERSON><PERSON>NTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND<br><PERSON><PERSON><PERSON>RINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS<br>BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN<br>ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN<br>CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN<br>THE SOFTWARE<br> -----------------------------------------------------------------------------<br>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import time\n", "from pathlib import Path\n", "\n", "import cv2  # pip install opencv-python\n", "import numpy as np  # pip install numpy\n", "\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save: PolarizedAoIp_Mono8 to PNG\n", ">\tThis example demonstrates saving PolarizedAoIp_Mono8\n", "\tbuffers to a PNG image using OpenCV and NumPy. This\n", "\tincludes configuring stream settings, getting buffer\n", "\tdata as c pointers, converting the data to a numpy array,\n", "\tand configuring and saving the data with OpenCV."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Setup stream nodes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get device stream nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Configure camera nodes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes = device.nodemap.get_node(['Width', 'Height', 'PixelFormat'])\n", "\n", "# Nodes\n", "print('Setting Width to its maximum value')\n", "nodes['Width'].value = nodes['Width'].max\n", "\n", "print('Setting Height to its maximum value')\n", "height = nodes['Height']\n", "height.value = height.max\n", "\n", "# Set pixel format to PolarizedDolp_Mono8\n", "pixel_format_name = 'PolarizedDolp_Mono8'\n", "print(f'Setting Pixel Format to {pixel_format_name}')\n", "nodes['PixelFormat'].value = pixel_format_name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Scale the rawpoints array.\n", "- Each \"column\" is normalized to the same linear scale from lowest value = 0 to highest value = 255\n", "- more: https://gist.github.com/perrygeo/4512375gistcomment-967246\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def scale_linear_bycolumn(rawpoints, high=255.0, low=0.0):\n", "\tmins = np.min(rawpoints, axis=0)\n", "\tmaxs = np.max(rawpoints, axis=0)\n", "\trng = maxs - mins\n", "\treturn high - (((high - low) * (maxs - rawpoints)) / rng)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### demonstrates Save: PolarizedAoIp_Mono8 to PNG\n", "1. Setup stream nodes\n", "2. Configure camera nodes\n", "3. Get image buffer data as c pointers\n", "4. Convert the data to numpy array using np.ctypeslib\n", "5. Saving raw image with OpenCV\n", "6. Scaling and applying color map to the image\n", "7. Saving image as <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "print('Starting stream')\n", "with device.start_stream(1):\n", "    print('Grabbing an image buffer')\n", "    image_buffer = device.get_buffer()  # Optional args\n", "\n", "    print(f' Width X Height = '\n", "        f'{image_buffer.width} x {image_buffer.height}')\n", "\n", "    \"\"\"\n", "    Buffer.pdata is a (uint8, ctypes.c_ubyte) type\n", "    Buffer.data is a list of elements each represents one byte.\n", "    Since Mono8 uses 8Bits (1 byte), then non eed to cast\n", "    the uint8 pointer off buffer.pdata.\n", "    It is easier to user Buffer.pdata over Buffer.data in other than\n", "    8bits pixelformats.=\n", "    \"\"\"\n", "    print('Converting image buffer to a numpy array')\n", "    nparray_reshaped = np.ctypeslib.as_array(image_buffer.pdata,\n", "                                            (image_buffer.height,\n", "                                            image_buffer.width))\n", "\n", "    # Saving --------------------------------------------------------------\n", "    print('Saving image')\n", "\n", "    # RAW\n", "    png_raw_name = f'from_{pixel_format_name}_raw_to_png_with_opencv.png'\n", "    cv2.imwrite(png_raw_name, nparray_reshaped)\n", "    print(f'Saved image path is: {Path(os.getcwd()) / png_raw_name}')\n", "\n", "    # HSV\n", "    png_hsv_name = f'from_{pixel_format_name}_hsv_to_png_with_opencv.png'\n", "\n", "    \"\"\"\n", "    Scale up the pixel value range from [0, 201] to [0,255]\n", "    \"\"\"\n", "    scaled_nparray_reshaped = scale_linear_bycolumn(nparray_reshaped)\n", "    scaled_nparray_reshaped_8 = np.uint8(scaled_nparray_reshaped)\n", "    cm_nparray = cv2.applyColorMap(\n", "        scaled_nparray_reshaped_8, cv2.COLORMAP_HSV)\n", "    cv2.imwrite(png_hsv_name, cm_nparray)\n", "    print(f'Saved image path is: {Path(os.getcwd()) / png_hsv_name}')\n", "\n", "    device.requeue_buffer(image_buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.9 64-bit ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}