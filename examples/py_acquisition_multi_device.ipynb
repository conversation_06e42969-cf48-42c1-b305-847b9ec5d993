{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import threading\n", "import time\n", "from arena_api.system import system\n", "\n", "NUMBER_OF_BUFFERS = 25"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: Multi-Device\n", ">    This example introduces the basics of image acquisition for multiple\n", "    devices and creating multiple threads. This includes creating all\n", "    discovered devices, creating a thread for each device to acquire images.\n", "    The thread then starts acquiring images by grabbing and requeuing buffers,\n", "    before finally stopping the image stream."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        device = system.select_device(devices)\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Configure devices and store initial values\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create empty lists for the devices\n", "initial_acquisition_mode_list = []\n", "\n", "# Configure each device\n", "for device in devices:\n", "    # Get nodemap\n", "    nodemap = device.nodemap\n", "\n", "    # Store initial acquisition mode to restore later\n", "    initial_acquisition_mode_list.append(nodemap.get_node(\"AcquisitionMode\").value)\n", "\n", "    # Set acquisition mode to continuous\n", "    nodemap.get_node(\"AcquisitionMode\").value = \"Continuous\"\n", "\n", "    # Get device stream nodemap\n", "    tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "    # Set buffer handling mode to \"Newest First\"\n", "    tl_stream_nodemap[\"StreamBufferHandlingMode\"].value = \"NewestOnly\"\n", "\n", "    # Enable stream auto negotiate packet size\n", "    tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "    # Enable stream packet resend\n", "    tl_stream_nodemap['StreamPacketResendEnable'].value = True\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def safe_print(*args, **kwargs):\n", "    \"\"\"\n", "    This function ensures resource access is locked to a single thread\n", "    \"\"\"\n", "    with threading.Lock():\n", "        print(*args, **kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates an acquisition on a device\n", "> - Start stream with N buffers\n", "> - Print each buffer info\n", "> - Requeue each buffer\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_multiple_image_buffers(device):\n", "    thread_id = f'''{device.nodemap['DeviceModelName'].value}''' \\\n", "        f'''-{device.nodemap['DeviceSerialNumber'].value} |'''\n", "\n", "    # Start stream with 25 buffers\n", "    device.start_stream(NUMBER_OF_BUFFERS)\n", "\n", "    # Print image buffer info\n", "    for count in range(NUMBER_OF_BUFFERS):\n", "        buffer = device.get_buffer()\n", "\n", "        safe_print(f'{thread_id:>30}',\n", "                   f'\\tbuffer{count:{2}} received | '\n", "                   f'Width = {buffer.width} pxl, '\n", "                   f'Height = {buffer.height} pxl, '\n", "                   f'Pixel Format = {buffer.pixel_format.name}')\n", "\n", "        \"\"\"\n", "        `Device.requeue_buffer()` takes a buffer or many buffers in\n", "        a list or tuple\n", "        \"\"\"\n", "        device.requeue_buffer(buffer)\n", "        safe_print(f'{thread_id:>30}', f'\\tbuffer{count:{2}} requeued | ')\n", "\n", "    device.stop_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Acquire images using one thread per device, then joins threads.\n", "> - Each device is on a seperate thread\n", "> - Start and join each thread in the list\n", "> - Calling thread is blocked util the thread object on which it was called is terminated."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["thread_list = []\n", "\n", "# Create a thread for each device\n", "for device in devices:\n", "    thread = threading.Thread(target=get_multiple_image_buffers,\n", "        args=(device,))\n", "    thread_list.append(thread)\n", "\n", "# Start each thread in the thread list\n", "for thread in thread_list:\n", "    thread.start()\n", "\n", "# Join each thread in the list\n", "for thread in thread_list:\n", "    thread.join()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Restore initial values to the device.\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(len(devices)):\n", "    devices[i].nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode_list[i]\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}