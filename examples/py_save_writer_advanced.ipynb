{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from datetime import datetime\n", "\n", "from arena_api.__future__.save import Writer \n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save: Advanced\n", "> This example shows the capabilities of the save library. It shows the construction of an image parameters object an image writer, a custom image name pattern, checks for incomplete images and saves multiple images."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def time_update_function():\n", "\t'''\n", "\tThis function will act like a generator. \n", "\t\tevery time it is triggered would return the time as str in the format\n", "\t\tshown\n", "\t'''\n", "\twhile True:\n", "\t\tnow = datetime.now()\n", "\t\tyield now.strftime('%H_%M_%S_%f')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Setup stream values\n", "\"\"\"\n", "\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### demonstrates saving an image\n", "1. registers an image writer tag for custom naming\n", "2. prepares image parameters\n", "3. prepares image writer\n", "4. saves the image if the buffer is complete, save the image, else save the image with a I_AM_INCOMPLETE tag\n", "5. requeue buffer to store the next image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "by default start_stream() has default argument of 10 which\n", "means 10 buffers can be filled with images or chunkdata before having to\n", "call device.requeue_buffer() on them for reuse.\n", "\"\"\"\n", "with device.start_stream():\n", "    print('Stream started')\n", "\n", "    \"\"\"\n", "    Create an image writer\n", "        The writer, optionally, can take width, height, and bits per pixel\n", "        of the image(s) it would save. if these arguments are not passed\n", "        # at run time, the first buffer passed to the Writer.save()\n", "        # function will configure the writer to the arguments buffer's width,\n", "        # height, and bits per pixel\n", "    \"\"\"\n", "    writer = Writer()\n", "\n", "    \"\"\"\n", "    Add a tag to use in the image name\n", "        By default the tag 'count' is defined for the user (can be\n", "        overwritten). A generator or a generator like function to be called\n", "        when new image saved evaluate its new name\n", "    \"\"\"\n", "    time_update_generator_from_func = time_update_function()\n", "    writer.register_tag(name='time',\n", "                        generator=time_update_generator_from_func)\n", "\n", "    \"\"\"\n", "    Set image name pattern\n", "        Default name for the image is 'image_<count>.jpg' where count\n", "        is a pre-defined tag that gets updated every time a buffer image\n", "        is saved.\n", "\n", "    Note:\n", "    All tags in pattern must be registered before\n", "    assigning the pattern to writer.pattern\n", "    \"\"\"\n", "    writer.pattern = 'all_images\\my_image_<count>_at_<time>.jpg'\n", "\n", "    for image_count in range(100):\n", "        buffer = device.get_buffer()\n", "        print(f'Image buffer {image_count} received')\n", "\n", "        \"\"\"\n", "        Let assume that one buffer should be saved with different name.\n", "        Choose the condition then pass the name to save. The name\n", "        will be used only for this save call and the future calls.\n", "        save() would use the pattern unless another name is passed.\n", "        \"\"\"\n", "\n", "        if buffer.is_incomplete:\n", "            \"\"\"\n", "            The case here that image would saved twice. One with the\n", "            pattern and the second copy by this condition\n", "            \"\"\"\n", "            writer.save(buffer, f'bad\\I_AM_INCOMPLETE_{image_count}.jpg')\n", "            print(f'Image saved {writer.saved_images[-1]}')\n", "        else:\n", "            \"\"\"\n", "            save the image with the pattern defined by writer.pattern\n", "            \"\"\"\n", "            writer.save(buffer)\n", "            print(f'Image saved {writer.saved_images[-1]}')\n", "\n", "        device.requeue_buffer(buffer)\n", "        print(f'Image buffer requeued')\n", "\n", "\"\"\"\n", "device.stop_stream() is automatically called at the end of the\n", "context manger scope\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function call with no arguments will destroy all of the\n", "created devices.\n", "\"\"\"\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}