{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------\n", "# -----------------------------------------------------------------------------\n", "# Warning:\n", "#\n", "# EVS examples support only on windows at the moment\n", "# -----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["'en_US.UTF-8'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["import time\n", "import locale\n", "\n", "from arena_api.system import system\n", "locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: EVS\n", "\n", "> This example demonstrates how to acquire images using the EVS\n", "> (Electronic Viewfinder System) stream protocol, which is designed \n", "> to provide efficient and high-quality image transfer from the camera \n", "> to the host system."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "TAB3 = \"\t \""]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Created 4 device(s)\n", "  Select device:\n", "    1. ('1c:0f:af:ef:5b:a7', 'ATX051S-C', '', '**************')\n", "    2. ('1c:0f:af:c4:07:90', 'TRI02KA-M', '', '************')\n", "    3. ('1c:0f:af:28:cc:b7', 'TRT009S-E', '', '*************')\n", "    4. ('1c:0f:af:3f:55:a4', 'PHX064S-M', '', '*************')\n"]}], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'{TAB1}Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{TAB1}{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "            tries += 1\n", "    else:\n", "        print(f'{TAB1}Created {len(devices)} device(s)')\n", "        device = system.select_device(devices)\n", "        nodemap = device.nodemap\n", "        tl_stream_nodemap = device.tl_stream_nodemap\n", "        break\n", "else:\n", "    raise Exception(f'{TAB1}No device found! Please connect a device and run '\n", "                    f'the example again.')\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Image (w, h) = (1280 , 720 )\n"]}], "source": ["width = nodemap.get_node(\"Width\").value\n", "height = nodemap.get_node(\"Height\").value\n", "print(f'{TAB1}Image (w, h) = ({width} , {height} )')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Configure device settings"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Set acquisition mode to 'Continuous'\n"]}], "source": ["print(f'{TAB1}Set acquisition mode to \\'Continuous\\'')\n", "initial_acquisition_mode = nodemap.get_node(\"AcquisitionMode\").value\n", "nodemap.get_node(\"AcquisitionMode\").value = \"Continuous\""]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Set buffer handling mode to 'NewestOnly'\n"]}], "source": ["print(f'{TAB1}Set buffer handling mode to \\'NewestOnly\\'')\n", "tl_stream_nodemap[\"StreamBufferHandlingMode\"].value = \"NewestOnly\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: EVS\n", ">\tThe EventFormat node determines whether the camera can use the EVS datastream engine. \n", ">   When set to EVS, Arena switches to the EVS engine. If EVS is not supported, \n", ">   the acquisition mode is restored to its original setting, and the process is exited."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Set Event Format to EVT3.0\n"]}], "source": ["print(f'{TAB1}Set Event Format to EVT3.0')\n", "\n", "try:\n", "    event_format_initial = nodemap.get_node('EventFormat').value\n", "    nodemap[\"EventFormat\"].value = \"EVT3_0\"\n", "except:\n", "    print(f'{TAB1}Connected camera does not support any EventFormats\\n')\n", "    nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode\n", "    system.destroy_device()\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set camera event rate to 10 Mev/s"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Set Camera Event Rate to 10 Mev/s\n"]}], "source": ["print(f'{TAB1}Set Camera Event Rate to 10 Mev/s')\n", "erc_enable_initial = nodemap.get_node('ErcEnable').value\n", "nodemap[\"ErcEnable\"].value = True\n", "\n", "camera_event_rate_initial = nodemap.get_node('ErcRateLimit').value\n", "nodemap[\"ErcRateLimit\"].value = 10.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#####  Set evs output format to CDFrame"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Set EVS output format to CDFrame\n"]}], "source": ["print(f'{TAB1}Set EVS output format to CDFrame')\n", "tl_stream_nodemap[\"StreamEvsOutputFormat\"].value = \"CDFrame\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["#####  Set evs accumulation time to auto"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Set EVS Accumulation Time to auto\n"]}], "source": ["print(f'{TAB1}Set EVS Accumulation Time to auto')\n", "stream_frame_generator_fps = tl_stream_nodemap[\"StreamFrameGeneratorFPS\"].value\n", "tl_stream_nodemap[\"StreamFrameGeneratorAccumTime\"].value = int(1000000 / stream_frame_generator_fps)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### EVS Rates Function\n", "\n", "Convert an event rate (given in raw events per second) to a more readable string format\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["def get_evs_event_rate(rate):\n", "    if rate < 1000:\n", "        return f\"{rate:.0f} ev/s\"\n", "    elif rate < 1000 * 1000:\n", "        return f\"{(rate / 1000):.1f} Kev/s\"\n", "    elif rate < 1000 * 1000 * 1000:\n", "        return f\"{(rate / (1000 * 1000)):.1f} Mev/s\"\n", "    else:\n", "        return f\"{(rate / (1000 * 1000 * 1000)):.1f} Gev/s\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Convert a gvsp frame rate to a more readable string format"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["def get_evs_gvsp_frame_rate(rate):\n", "    return f\"{rate:.0f} Bid/s\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Convert the link throughput to a more readable string format"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def get_evs_link_throughput(rate):\n", "    if rate < 1000:\n", "        return f\"{rate:.0f} Bps\"\n", "    elif rate < 1000 * 1000:\n", "        return f\"{(rate / 1000):.1f} KBps\"\n", "    elif rate < 1000 * 1000 * 1000:\n", "        return f\"{(rate / (1000 * 1000)):.1f} MBps\"\n", "    else:\n", "        return f\"{(rate / (1000 * 1000 * 1000)):.1f} GBps\""]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Stream started with 25 buffers\n", "  Get 25 buffers in a list\n", "Success\n", "    buffer 0 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer 1 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer 2 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer 3 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer 4 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer 5 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer 6 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer 7 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer 8 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer 9 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer10 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer11 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer12 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer13 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.7 MBps\n", "    buffer14 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "    buffer15 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "    buffer16 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "    buffer17 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "    buffer18 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "    buffer19 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6250 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "    buffer20 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "    buffer21 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "    buffer22 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "    buffer23 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "    buffer24 received\n", "\t Event Rate: 86.7 Kev/s\n", "\t GVSP Frame Rate: 6251 Bid/s\n", "\t GVSP Frame Rate: 4.6 MBps\n", "  Requeued 25 buffers\n", "  Stream stopped\n"]}], "source": ["number_of_buffers = 25\n", "\n", "device.start_stream(number_of_buffers)\n", "print(f'{TAB1}Stream started with {number_of_buffers} buffers')\n", "\n", "print(f'{TAB1}Get {number_of_buffers} buffers in a list')\n", "buffers = device.get_buffer(number_of_buffers)\n", "print(\"Success\")\n", "\n", "'''\n", "Print image buffer info\n", "    Buffers contain image data.\n", "    Image data can also be copied and converted using BufferFactory.\n", "    That is necessary to retain image data, as we must also requeue the buffer.\n", "'''\n", "for count, buffer in enumerate(buffers):\n", "\t\tprint(f'{TAB2}buffer{count:{2}} received')\n", "\n", "\t\tif buffer.is_incomplete:\n", "\t\t\tprint(f'{TAB3}Image {buffer.frame_id} is incomplete')\n", "\n", "\t\tevent_rate = tl_stream_nodemap[\"StreamEvsEventRate\"].value\n", "\t\tprint(f'{TAB3}Event Rate: {get_evs_event_rate(event_rate)}')\n", "\n", "\t\tgvsp_frame_rate = tl_stream_nodemap[\"StreamEvsGvspFrameRate\"].value\n", "\t\tprint(f'{TAB3}GVSP Frame Rate: {get_evs_gvsp_frame_rate(gvsp_frame_rate)}')\n", "\n", "\t\tlink_throughput = tl_stream_nodemap[\"StreamEvsLinkThroughput\"].value\n", "\t\tprint(f'{TAB3}GVSP Frame Rate: {get_evs_link_throughput(link_throughput)}')\n", "\n", "device.requeue_buffer(buffers)\n", "print(f'{TAB1}Requeued {number_of_buffers} buffers')\n", "\n", "device.stop_stream()\n", "print(f'{TAB1}Stream stopped')"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["nodemap.get_node(\"ErcEnable\").value = erc_enable_initial\n", "nodemap.get_node(\"ErcRateLimit\").value = camera_event_rate_initial\n", "nodemap.get_node(\"EventFormat\").value = event_format_initial"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Destroyed all created devices\n"]}], "source": ["nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode\n", "\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}