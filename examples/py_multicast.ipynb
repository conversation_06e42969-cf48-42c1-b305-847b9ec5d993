{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system\n", "from datetime import datetime\n", "\n", "NUM_SECONDS = 10\n", "TIMEOUT_MILLISEC = 2000"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Multicast\n", "> This example demonstrates multicasting from the master's perspective. Multicasting allows for the streaming of images and events to multiple destinations. Multicasting requires nearly the same steps for both masters and listeners. The only difference, as seen below, is that device features can only be set by the master."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Enable multicast and configure device\n", "> Multicast must be enabled on both the master and listener. A small number of transport layer features will remain writable even though a device's access mode might be read-only."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Enable multicast')\n", "device.tl_stream_nodemap['StreamMulticastEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Prepare settings on master, not on listener\n", "> Device features must be set on the master rather than the listener. This is because the listener is opened with a read-only access mode."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device_access_status = device.tl_device_nodemap['DeviceAccessStatus'].value\n", "\n", "# Master\n", "if device_access_status == 'ReadWrite':\n", "\n", "    print('Host streaming as \"master\"')\n", "\n", "    # Get node values that will be changed in order to return their values\n", "    # at the end of the example\n", "    acquisition_mode_initial = device.nodemap['AcquisitionMode'].value\n", "\n", "    # Set acquisition mode\n", "    print('Set acquisition mode to \"Continuous\"')\n", "\n", "    device.nodemap['AcquisitionMode'].value = 'Continuous'\n", "\n", "    # Enable stream auto negotiate packet size\n", "    device.tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "    # Enable stream packet resend\n", "    device.tl_stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "# Listener\n", "else:\n", "    print('Host streaming as \"listener\"\\n')\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define start and latest time for timed image acquisition\n", "start_time = datetime.now()\n", "latest_time = datetime.now()\n", "\n", "# Start stream\n", "with device.start_stream():\n", "\n", "    # Define image count to detect if all images are not received\n", "    image_count = 0\n", "    unreceived_image_count = 0\n", "\n", "    print(f'Stream started')\n", "\n", "    while (latest_time - start_time).total_seconds() < NUM_SECONDS:\n", "\n", "        # update time\n", "        latest_time = datetime.now()\n", "\n", "        try:\n", "            image_count = image_count + 1\n", "\n", "            # 'Device.get_buffer()' with no arguments returns\n", "            #  only one buffer\n", "            buffer = device.get_buffer(timeout=TIMEOUT_MILLISEC)\n", "\n", "            # Print some info about the image in the buffer\n", "            #   Using the frame ID and timestamp allows for the comparison\n", "            #   of images between multiple hosts.\n", "            print(f'\\t\\tImage retrieved ('\n", "                  f'frame ID = {buffer.frame_id}, '\n", "                  f'timestamp (ns) = {buffer.timestamp_ns}) and requeue')\n", "\n", "        except TimeoutError:\n", "            print(f'\\t\\tNo image received')\n", "            unreceived_image_count = unreceived_image_count + 1\n", "            continue\n", "\n", "        # Requeue the image buffer\n", "        device.requeue_buffer(buffer)\n", "\n", "if unreceived_image_count == image_count:\n", "    print(f'\\nNo images were received, this can be caused by firewall, vpn settings or firmware\\n')\n", "    print(f'Please add python application to firewall exception')\n", "\n", "# Return node to its initial value\n", "if device_access_status == \"ReadWrite\":\n", "    device.nodemap['AcquisitionMode'].value = acquisition_mode_initial\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}