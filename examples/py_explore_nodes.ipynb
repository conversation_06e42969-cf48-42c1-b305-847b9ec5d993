{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Explore: Nodes\n", ">    This example explores traversing the nodes as a tree and fundamental node\n", "    information including display name, access mode visibility, interface type,\n", "    and value.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Choose node properties to explore"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["explore_access = True\n", "explore_visibility = True\n", "explore_type = True\n", "explore_value = True"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 1 device(s)\n", "Device used in the example:\n", "\t('1c:0f:af:00:00:37', 'PHX050S-M', 'new', '169.254.56.0')\n"]}], "source": ["\"\"\"\n", "Waits for the user to connect a device before raising\n", "an exception if it fails\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "devices = None\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "nodemap = device.nodemap\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Explores: nodes\n", "> - retrieves display name\n", "> - retrieves node name\n", "> - retrieves accessibility\n", "> - retrieves visibility\n", "> - retrieves interface type\n", "> - retrieves value"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def explore_node(node, nodemap, depth):\n", "    # Get display name\n", "\tdisplay_name = node.display_name\n", "\t\n", "\t# Get node name\n", "\tnode_name = node.name\n", "\n", "\t# Retrieve accessibility\n", "\taccess_mode = node.access_mode\n", "\taccess_mode_str = str(access_mode)\n", "\n", "\t# Retrieve visibility\n", "\tvisibility = node.visibility\n", "\tvisibility_str = str(visibility)\n", "\n", "\t# Retrieve interface type\n", "\tinterface_type = node.interface_type\n", "\tinterface_type_str = str(interface_type)\n", "\n", "\t# Retrieve value if node is not a category or register node and is readable\n", "\tvalue = \"-\"\n", "\tif (node.is_readable and\n", "\t(node.interface_type.value != 7 and node.interface_type.value != 8)):\n", "\t\tvalue = str(node.value)\n", "\n", "\t# Check and print the desired information\n", "\taccess_mode_explored = None\n", "\tvisibility_explored = None\n", "\tinterface_type_explored = None\n", "\n", "\tif explore_access:\n", "\t\taccess_mode_explored = access_mode_str\n", "\n", "\tif explore_visibility:\n", "\t\tvisibility_explored = visibility_str\n", "\n", "\tif explore_type:\n", "\t\tinterface_type_explored = interface_type_str\n", "\n", "\tif explore_value:\n", "\t\tvalue_explored = value if len(value) < 30 else \"...\"\n", "\t\n", "\tdisplay_and_node_name = display_name + \" (\" + node_name + \")\"\n", "\n", "\tprint(\" \" * depth, '{:<{name_width}}{:<20}{:<25}{:<30}{:}'.format(display_and_node_name,\n", "\t\taccess_mode_explored, visibility_explored, interface_type_explored,\n", "\t\t value_explored, name_width = 90 - depth))\n", "\n", "\t# Explore category node children\n", "\tif node.interface_type.value == 8 and node.is_readable:\n", "\t\tchildren = node.features\n", "\t\tfor val in children:\n", "\t\t\texplore_node(nodemap.get_node(val), nodemap, depth+1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Initialize the Root node from all nodemaps, call the explore nodes function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemaps = [device.nodemap, device.tl_device_nodemap, device.tl_stream_nodemap,\n", "\t\t\t\tdevice.tl_interface_nodemap, system.tl_system_nodemap]\n", "nodemap_names = [\"Device Nodemap\", \"TL Device Nodemap\", \"TL Stream Nodemap\", \n", "                    \"TL Interface Nodemap\", \"TL System Nodemap\" ]\n", "print(\"Example Started\\n\")\n", "\n", "for i, nodemap in enumerate(nodemaps):\n", "    print(\"\\n\", nodemap_names[i])\n", "    explore_node(nodemap.get_node(\"Root\"), nodemap, 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()\n", "print(\"\\nExample Finished\")"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}