# -----------------------------------------------------------------------------
# Copyright (c) 2024, Lucid Vision Labs, Inc.
#
# THE SOFTWARE IS PROVIDED AS IS WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
# OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
# NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
# BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
# ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.
# -----------------------------------------------------------------------------

import time
from datetime import datetime
from arena_api.system import system
from arena_api.__future__.save import Writer
'''
Save: File Name Pattern
	This example demonstrates saving a set of images according to a file name
	pattern, which uses the <count> and <timestamp> tags to differentiate between
	saved images. The essential points of the example include setting the image
	writer up with a file name pattern and using the cascading I/O operator (<<)
	to update the timestamp and save each image.
'''
TAB1 = "  "
TAB2 = "    "

'''
Generator functions
'''
def time_update_function():
	'''
	This function acts like a generator.
		every time it is triggered, it returns the time as str in the format
		shown
	'''
	while True:
		now = datetime.now()
		yield now.strftime('%Y_%H_%M_%S_%f')

def get_vendor(device):
	'''
	Generator function for vendor
	'''
	while True:
		yield device.nodemap.get_node("DeviceVendorName").value


def get_model(device):
	'''
	Generator function for model name
	'''
	while True:
		yield device.nodemap.get_node("DeviceModelName").value


def get_serial(device):
	'''
	Generator function for serial number
	'''
	while True:
		yield device.nodemap.get_node("DeviceSerialNumber").value


def get_and_save_images(device, writer, num_images):

	# Starting the stream allocates buffers, which can be passed in as
	# an argument (default: 10), and begins filling them with data.
	# Buffers must later be requeued to avoid memory leaks.
	with device.start_stream(num_images):
		print(f'{TAB1}Stream started with {num_images} buffers')
		for i in range(num_images):
			# 'Device.get_buffer()' with no arguments returns only one buffer
			print(f'{TAB1}Get one buffer')
			buffer = device.get_buffer()

			# Print some info about the image in the buffer
			print(f'{TAB2}buffer received   | '
				f'Width = {buffer.width} pxl, '
				f'Height = {buffer.height} pxl, '
				f'Pixel Format = {buffer.pixel_format.name}')

			# print(f"{TAB2}Save image {i}")
			writer.save(buffer)
			print(f'{TAB1}Image saved {writer.saved_images[-1]}')

			# Requeue the image buffer
			device.requeue_buffer(buffer)


def create_devices_with_tries():
	# Connect a device
	tries = 0
	tries_max = 6
	sleep_time_secs = 10
	while tries < tries_max:  # Wait for device for 60 seconds
		devices = system.create_device()
		if not devices:
			print(
				f'{TAB1}Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '
				f'secs for a device to be connected!')
			for sec_count in range(sleep_time_secs):
				time.sleep(1)
				print(f'{TAB1}{sec_count + 1 } seconds passed ',
					'.' * sec_count, end='\r')
			tries += 1
		else:
			print(f'{TAB1}Created {len(devices)} device(s)')
			return devices
	else:
		raise Exception(f'{TAB1}No device found! Please connect a device and run '
						f'the example again.')


def example_entry_point():
	# Settings

	'''
	File name pattern
		File name patterns can use tags to easily customize your file names.
		Customizable tags can be added to a file name pattern and later set on
		the fly. Two tags, <count> and <datetime> have been built in to the save
		library. As seen below, <datetime> can take an argument to specify
		output. <count> also accepts arguments (local, path, and global) to
		specify what exactly is being counted.
	'''
	FILE_NAME_PATTERN = "images/py_save_file_name_pattern/<vendor>_<model>_<serial>\
_image<count>-<time>.bmp"

	# number of images to acquire and save
	NUM_IMAGES = 25

	# image timeout (milliseconds)
	TIMEOUT = 2000

	devices = create_devices_with_tries()
	device = system.select_device(devices)

	writer = Writer()

	'''
	Must register tags with writer before including them in pattern
		Must include a generator function
	'''
	print(f"{TAB1}Register tags")
	time_update_generator_from_func = time_update_function()
	writer.register_tag(name='time',
							generator=time_update_generator_from_func)
	writer.register_tag("vendor", generator=get_vendor(device))
	writer.register_tag("model", generator=get_model(device))
	writer.register_tag("serial", generator=get_serial(device))

	print(f"{TAB1}Set file name pattern")
	writer.pattern = FILE_NAME_PATTERN

	get_and_save_images(device, writer, NUM_IMAGES)
	system.destroy_device()
	print(f'{TAB1}Destroyed all created devices')


if __name__ == '__main__':
	print('\nWARNING:\nTHIS EXAMPLE MIGHT CHANGE THE DEVICE(S) SETTINGS!\n')
	print('Example started\n')
	example_entry_point()
	print('\nExample finished successfully')
