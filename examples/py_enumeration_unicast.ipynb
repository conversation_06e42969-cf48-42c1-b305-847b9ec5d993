{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Enumeration Unicast: Introduction\n", ">    This example introduces adding unicast device. This includes opening and\n", "   closing the system, updating and retrieving the list of devices, adding\n", "   unicast devices using the IP address for the device, and chekcing connections\n", "   of the devices\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### enumerates device(s)\n", "> - gets device list\n", "> - creates devices\n", "> - prints device information\n", "> - checks connection for device\n", "> - prints connection information"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def enumerate_devices():\n", "\tdevice_info = system.device_infos\n", "\tdevices = system.create_device()\n", "\t# for each device: print all attributes\n", "\tfor i, info in enumerate(device_info):\n", "\t\tprint(f\"\\tDevice {i}\")\n", "\t\tfor item in device_info[i]:\n", "\t\t\tprint(f\"\\t\\t{item}: {device_info[i][item]}\")\n", "\t\t# check connection and print connection info if device is connected\n", "\t\tif devices[i].is_connected():\n", "\t\t\tprint(\"\\t\\tconnection: True\")\n", "\t\t\tpixel_format = str(devices[i].nodemap.get_node(\"PixelFormat\").value)\n", "\t\t\tframe_rate = str(round(devices[i].nodemap.get_node(\"AcquisitionFrameRate\").value,1))\n", "\t\t\tprint(f\"\\t\\tpixel format: {pixel_format}\")\n", "\t\t\tprint(f\"\\t\\tframe rate: {frame_rate}fps\\n\")\n", "\t\telse:\n", "\t\t\tprint(\"\\t\\tconnection: False\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### adds unicast device(s)\n", "> - enumerates devices before adding unicast device\n", "> - takes ip for device to be added from user\n", "> - adds unicast discovery device(s)\n", "> - enumerates devices after adding unicast device(s)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('\\nExa<PERSON> started\\n')\n", "\n", "# enumerate devices before adding unicast device(s) \n", "print(\"\\tDevice list before adding unicast device(s)\")\n", "enumerate_devices()\n", "\n", "# stay in loop until exit\n", "while True:\n", "    ip = input(\"\\tInput IP for device to be added ('x' to exit)\\n\\t\")\n", "    # exit manually on 'x'\n", "    if ip.__eq__('x'):\n", "        print(f\"\\tSuccesfully exited\")\n", "        break\n", "    \n", "    '''\n", "    Add a unicast discovery device\n", "        registers an IP address for a device on a different subnet \n", "        than the host. Registered devices will be enumerated using \n", "        unicast discovery messages. The list of remote devices will \n", "        persist until they are removed using RemoveUnicastDiscoveryDevice() \n", "        or until the application terminates. Unicast discovery's will be \n", "        sent when UpdateDevices() is called.\n", "    '''\n", "    print(f\"\\tAdd device with ip: {ip}\\n\")\n", "    system.add_unicast_discovery_device(ip)\n", "\n", "# enumerate devices after adding unicast device(s) \n", "print(\"\\tDevice list after adding unicast device(s)\")\n", "enumerate_devices()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()\n", "print('\\n<PERSON><PERSON><PERSON> finished successfully')"]}], "metadata": {"kernelspec": {"display_name": "Python 3.10.7 ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "9a831188c03c7caeebd3251c40fdaa86517461ff286f09cf3194138e4e5369b6"}}}, "nbformat": 4, "nbformat_minor": 2}