{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Scheduled Action Commands\n", "> This example introduces scheduling action commands on multiple cameras. The device settings are configured to allow each device to trigger a single image using action commands. The system is prepared to receive an action command and the devices' PTP relationships are synchronized. This allows actions commands to be fired across all devices, resulting in simultaneously acquired images with synchronized timestamps. Depending on the initial PTP state of each camera, it can take about 40 seconds for all devices to autonegotiate."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "# Exposure time to set in microseconds\n", "EXPOSURE_TIME_TO_SET_US = 500.0\n", "# Delta time in nanoseconds to set action command\n", "DELTA_TIME_NS = 1000000000\n", "# Creating global system nodemap\n", "sys_tl_map = system.tl_system_nodemap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def store_initial(device):\n", "    dev_map = device.nodemap\n", "    exposure_auto_initial = dev_map['ExposureAuto'].value\n", "    trigger_source_initial = dev_map['TriggerSource'].value\n", "    action_uncond_initial = dev_map['ActionUnconditionalMode'].value\n", "    action_selector_initial = dev_map['ActionSelector'].value\n", "    action_group_key_initial = dev_map['ActionGroupKey'].value\n", "    action_group_mask_initial = dev_map['ActionGroupMask'].value\n", "    transfer_control_mode_initial = dev_map['TransferControlMode'].value\n", "    ptp_enable_initial = dev_map['PtpEnable'].value\n", "    action_command_dev_key_initial = sys_tl_map['ActionCommandDeviceKey'].value\n", "    action_command_grp_key_initial = sys_tl_map['ActionCommandGroupKey'].value\n", "    action_command_grp_mask_initial = sys_tl_map['ActionCommandGroupMask'].value\n", "    action_command_target_ip_initial = sys_tl_map['ActionCommandTargetIP'].value\n", "\n", "    return [ exposure_auto_initial, trigger_source_initial, action_uncond_initial, \n", "    action_selector_initial, action_group_key_initial, action_group_mask_initial,\n", "    transfer_control_mode_initial, ptp_enable_initial,\n", "    action_command_dev_key_initial, action_command_grp_key_initial, \n", "    action_command_grp_mask_initial, action_command_target_ip_initial ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def restore_initial(initial_vals, device):\n", "    dev_map = device.nodemap\n", "\n", "    dev_map['ExposureAuto'].value = initial_vals[0]\n", "    dev_map['TriggerSource'].value = initial_vals[1]\n", "    dev_map['ActionUnconditionalMode'].value = initial_vals[2]\n", "    dev_map['ActionSelector'].value = initial_vals[3]\n", "    dev_map['ActionGroupKey'].value = initial_vals[4]\n", "    dev_map['ActionGroupMask'].value = initial_vals[5]\n", "    dev_map['TransferControlMode'].value = initial_vals[6]\n", "    dev_map['PtpEnable'].value = initial_vals[7]\n", "    sys_tl_map['ActionCommandDeviceKey'].value = initial_vals[8]\n", "    sys_tl_map['ActionCommandGroupKey'].value = initial_vals[9]\n", "    sys_tl_map['ActionCommandGroupMask'].value = initial_vals[10]\n", "    sys_tl_map['ActionCommandTargetIP'].value = initial_vals[11]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Use max supported packet size. We use transfer control to ensure that\n", "only one camera is transmitting at a time.\n", "\"\"\"\n", "for device in devices:\n", "    device.tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "print(f'{TAB1}Stream Auto Negotiate Packet Size Enabled :'\n", "        f''' {device.tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value}''')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Manually set exposure time\n", "    In order to get synchronized images, the exposure time\n", "    must be synchronized.\n", "\"\"\"\n", "dev_map = device.nodemap\n", "nodes = dev_map.get_node(['ExposureAuto', 'ExposureTime'])\n", "\n", "nodes['ExposureAuto'].value = 'Off'\n", "\n", "exposure_time_node = nodes['ExposureTime']\n", "\n", "min_device_exposure_time = exposure_time_node.min\n", "max_device_exposure_time = exposure_time_node.max\n", "\n", "if (EXPOSURE_TIME_TO_SET_US >= min_device_exposure_time and\n", "        EXPOSURE_TIME_TO_SET_US <= max_device_exposure_time):\n", "    exposure_time_node.value = EXPOSURE_TIME_TO_SET_US\n", "else:\n", "    exposure_time_node.value = min_device_exposure_time\n", "\n", "print(f'''{TAB1}Exposure Time : {dev_map['ExposureTime'].value}''')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Enable trigger mode and set source to action\n", "To trigger a single image using action commands, trigger mode must\n", "be enabled, the source set to an action command, and the selector\n", "set to the start of a frame.\n", "\"\"\"\n", "for device in devices:\n", "    dev_map = device.nodemap\n", "\n", "    dev_map['TriggerMode'].value = 'On'\n", "    dev_map['TriggerSource'].value = 'Action0'\n", "    dev_map['TriggerSelector'].value = 'FrameStart'\n", "\n", "print(f'''{TAB1}Trigger Source : {dev_map['TriggerSource'].value}''')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Prepare the device to receive an action command\n", "Action unconditional mode allows a camera to accept action from an\n", "application without write access. The device key, group key, and\n", "group mask must match similar settings in the system's TL node map.\n", "\"\"\"\n", "for device in devices:\n", "    dev_map = device.nodemap\n", "\n", "    dev_map['ActionUnconditionalMode'].value = 'On'\n", "    dev_map['ActionSelector'].value = 0\n", "    dev_map['ActionDeviceKey'].value = 1\n", "    dev_map['ActionGroupKey'].value = 1\n", "    dev_map['ActionGroupMask'].value = 1\n", "\n", "print(f'{TAB1}Action commands: prepared')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Enable user controlled transfer control\n", "Synchronized cameras will begin transmiting images at the same time.\n", "To avoid missing packets due to collisions, we will use transfer\n", "control to control when each camera transmits the image.\n", "\"\"\"\n", "for device in devices:\n", "    dev_map = device.nodemap\n", "\n", "    dev_map['TransferControlMode'].value = 'UserControlled'\n", "    dev_map['TransferOperationMode'].value = 'Continuous'\n", "    dev_map['TransferStop'].execute()\n", "\n", "print(f'{TAB1}Transfer Control: prepared')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Synchronize devices by enabling PTP\n", "Enabling PTP on multiple devices causes them to negotiate amongst\n", "themselves so that there is a single master device while all the\n", "rest become slaves. The slaves' clocks all synchronize to the\n", "master's clock.\n", "\"\"\"\n", "for device in devices:\n", "    device.nodemap['PtpEnable'].value = True\n", "\n", "print(f'''{TAB1}PTP Enabled : {device.nodemap['PtpEnable'].value}\\n''')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Prepare the system to broadcast an action command.\n", "The device key, group key, group mask, and target IP must all match\n", "similar settings in the devices' node maps. The target IP acts as a mask.\n", "\"\"\"\n", "sys_tl_map['ActionCommandDeviceKey'].value = 1\n", "sys_tl_map['ActionCommandGroupKey'].value = 1\n", "sys_tl_map['ActionCommandGroupMask'].value = 1\n", "sys_tl_map['ActionCommandTargetIP'].value = 0xFFFFFFFF  # 0.0.0.0\n", "\n", "print(f'{TAB1}System: prepared')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def synchronize_cameras():\n", "    \"\"\"\n", "    Wait for devices to negotiate their PTP relationship\n", "    Before starting any PTP-dependent actions, it is important to\n", "    wait for the devices to complete their negotiation; otherwise,\n", "    the devices may not yet be synced. Depending on the initial PTP\n", "    state of each camera, it can take about 40 seconds for all devices\n", "    to autonegotiate. Below, we wait for the PTP status of each device until\n", "    there is only one 'Master' and the rest are all 'Slaves'.\n", "    During the negotiation phase, multiple devices may initially come up as\n", "    Master so we will wait until the ptp negotiation completes.\n", "    \"\"\"\n", "    print(f'{TAB1}Waiting for PTP Master/Slave negotiation. '\n", "          f'This can take up to about 40s')\n", "\n", "    while True:\n", "        master_found = False\n", "        restart_sync_check = False\n", "\n", "        for device in devices:\n", "\n", "            ptp_status = device.nodemap['PtpStatus'].value\n", "\n", "            # User might uncomment this line for debugging\n", "            '''\n", "            print(f'{device} is {ptp_status}')\n", "            '''\n", "\n", "            # Find master\n", "            if ptp_status == 'Master':\n", "                if master_found:\n", "                    restart_sync_check = True\n", "                    break\n", "                master_found = True\n", "\n", "            # Restart check until all slaves found\n", "            elif ptp_status != 'Slave':\n", "                restart_sync_check = True\n", "                break\n", "\n", "        # A single master was found and all remaining cameras are slaves\n", "        if not restart_sync_check and master_found:\n", "            break\n", "\n", "        time.sleep(1)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def schedule_action_command():\n", "    \"\"\"\n", "    Set up timing and broadcast action command\n", "    Action commands must be scheduled for a time in the future.\n", "    This can be done by grabbing the PTP time from a device, adding\n", "    a delta to it, and setting it as an action command's execution time.\n", "    \"\"\"\n", "    device = system.select_device(devices)\n", "\n", "    device.nodemap['PtpDataSetLatch'].execute()\n", "    ptp_data_set_latch_value = device.nodemap['PtpDataSetLatchValue'].value\n", "\n", "    print(f'{TAB2}Set action command to {DELTA_TIME_NS} nanoseconds from now')\n", "\n", "    sys_tl_map['ActionCommandExecuteTime'].value \\\n", "        = ptp_data_set_latch_value + DELTA_TIME_NS\n", "\n", "    print(f'{TAB2}Fire action command')\n", "    \"\"\"\n", "    Fire action command\n", "    Action commands are fired and broadcast to all devices, but\n", "    only received by the devices matching desired settings.\n", "    \"\"\"\n", "    sys_tl_map['ActionCommandFireCommand'].execute()\n", "\n", "    # Grab image from cameras\n", "    for device in devices:\n", "\n", "        # Transfer Control\n", "        device.nodemap['TransferStart'].execute()\n", "\n", "        buffer = device.get_buffer(timeout=2000)\n", "\n", "        device.nodemap['TransferStop'].execute()\n", "\n", "        print(f'{TAB1}{TAB2}Received image from {device}'\n", "              f' | Timestamp: {buffer.timestamp_ns} ns')\n", "\n", "        device.requeue_buffer(buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "// Demonstrates action commands\n", "// (1) manually sets exposure, trigger and action command settings\n", "// (2) prepares devices for action commands\n", "// (3) synchronizes devices and fire action command\n", "// (4) retrieves images with synchronized timestamps\n", "\"\"\"\n", "\n", "initial_vals_arr = []\n", "for device in devices:\n", "    # Get device stream nodemap\n", "    tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "    # Enable stream auto negotiate packet size\n", "    tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "    # Enable stream packet resend\n", "    tl_stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "    # Store initial values\n", "    initial_vals = store_initial(device)\n", "    initial_vals_arr.append(initial_vals)\n", "\n", "synchronize_cameras()\n", "\n", "print(f'{TAB1}Start stream')\n", "for device in devices:\n", "    device.start_stream()\n", "\"\"\"\n", "Compare timestamps\n", "Scheduling action commands amongst PTP synchronized devices\n", "results synchronized images with synchronized timestamps.\n", "\"\"\"\n", "schedule_action_command()\n", "\n", "for i in range(0, devices.__len__()):\n", "    restore_initial(initial_vals_arr.pop(0), devices[i])\n", "\n", "print(f'{TAB1}Stop stream and destroy all devices')\n", "system.destroy_device()\n"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}