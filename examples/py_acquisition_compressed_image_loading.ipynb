{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import os\n", "import time\n", "\n", "from arena_api.__future__.save import Writer\n", "from arena_api.enums import PixelFormat\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.__future__.save import _xWriter, _xReader\n", "\n", "RAW_FILE_PATH = \"images\\\\py_acquisition_compressed_image_loading\\\\CompressedImage\"\n", "PNG_FILE_PATH = \"images\\\\py_acquisition_compressed_image_loading\\\\DecompressedImage\"\n", "\n", "TAB1 = \"  \"\n", "TAB2 = \"    \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: Compressed Image Loading\n", ">\tThis example demonstrates how to handle compressed image data, specifically\n", "\tloading and processing from raw data files using the Arena SDK. The example\n", "\tincludes steps to configure the camera, acquire a compressed image, save\n", "\tthe raw file, load the raw file, decompress the data, and save the decompressed\n", "\timage."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 1 device(s)\n", "  Only one device detected:  ('1c:0f:af:3f:55:a4', 'PHX064S-M', '', '169.254.165.85')\n", "    Automatically selecting this device.\n", "Device used in the example:\n", "\t('1c:0f:af:3f:55:a4', 'PHX064S-M', '', '169.254.165.85')\n"]}], "source": ["tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "\tdevices = system.create_device()\n", "\tif not devices:\n", "\t\tprint(\n", "\t\t\tf'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "\t\t\tf'secs for a device to be connected!')\n", "\t\tfor sec_count in range(sleep_time_secs):\n", "\t\t\ttime.sleep(1)\n", "\t\t\tprint(f'{sec_count + 1 } seconds passed ',\n", "\t\t\t\t'.' * sec_count, end='\\r')\n", "\t\ttries += 1\n", "\telse:\n", "\t\tprint(f'Created {len(devices)} device(s)')\n", "\t\tdevice = system.select_device(devices)\n", "\t\tbreak\n", "else:\n", "\traise Exception(f'No device found! Please connect a device and run '\n", "\t\t\t\t\tf'the example again.')\n", "\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Enable stream auto negotiate packet size\n", ">   Setting the stream packet size is done before starting the stream.\n", "\tSetting the stream to automatically negotiate packet size instructs the\n", "\tcamera to receive the largest packet size that the system will allow.\n", "\tThis generally increases frame rate and results in fewer interrupts per\n", "\timage, thereby reducing CPU load on the host system. Ethernet settings\n", "\tmay also be manually changed to allow for a larger packet size."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Enable stream packet resend\n", ">   Enable stream packet resend before starting the stream. Images are sent\n", "\tfrom the camera to the host in packets using UDP protocol, which\n", "\tincludes a header image number, packet number, and timestamp\n", "\tinformation. If a packet is missed while receiving an image, a packet\n", "\tresend is requested and this information is used to retrieve and\n", "\tredeliver the missing packet in the correct order."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Set features before streaming\n", ">   Set PixelFormat to QOI_Mono8"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initial PixelFormat value: QOI_Mono8\n"]}], "source": ["# Get device nodemap\n", "nodemap = device.nodemap\n", "\n", "# Iterate through the PixelFormat enum and check for \"QOI_Mono8\"\n", "node = nodemap.get_node('PixelFormat')\n", "entries = node.enumentry_names\n", "found = False\n", "for e in entries:\n", "\tif (e == 'QOI_Mono8'):\n", "\t\tfound = True\n", "\t\tbreak\n", "\n", "if (not found):\n", "\tprint(f'QOI_Mono8 is not available in the PixelFormat enumeration for this camera.\\n')\n", "\n", "# Get initial node values in order to return their values at the end of the example\n", "pixel_format_initial = node.value\n", "print(f\"Initial PixelFormat value: {pixel_format_initial}\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Setting pixel format to QOI_Mono8\n", "\n"]}], "source": ["# - PixelFormat to QOI_Mono8\n", "pixel_format_setting = 'QOI_Mono8'\n", "print(f'Setting pixel format to { pixel_format_setting }\\n')\n", "node.value = pixel_format_setting"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Start stream and grab images\n", ">   - Starting stream with one buffer, grabbing one image.\n", ">   - Printing the uncompressed size and compressed size for comparison.\n", ">   - Saving compresed image (in PixelFormat QOI_Mono8) in its raw state. \n", ">\t- Loading saved image and then decompressing image to Mono8.\n", ">   - Must requeue buffer in order to prevent memory leaks."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def save_compressed_image(buffer, index):\n", "\n", "\tprint(f'{TAB1}Save compressed input image data to', end=' ')\n", "\n", "\tfilename = RAW_FILE_PATH + str(index) + \".raw\"\n", "\n", "\t# Save function for .raw file\n", "\t_xWriter.SaveRawData(filename, buffer.compressed_image_pdata, buffer.size_filled)\n", "\tprint(f'{filename}')"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stream started with 1 buffer\n", "Get compressed image 0\n", "  Compressed image 0 size: 2405640 bytes\n", "  Save compressed input image data to images\\py_acquisition_compressed_image_loading\\CompressedImage0.raw\n", "Get compressed image 1\n", "  Compressed image 1 size: 2408840 bytes\n", "  Save compressed input image data to images\\py_acquisition_compressed_image_loading\\CompressedImage1.raw\n", "Get compressed image 2\n", "  Compressed image 2 size: 2410492 bytes\n", "  Save compressed input image data to images\\py_acquisition_compressed_image_loading\\CompressedImage2.raw\n", "Get compressed image 3\n", "  Compressed image 3 size: 2409100 bytes\n", "  Save compressed input image data to images\\py_acquisition_compressed_image_loading\\CompressedImage3.raw\n", "Get compressed image 4\n", "  Compressed image 4 size: 2407000 bytes\n", "  Save compressed input image data to images\\py_acquisition_compressed_image_loading\\CompressedImage4.raw\n", "Get compressed image 5\n", "  Compressed image 5 size: 2408012 bytes\n", "  Save compressed input image data to images\\py_acquisition_compressed_image_loading\\CompressedImage5.raw\n", "Get compressed image 6\n", "  Compressed image 6 size: 2405636 bytes\n", "  Save compressed input image data to images\\py_acquisition_compressed_image_loading\\CompressedImage6.raw\n", "Get compressed image 7\n", "  Compressed image 7 size: 2406756 bytes\n", "  Save compressed input image data to images\\py_acquisition_compressed_image_loading\\CompressedImage7.raw\n", "Get compressed image 8\n", "  Compressed image 8 size: 2406192 bytes\n", "  Save compressed input image data to images\\py_acquisition_compressed_image_loading\\CompressedImage8.raw\n", "Get compressed image 9\n", "  Compressed image 9 size: 2406576 bytes\n", "  Save compressed input image data to images\\py_acquisition_compressed_image_loading\\CompressedImage9.raw\n"]}], "source": ["with device.start_stream(1):\n", "\n", "\tprint(f'Stream started with 1 buffer')\n", "\n", "\tfor i in range(0, 10):\n", "\t\t# Get compressed image\n", "\t\tprint(f'Get compressed image {i}')\n", "\t\tbuffer = device.get_buffer()\n", "\n", "\t\t# Get compressed image size\n", "\t\tcompressed_image_size = buffer.size_filled\n", "\t\tprint(f'{TAB1}Compressed image {i} size: {compressed_image_size} bytes')\n", "\n", "\t\t# Save the raw image\n", "\t\tsave_compressed_image(buffer, i)\n", "\n", "\t\t# Re-queue the image buffer\n", "\t\tdevice.requeue_buffer(buffer)"]}, {"cell_type": "markdown", "metadata": {}, "source": [">\tStops stream and prevents memory leaks"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stopping stream\n"]}], "source": ["# Stop stream\n", "print(f'Stopping stream')\n", "device.stop_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": [">\tLoads image and decompresses it, saving it as a file"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Load and process image\n", "  Decompressed image 0 size: 6291456 bytes\n", "  Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_loading\\DecompressedImage0.png\n", "  Decompressed image 1 size: 6291456 bytes\n", "  Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_loading\\DecompressedImage1.png\n", "  Decompressed image 2 size: 6291456 bytes\n", "  Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_loading\\DecompressedImage2.png\n", "  Decompressed image 3 size: 6291456 bytes\n", "  Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_loading\\DecompressedImage3.png\n", "  Decompressed image 4 size: 6291456 bytes\n", "  Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_loading\\DecompressedImage4.png\n", "  Decompressed image 5 size: 6291456 bytes\n", "  Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_loading\\DecompressedImage5.png\n", "  Decompressed image 6 size: 6291456 bytes\n", "  Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_loading\\DecompressedImage6.png\n", "  Decompressed image 7 size: 6291456 bytes\n", "  Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_loading\\DecompressedImage7.png\n", "  Decompressed image 8 size: 6291456 bytes\n", "  Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_loading\\DecompressedImage8.png\n", "  Decompressed image 9 size: 6291456 bytes\n", "  Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_loading\\DecompressedImage9.png\n", "Time to decompress 10 images (sec) = 1.5047881603240967\n"]}], "source": ["print(f'Load and process image')\n", "\n", "begin = time.time()\n", "\n", "for i in range(0, 10):\n", "\t\n", "\tin_filename = RAW_FILE_PATH + str(i) + \".raw\"\n", "\n", "\t# Read raw file size\n", "\tsize = os.path.getsize(in_filename);\n", "\n", "\t# Read file\n", "\tpdata = _xReader.LoadRawData(in_filename, size)\n", "\n", "\t# Load file into compressed image\n", "\tcompressed_image = BufferFactory.create_compressed_image(pdata, size, PixelFormat.QOI_Mono8)\n", "\n", "\t# Decompress image\n", "\tdecompressed_image = BufferFactory.decompress_image(compressed_image)\n", "\n", "\t# Get and print size for comparison\n", "\tdecompressed_image_size = decompressed_image.size_filled\n", "\tprint(f'{TAB1}Decompressed image {i} size: {decompressed_image_size} bytes')\n", "\n", "\tout_filename = PNG_FILE_PATH + str(i) + \".png\"\n", "\n", "\t# Save the decompressed image\n", "\twriter = Writer.from_buffer(decompressed_image)\n", "\twriter.pattern = out_filename\n", "\twriter.save(decompressed_image)\n", "\tprint(f'{TAB1}Image saved {writer.saved_images[-1]}')\n", "\n", "\t# Destroy buffer to avoid memory leaks\n", "\tBufferFactory.destroy(decompressed_image)\n", "\tBufferFactory.destroy_compressed_image(compressed_image)\n", "\n", "end = time.time()\n", "print(f'Time to decompress 10 images (sec) = {end - begin}')"]}, {"cell_type": "markdown", "metadata": {}, "source": [">\tResets node values to initial values"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["node.value = pixel_format_initial"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Destroyed all created devices\n"]}], "source": ["system.destroy_device()\n", "print(f'Destroyed all created devices')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ve_win_dev_py64", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}