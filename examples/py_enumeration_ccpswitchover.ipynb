{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Enumeration: CcpSwitchover\n", ">    This example introduces device enumeration with the ability to hand over control\n", "    to another process. This includes opening and closing the system, updating and \n", "    retrieving the list of devices, searching for devices, and creating and destroying\n", "    a device. In this example, we will also set a special key to the device that another \n", "    process can use to aquire control of the device when running the example for the first\n", "    time. Running the example a second time while the first instance is still running will \n", "    try to use the special key to gain control of the device. \n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from arena_api.system import system\n", "import time\n", "\n", "TAB1 = \"  \"\n", "TAB2 = \"    \""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 1 device(s)\n", "\n"]}], "source": ["'''\n", "Create devices\n", "'''\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "device_infos = None\n", "\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    device_infos = system.device_infos\n", "    if not device_infos:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1} seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(device_infos)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def has_control(device):\n", "\t'''\n", "\tCheck if device has control\n", "\t'''\n", "\tdevice_access_status = device.tl_device_nodemap['DeviceAccessStatus'].value\n", "\treturn device_access_status == \"ReadWrite\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### enumerates device(s)\n", "Demonstrated enumeration\n", "> - gets device list\n", "> - save first serial number to demonstrate search\n", "> - prints device information\n", "> - demonstrates device search\n", "> - create device\n", "> - if running first instance will set a special key\n", "> - if running second instance will use key to aquire control\n", "> - cleans up"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'420'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["serial_to_find = device_infos[0]['serial']\n", "serial_to_find"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Information for device 0:\n", "  Vendor: Lucid Vision Labs; model: PHX050S-C; serial: 420; MAC: 1c:0f:af:12:53:65; ip: ***********\n"]}], "source": ["for i, device_info in enumerate(device_infos): \n", "    print(f\"Information for device {i}:\")\n", "    vendor = device_info['vendor']\n", "    model = device_info['model']\n", "    serial = device_info['serial']\n", "    macStr = device_info['mac']\n", "    ipStr = device_info['ip']\n", "    print(f\"{TAB1}Vendor: {vendor}; model: {model}; serial: {serial}; MAC: {macStr}; ip: {ipStr}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for device with serial number: 420\n", "<PERSON><PERSON> found!\n", "Creating device\n", "Set the switchover key. Continue\n", "Destroyed all created devices\n"]}], "source": ["matching_device_info = None\n", "\n", "print(f\"Searching for device with serial number: {serial_to_find}\")\n", "for device_info in device_infos:\n", "    if device_info['serial'] == serial_to_find:\n", "        matching_device_info = device_info\n", "        break\n", "\n", "if matching_device_info:\n", "    print(f\"<PERSON><PERSON> found!\")\n", "    '''\n", "    Create device\n", "        Create device in order to configure it and grab images. A device can only be created\n", "        once per process, and can only be opened with read-write access once. \n", "    '''\n", "\n", "    print(f\"Creating device\")\n", "    devices = system.create_device(matching_device_info)\n", "    device = devices[0]\n", "\n", "    # static key used to acquire control between applications\n", "    switchoverKey = 0x1234\n", "    device.tl_device_nodemap['CcpSwitchoverKey'].value = switchoverKey\n", "    \n", "    if has_control(device):\n", "        print(f'Set the switchover key. Continue')\n", "    else:\n", "        device.tl_device_nodemap['DeviceAccessStatus'].value = \"ReadWrite\"\n", "        if has_control(device):\n", "            print(f\"Create device succeeded with acquiring control\")\n", "        else:\n", "            print(f\"Create device failed to acquire control\")\n", "            \n", "    system.destroy_device()\n", "    print(f\"Destroyed all created devices\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}