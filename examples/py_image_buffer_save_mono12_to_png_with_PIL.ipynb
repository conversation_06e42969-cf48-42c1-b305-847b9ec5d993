{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import ctypes  # ctypes.cast(), ctypes.POINTER(), ctypes.c_ushort\n", "import os  # os.getcwd()\n", "import time\n", "from pathlib import Path\n", "\n", "import numpy as np  # pip install numpy\n", "from PIL import Image as PIL_Image  # pip install Pillow\n", "\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save: Mono12 to PNG\n", ">\tThis example demonstrates saving the images retrieved\n", "\tby the camera in an easy to share PNG format using PIL.\n", "\tThis includes configuring camera nodes, getting and converting\n", "\tthe buffers into a numpy array, and saving the numpy array \n", "\tusing PIL as a PNG image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Setup stream values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get device stream nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Configure camera nodes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes = device.nodemap.get_node(['Width', 'Height', 'PixelFormat'])\n", "\n", "print('Setting Width to its maximum value')\n", "nodes['Width'].value = nodes['Width'].max\n", "\n", "print('Setting Height to its maximum value')\n", "height = nodes['Height']\n", "height.value = height.max"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "##### Set pixel format to Mono12"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pixel_format_name = 'Mono12'\n", "print(f'Setting Pixel Format to {pixel_format_name}')\n", "nodes['PixelFormat'].value = pixel_format_name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### demonstrates Save: Mono12 to PNG\n", "> - setup stream nodes\n", "> - configure camera nodes\n", "> - get buffer data as c pointers\n", "> - convert the data to numpy array using np.ctypeslib\n", "> - convert numpy array to bytes\n", "> - save the byte array as a png"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Starting stream')\n", "device.start_stream(1)\n", "\n", "print('Grabbing an image buffer')\n", "image_buffer = device.get_buffer()\n", "\n", "print(f' Width X Height = '\n", "    f'{image_buffer.width} x {image_buffer.height}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Saving image with PIL\n", "> To save an image Pi<PERSON> needs an array that is shaped to\n", "    (height, width). In order to obtain such an array we use numpy library"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Buffer Config\n", "> - Buffer.pdata is a (uint8, ctypes.c_ubyte)\n", "> - Buffer.data is a list of elements each represents one byte. <br>\n", "> Since Mono12 uses 16Bits (2 bytes), It is easier to user Buffer.pdata\n", "    over Buffer.data. <br> Buffer.pdata must be cast to (uint16, c_ushort)\n", "    so every element in the array would represent one pixel."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Converting image buffer to a numpy array')\n", "\n", "pdata_as16 = ctypes.cast(image_buffer.pdata,\n", "                        ctypes.POINTER(ctypes.c_ushort))\n", "nparray_reshaped = np.ctypeslib.as_array(\n", "    pdata_as16,\n", "    (image_buffer.height, image_buffer.width))\n", "\n", "# Saving --------------------------------------------------------------\n", "print('Saving image')\n", "\n", "png_name = f'from_{pixel_format_name}_to_png_with_pil.png'"]}, {"cell_type": "markdown", "metadata": {}, "source": [">   These steps are due to a bug in Pillow saving 16bits png images\n", "    more : https://github.com/python-pillow/Pillow/issues/2970"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nparray_reshaped_as_bytes = nparray_reshaped.tobytes()\n", "png_array = PIL_Image.new('I', nparray_reshaped.T.shape)\n", "png_array.frombytes(nparray_reshaped_as_bytes, 'raw', 'I;16')\n", "\n", "png_array.save(png_name)\n", "print(f'Saved image path is: {Path(os.getcwd()) / png_name }')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Requeue buffer and stop stream to avoid memory leaks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.requeue_buffer(image_buffer)\n", "device.stop_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}