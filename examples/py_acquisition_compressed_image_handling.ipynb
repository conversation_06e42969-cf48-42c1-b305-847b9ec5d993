{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "from arena_api.__future__.save import Writer\n", "from arena_api.enums import PixelFormat\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.__future__.save import _xWriter"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: Compressed Image Handling\n", ">\tThis example demonstrates how to acquire and process compressed image data\n", "\tfrom the camera using the Arena SDK. The example includes\n", "\tsteps to configure the camera, acquire a compressed image, process the\n", "\timage to decompress it, and save both the raw input and decompressed images."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 1 device(s)\n", "  Only one device detected:  ('1c:0f:af:3f:55:a4', 'PHX064S-M', '', '169.254.165.85')\n", "    Automatically selecting this device.\n", "Device used in the example:\n", "\t('1c:0f:af:3f:55:a4', 'PHX064S-M', '', '169.254.165.85')\n"]}], "source": ["tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "\tdevices = system.create_device()\n", "\tif not devices:\n", "\t\tprint(\n", "\t\t\tf'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "\t\t\tf'secs for a device to be connected!')\n", "\t\tfor sec_count in range(sleep_time_secs):\n", "\t\t\ttime.sleep(1)\n", "\t\t\tprint(f'{sec_count + 1 } seconds passed ',\n", "\t\t\t\t'.' * sec_count, end='\\r')\n", "\t\ttries += 1\n", "\telse:\n", "\t\tprint(f'Created {len(devices)} device(s)')\n", "\t\tdevice = system.select_device(devices)\n", "\t\tbreak\n", "else:\n", "\traise Exception(f'No device found! Please connect a device and run '\n", "\t\t\t\t\tf'the example again.')\n", "\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Enable stream auto negotiate packet size\n", ">   Setting the stream packet size is done before starting the stream.\n", "\tSetting the stream to automatically negotiate packet size instructs the\n", "\tcamera to receive the largest packet size that the system will allow.\n", "\tThis generally increases frame rate and results in fewer interrupts per\n", "\timage, thereby reducing CPU load on the host system. Ethernet settings\n", "\tmay also be manually changed to allow for a larger packet size."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Enable stream packet resend\n", ">   Enable stream packet resend before starting the stream. Images are sent\n", "\tfrom the camera to the host in packets using UDP protocol, which\n", "\tincludes a header image number, packet number, and timestamp\n", "\tinformation. If a packet is missed while receiving an image, a packet\n", "\tresend is requested and this information is used to retrieve and\n", "\tredeliver the missing packet in the correct order."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Set features before streaming\n", ">   Set PixelFormat to QOI_Mono8"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initial PixelFormat value: QOI_Mono8\n"]}], "source": ["# Get device nodemap\n", "nodemap = device.nodemap\n", "\n", "# Iterate through the PixelFormat enum and check for \"QOI_Mono8\"\n", "node = nodemap.get_node('PixelFormat')\n", "entries = node.enumentry_names\n", "found = False\n", "for e in entries:\n", "\tif (e == 'QOI_Mono8'):\n", "\t\tfound = True\n", "\t\tbreak\n", "\n", "if (not found):\n", "\tprint(f'QOI_Mono8 is not available in the PixelFormat enumeration for this camera.\\n')\n", "\n", "# Get initial node values in order to return their values at the end of the example\n", "pixel_format_initial = node.value\n", "print(f\"Initial PixelFormat value: {pixel_format_initial}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Setting pixel format to QOI_Mono8\n", "\n"]}], "source": ["# - PixelFormat to QOI_Mono8\n", "pixel_format_setting = 'QOI_Mono8'\n", "print(f'Setting pixel format to { pixel_format_setting }\\n')\n", "node.value = pixel_format_setting"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Start stream and grab images\n", ">   - Starting stream with one buffer, grabbing one image.\n", ">   - Printing the uncompressed size and compressed size for comparison.\n", ">   - Saving compresed image (in PixelFormat QOI_Mono8) in its raw state and decompressing image to Mono8 and saving it.\n", ">   - Must requeue buffer in order to prevent memory leaks."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stream started with 1 buffer\n", "\n", "Get one image\n"]}], "source": ["device.start_stream(1)\n", "\n", "print(f'Stream started with 1 buffer\\n')\n", "\n", "# Get compressed image\n", "print(f'Get one image')\n", "buffer = device.get_buffer()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["QOI_Mono8 compressed image size: 3871460 bytes\n", "\n"]}], "source": ["# Get compressed image size\n", "compressed_image_size = buffer.size_filled\n", "print(f'QOI_Mono8 compressed image size: {compressed_image_size} bytes\\n')"]}, {"cell_type": "markdown", "metadata": {}, "source": [">\tTakes compresed image and saves the raw data"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saving compressed image\n", "Image saved images\\py_acquisition_compressed_image_handling\\CompressedImage.raw\n"]}], "source": ["print(f'Saving compressed image')\n", "\n", "# Set raw file save location\n", "filename = r'images\\py_acquisition_compressed_image_handling\\CompressedImage.raw'\n", "\n", "# Save function for .raw file\n", "_xWriter.SaveRawData(filename, buffer.compressed_image_pdata, buffer.size_filled)\n", "print(f'Image saved {filename}')"]}, {"cell_type": "markdown", "metadata": {}, "source": [">\tDecompresses image and saves it as a file"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Decompressing to Mono8\n", "Decompressed image\n", "Mono8 image size: 6291456 bytes\n", "\n", "Image saved c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_acquisition_compressed_image_handling\\DecompressedImage.png\n"]}], "source": ["print(f'Decompressing to Mono8')\n", "\n", "# Decompress image\n", "png_buffer = BufferFactory.decompress_image(buffer)\n", "print(f'Decompressed image')\n", "\n", "# Get and print size for comparison\n", "compressed_image_size = png_buffer.size_filled\n", "print(f'Mono8 image size: {compressed_image_size} bytes\\n')\n", "\n", "# Save the decompressed image\n", "writer = Writer.from_buffer(png_buffer)\n", "writer.pattern = r'images/py_acquisition_compressed_image_handling/DecompressedImage.png'\n", "writer.save(png_buffer)\n", "print(f'Image saved {writer.saved_images[-1]}')"]}, {"cell_type": "markdown", "metadata": {}, "source": [">\tStops stream and prevents memory leaks"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stopping stream\n"]}], "source": ["# Destroy converted buffer and requeues buffer to avoid memory leaks\n", "BufferFactory.destroy(png_buffer)\n", "device.requeue_buffer(buffer)\n", "\n", "# Stop stream\n", "print(f'Stopping stream')\n", "device.stop_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": [">\tResets node values to initial values"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["node.value = pixel_format_initial"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Destroyed all created devices\n"]}], "source": ["system.destroy_device()\n", "print(f'Destroyed all created devices')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ve_win_dev_py64", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}