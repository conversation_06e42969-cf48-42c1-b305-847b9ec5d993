{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Exposure: For High Dynamic Range\n", ">    This example demonstrates dynamically updating the exposure time in\n", "    order to grab images appropriate for high dynamic range (or HDR)\n", "    imaging. HDR images can be created by combining a number of images\n", "    acquired at various exposure times. This example demonstrates\n", "    grabbing three images for this purpose, without the actual creation\n", "    of an HDR image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "num_images = 5\n", "exposure_high = 100000.0\n", "exposure_mid = 50000.0\n", "exposure_low = 25000.0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Waits for the user to connect a device before raising an exception if it fails\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "devices = None\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "nodemap = device.nodemap\n", "print(f'Device used in the example:\\n{TAB1}{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Store initial values\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes = nodemap.get_node(['TriggerMode', 'TriggerSource', 'TriggerSelector', 'TriggerSoftware', 'TriggerArmed', 'ExposureAuto', 'ExposureTime'])\n", "\n", "trigger_mode_initial = nodes['TriggerMode'].value\n", "trigger_source_initial = nodes['TriggerSource'].value\n", "trigger_selector_initial = nodes['TriggerSelector'].value\n", "exposure_auto_initial = nodes['ExposureAuto'].value\n", "exposure_time_initial = nodes['ExposureTime'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Continually check until trigger is armed. Once the trigger is armed, it is ready to be executed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def trigger_software_once_armed(nodes):\n", "    trigger_armed = False\n", "\n", "    while not trigger_armed:\n", "        trigger_armed = bool(nodes['TriggerArmed'].value)\n", "\n", "    # retrieve and execute software trigger node\n", "    nodes['TriggerSoftware'].execute()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates exposure configuration and acquisition for HDR imaging\n", "> - sets trigger mode\n", "> - disables automatic exposure\n", "> - sets high exposure time\n", "> - gets first image\n", "> - sets medium exposure time\n", "> - gets second image\n", "> - sets low exposure time\n", "> - gets third images\n", "> - copies images into object for later processing\n", "> - does NOT process copied images\n", "> - cleans up copied images"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Prepare trigger mode\n", ">   Enable trigger mode before starting the stream. This example uses the\n", "    trigger to control the moment that images are taken. This ensures the\n", "    exposure time of each image in a way that a continuous stream might\n", "    have trouble with."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{TAB1}Prepare trigger mode\")\n", "nodes['TriggerSelector'].value = \"FrameStart\"\n", "nodes['TriggerMode'].value = \"On\"\n", "nodes['TriggerSource'].value = \"Software\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Disable automatic exposure\n", ">   Disable automatic exposure before starting the stream. The HDR images\n", "    in this example require three images of varied exposures, which need to\n", "    be set manually."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{TAB1}Disable auto exposure\")\n", "nodes['ExposureAuto'].value = 'Off'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Get exposure time and software trigger nodes\n", ">   The exposure time and software trigger nodes are retrieved beforehand\n", "    in order to check for existance, readability, and writability only once\n", "    before the stream."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{TAB1}Get exposure time and trigger software nodes\")\n", "\n", "if nodes['ExposureTime'] is None or nodes['TriggerSoftware'] is None:\n", "    raise Exception(\"ExposureTime or TriggerSoftware node not found\")\n", "\n", "if (nodes['ExposureTime'].is_writable is False\n", "    or nodes['TriggerSoftware'].is_writable is False):\n", "    raise Exception(\"ExposureTime or TriggerSoftware node not writable\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Check exposure time range\n", "> If largest exposure times is not within the exposure time range, set largest exposure time to max value and set the remaining exposure times to half the value of the state before"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if (exposure_high > nodes['ExposureTime'].max\n", "    or exposure_low < nodes['ExposureTime'].min):\n", "\n", "    exposure_h = nodes['ExposureTime'].max\n", "    exposure_m = exposure_h / 2\n", "    exposure_l = exposure_m / 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Setup stream values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap = device.tl_stream_nodemap\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Get high, medium, and low exposure images\n", ">   This example grabs three examples of varying exposures for later\n", "    processing. For each image, the exposure must be set, an image must\n", "    be triggered, and then that image must be retrieved. After the\n", "    exposure time is changed, the setting does not take place on the\n", "    device until after the next frame. Because of this, two images are\n", "    retrieved, the first of which is discarded."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Store HDR images for processing\n", "hdr_images = []\n", "\n", "print(f\"{TAB1}Acquire {num_images} HDR images\")\n", "device.start_stream()\n", "\n", "for i in range(0, num_images):\n", "    print(f'{TAB2}Getting HDR image {i}')\n", "\n", "    # High exposure time\n", "    nodes['ExposureTime'].value = exposure_h\n", "    trigger_software_once_armed(nodes)\n", "    image_pre_high = device.get_buffer()\n", "    trigger_software_once_armed(nodes)\n", "    image_high = device.get_buffer()\n", "\n", "    print(f\"{TAB1}{TAB2}Image High Exposure {nodes['ExposureTime'].value}\")\n", "\n", "    # Medium exposure time\n", "    nodes['ExposureTime'].value = exposure_m\n", "    trigger_software_once_armed(nodes)\n", "    image_pre_mid = device.get_buffer()\n", "    trigger_software_once_armed(nodes)\n", "    image_mid = device.get_buffer()\n", "\n", "    print(f\"{TAB1}{TAB2}Image Mid Exposure {nodes['ExposureTime'].value}\")\n", "\n", "    # Low exposure time\n", "    nodes['ExposureTime'].value = exposure_l\n", "    trigger_software_once_armed(nodes)\n", "    image_pre_low = device.get_buffer()\n", "    trigger_software_once_armed(nodes)\n", "    image_low = device.get_buffer()\n", "\n", "    print(f\"{TAB1}{TAB2}Image Low Exposure {nodes['ExposureTime'].value}\")\n", "\n", "    \"\"\"\n", "    Copy images for processing later\n", "        Use the image factory to copy the images for later processing.\n", "        Images are copied in order to requeue buffers to allow for more\n", "        images to be retrieved from the device.\n", "    \"\"\"\n", "    print(f\"{TAB2}Copy images for HDR processing later\")\n", "\n", "    i_high = BufferFactory.copy(image_high)\n", "    hdr_images.append(i_high)\n", "    i_mid = BufferFactory.copy(image_mid)\n", "    hdr_images.append(i_mid)\n", "    i_low = BufferFactory.copy(image_low)\n", "    hdr_images.append(i_low)\n", "\n", "    # Requeue buffers\n", "    device.requeue_buffer(image_pre_high)\n", "    device.requeue_buffer(image_high)\n", "    device.requeue_buffer(image_pre_mid)\n", "    device.requeue_buffer(image_mid)\n", "    device.requeue_buffer(image_pre_low)\n", "    device.requeue_buffer(image_low)\n", "\n", "device.stop_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Run HDR processing\n", ">    Once the images have been retrieved and copied, they can be processed\n", "    into an HDR image. HDR algorithms"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{TAB1}Run HDR processing\")\n", "\n", "# Destroy copied images after processing to prevent memory leaks\n", "for i in range(0, hdr_images.__len__()):\n", "    BufferFactory.destroy(hdr_images[i])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Return nodes to intial values\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes['ExposureTime'].value = exposure_time_initial\n", "nodes['ExposureAuto'].value = exposure_auto_initial\n", "nodes['TriggerSelector'].value = trigger_selector_initial\n", "nodes['TriggerSource'].value = trigger_source_initial\n", "nodes['TriggerMode'].value = trigger_mode_initial\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}