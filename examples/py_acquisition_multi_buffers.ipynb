{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system\n", "\n", "NUMBER_OF_BUFFERS = 25"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: Introduction\n", ">   This example introduces the basics of image acquisition. This includes\n", "    setting image acquisition and buffer handling modes, setting the device to\n", "    automatically negotiate packet size, and setting the stream packet resend\n", "    node before starting the image stream. The example then starts acquiring\n", "    images by grabbing and requeuing buffers, and retrieving data on images\n", "    before finally stopping the image stream.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 1 device(s)\n", "\n", "  Select device:\n", "    1. ('1c:0f:af:12:53:65', 'PHX050S-C', 'ThisIsAValue', '169.254.3.2')\n", "Device used in the example:\n", "\t('1c:0f:af:12:53:65', 'PHX050S-C', 'ThisIsAValue', '169.254.3.2')\n"]}], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        device = system.select_device(devices)\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Enable stream auto negotiate packet size\n", ">   Setting the stream packet size is done before starting the stream.\n", "    Setting the stream to automatically negotiate packet size instructs the\n", "    camera to receive the largest packet size that the system will allow.\n", "    This generally increases frame rate and results in fewer interrupts per\n", "    image, thereby reducing CPU load on the host system. Ethernet settings\n", "    may also be manually changed to allow for a larger packet size."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Enable stream packet resend\n", ">   Enable stream packet resend before starting the stream. Images are sent\n", "    from the camera to the host in packets using UDP protocol, which\n", "    includes a header image number, packet number, and timestamp\n", "    information. If a packet is missed while receiving an image, a packet\n", "    resend is requested and this information is used to retrieve and\n", "    redeliver the missing packet in the correct order."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set features before streaming.-------------------------------------------\n", ">  Set width and height to their max values"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Getting 'Width' and 'Height' Nodes\n", "Setting 'Width' and 'Height' Nodes value to their max values\n"]}], "source": ["print('Getting \\'Width\\' and \\'Height\\' Nodes')\n", "nodes = device.nodemap.get_node(['Width', 'Height'])\n", "\n", "print('Setting \\'Width\\' and \\'Height\\' Nodes value to their '\n", "\t'max values')\n", "nodes['Width'].value = nodes['Width'].max\n", "nodes['Height'].value = nodes['Height'].max"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Grab images -------------------------------------------------------------\n", "> - Starting the stream allocates buffers, which can be passed in as\n", " an argument (default: 10), and begins filling them with data.\n", " Buffers must later be requeued to avoid memory leaks.<br>\n", "> - 'Device.get_buffer()' with no arguments returns one buffer(NOT IN A LIST)<br>\n", "> - 'Device.get_buffer(30)' returns 30 buffers(IN A LIST)<br>\n", "> - 'Device.requeue_buffer()' takes a buffer or many buffers in a list or tuple"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stream started with 30 buffers\n", "\tGet 30 buffers in a list\n", "\t\tbuffer 0 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer 1 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer 2 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer 3 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer 4 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer 5 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer 6 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer 7 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer 8 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer 9 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer10 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer11 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer12 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer13 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer14 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer15 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer16 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer17 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer18 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer19 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer20 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer21 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer22 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer23 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer24 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer25 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer26 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer27 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer28 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "\t\tbuffer29 received | Width = 2448 pxl, Height = 2048 pxl, Pixel Format = BayerRG8\n", "Requeued 30 buffers\n", "Stream stopped\n"]}], "source": ["number_of_buffers = 30\n", "\n", "\n", "device.start_stream(number_of_buffers)\n", "print(f'Stream started with {number_of_buffers} buffers')\n", "\n", "print(f'\\tGet {number_of_buffers} buffers in a list')\n", "buffers = device.get_buffer(number_of_buffers)\n", "\n", "# Print image buffer info\n", "for count, buffer in enumerate(buffers):\n", "\tprint(f'\\t\\tbuffer{count:{2}} received | '\n", "\t\tf'Width = {buffer.width} pxl, '\n", "\t\tf'Height = {buffer.height} pxl, '\n", "\t\tf'Pixel Format = {buffer.pixel_format.name}')\n", "\n", "device.requeue_buffer(buffers)\n", "print(f'Requeued {number_of_buffers} buffers')\n", "\n", "device.stop_stream()\n", "print(f'Stream stopped')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Destroyed all created devices\n"]}], "source": ["system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 4}