{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import time\n", "import ctypes\n", "import math\n", "\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.system import system\n", "from arena_api.__future__ import save\n", "from arena_api.__future__.save import Writer\n", "from arena_api.enums import PixelFormat\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Polarization: Reflection Suppression\n", "> This example demonstrates the process of suppressing reflections in\n", "    image data acquired from a polarized sensor camera (polar-color)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "TAB1 = \"  \"\n", "TAB2 = \"    \""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Created 2 device(s)\n"]}], "source": ["def create_devices_with_tries():\n", "\t'''\n", "\tThis function waits for the user to connect a device before raising\n", "\t\tan exception\n", "\t'''\n", "\n", "\ttries = 0\n", "\ttries_max = 6\n", "\tsleep_time_secs = 10\n", "\twhile tries < tries_max:  # Wait for device for 60 seconds\n", "\t\tdevices = system.create_device()\n", "\t\tif not devices:\n", "\t\t\tprint(\n", "\t\t\t\tf'{TAB1}Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "\t\t\t\tf'secs for a device to be connected!')\n", "\t\t\tfor sec_count in range(sleep_time_secs):\n", "\t\t\t\ttime.sleep(1)\n", "\t\t\t\tprint(f'{TAB1}{sec_count + 1 } seconds passed ',\n", "\t\t\t\t\t'.' * sec_count, end='\\r')\n", "\t\t\ttries += 1\n", "\t\telse:\n", "\t\t\tprint(f'{TAB1}Created {len(devices)} device(s)')\n", "\t\t\treturn devices\n", "\telse:\n", "\t\traise Exception(f'{TAB1}No device found! Please connect a device and run '\n", "\t\t\t\t\t\tf'the example again.')\n", "\t\n", "devices = create_devices_with_tries()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Select device"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Select device:\n", "    1. ('1c:0f:af:2b:cf:60', 'TRI050S-P', 'polar-mono', '169.254.97.207')\n", "    2. ('1c:0f:af:cb:e3:13', 'PHX050S1-Q', 'polar-color', '169.254.20.227')\n"]}], "source": ["device = system.select_device(devices)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Get device stream nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Get nodes ---------------------------------------------------------------\n", "nodes = device.nodemap.get_node(['Width', 'Height', 'PixelFormat'])\n", "\n", "# get node values that will be changed in order to return their values at\n", "# the end of the example\n", "pixelformat_initial_value = nodes['PixelFormat'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates acquisition and processing of polarized image data to suppress reflections:\n", "> - Configures the camera to a polarized pixel format.\n", "> - Acquires a polarized input image.\n", "> - Processes the raw input image to suppress reflections.\n", "> - Saves the processed image to disk"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Setting Pixel Format to PolarizedAngles_0d_45d_90d_135d_BayerRG8\n"]}], "source": ["# Set pixel format to PolarizedAngles_0d_45d_90d_135d_BayerRG8\n", "pixel_format_name = 'PolarizedAngles_0d_45d_90d_135d_BayerRG8'\n", "print(f'{TAB1}Setting Pixel Format to {pixel_format_name}')\n", "nodes['PixelFormat'].value = pixel_format_name"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Starting stream\n", "    Acquire image\n"]}], "source": ["print(f'{TAB1}Starting stream')\n", "\n", "device.start_stream(1)\n", "print(f'{TAB2}Acquire image')\n", "image_buffer = device.get_buffer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Save raw input image"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Save input image to c:\\Users\\<USER>\\software\\arena_api\\examples\\images\\jupyter_polarization\\input_image.raw\n"]}], "source": ["def save(buffer):\n", "    \n", "\twriter = Writer()\n", "\twriter.pattern = 'images/jupyter_polarization/input_image.raw'\n", "\n", "\t# Save converted buffer\n", "\twriter.save(buffer)\n", "\tprint(f'{TAB2}Save input image to {writer.saved_images[-1]}')\n", "\t\n", "save(image_buffer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Process the input image"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Processing the raw input image to suppress reflections\n"]}], "source": ["print(f'{TAB2}Processing the raw input image to suppress reflections')\n", "        \n", "src_width = image_buffer.width         \n", "src_height = image_buffer.height\n", "src_pixel_format = image_buffer.pixel_format\n", "        \n", "\n", "if src_pixel_format != PixelFormat.PolarizedAngles_0d_45d_90d_135d_BayerRG8:\n", "\tprint(\"\\tError - Input image pixel format [{}] is a non-polarized format\".format(src_pixel_format))\n", "        \n", "src_bpp = image_buffer.bits_per_pixel\n", "src_step = src_bpp // 8\n", "\t\t\n", "\n", "# Create a new buffer to store the processed image\n", "dst_width = src_width\n", "dst_height = src_height\n", "dst_pixel_format = PixelFormat.BayerRG8\n", "dst_step = PixelFormat.get_bits_per_pixel(dst_pixel_format) // 8\n", "        \n", "dst_size = dst_width * dst_height\n", "dst_data_size = dst_width * dst_height * dst_step\n", "            \n", "dst_data = (ctypes.c_ubyte * dst_data_size)()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Processing the raw input image to suppress reflections has been completed\n"]}], "source": ["def suppress_reflection(src_data, src_step, dst_data, dst_step, dst_size):\n", "    src_length = len(src_data)\n", "    \n", "    for i in range(dst_size):\n", "        p0_index = i * src_step\n", "        p45_index = p0_index + 1\n", "        p90_index = p0_index + 2\n", "        p135_index = p0_index + 3\n", "\n", "        if p135_index >= src_length:\n", "            break\n", "\n", "        _90 = float(src_data[p90_index])\n", "        _45 = float(src_data[p45_index])\n", "        _135 = float(src_data[p135_index])\n", "        _0 = float(src_data[p0_index])\n", "\n", "        theta = 0.5 * math.atan2(_45 - _135, _0 - _90) + math.pi / 2.0\n", "        sintheta = math.sin(2.0 * theta) * 0.5\n", "        costheta = math.cos(2.0 * theta) * 0.5\n", "\n", "        _signed = (0.25 * (_0 + _45 + _90 + _135)) + (costheta * (_0 - _90)) + (sintheta * (_45 - _135))\n", "\n", "        if _signed > 255.0:\n", "            _signed = 255.0\n", "\n", "        dst_value = max(0, int(_signed))\n", "\n", "        dst_index = i * dst_step\n", "        if dst_index + 2 < len(dst_data):\n", "            dst_data[dst_index] = dst_value\n", "            dst_data[dst_index + 1] = dst_value\n", "            dst_data[dst_index + 2] = dst_value\n", "\n", "if (src_bpp == 32 or src_bpp == 64):\n", "\tsuppress_reflection(image_buffer.data, src_step, dst_data, dst_step, dst_size)\n", "     \n", "print(f'{TAB2}Processing the raw input image to suppress reflections has been completed')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Save the reflection-corrected polarized image"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Save the reflection-corrected polarized image to c:\\Users\\<USER>\\software\\arena_api\\examples\\images\\jupyter_polarization\\reflection_suppression.jpg\n"]}], "source": ["uint8_ptr = ctypes.POINTER(ctypes.c_ubyte)\n", "dst_data_ptr = ctypes.cast(dst_data, uint8_ptr)\n", "            \n", "# Save the reflection-corrected polarized image\n", "output_buffer = BufferFactory.create(dst_data_ptr, dst_data_size, dst_width, dst_height, dst_pixel_format) \n", "        \n", "writer_jpg = Writer.from_buffer(output_buffer)\n", "            \n", "writer_jpg.save(output_buffer, 'images/jupyter_polarization/reflection_suppression.jpg')\n", "\t\t\n", "print(f'{TAB2}Save the reflection-corrected polarized image to {writer_jpg.saved_images[-1]}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Clean up"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Stream stopped\n"]}], "source": ["BufferFactory.destroy(output_buffer)\n", "      \n", "device.requeue_buffer(image_buffer)\n", "\t\t\n", "device.stop_stream()\n", "print(f'{TAB1}Stream stopped')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# restores initial node values\n", "nodes['PixelFormat'].value = pixelformat_initial_value"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Destroyed all created devices\n"]}], "source": ["# Destroy device. Optional, implied by closing of module\n", "system.destroy_device()\n", "print(f'{TAB1}Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}