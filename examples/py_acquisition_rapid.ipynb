{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: Rapid Acquisition\n", "> This example demonstrates configuring device settings in order to reduce\n", "    bandwidth and increase framerate. This includes reducing the region of\n", "    interest, reducing bits per pixel, increasing packet size, reducing\n", "    exposure time, and setting a large number of buffers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Waits for the user to connect a device before raising an\n", "exception if it fails\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 1\n", "devices = None\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Store initial values\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap = device.nodemap\n", "nodes = nodemap.get_node(['Width', 'Height', 'PixelFormat',  'ExposureAuto', 'ExposureTime','DeviceStreamChannelPacketSize',])\n", "width_initial = nodes['Width'].value\n", "height_initial = nodes['Height'].value\n", "pixel_format_initial = nodes['PixelFormat'].value\n", "exposure_auto_initial = nodes['ExposureAuto'].value\n", "exposure_time_initial = nodes['ExposureTime'].value\n", "stream_packet_size_initial = nodes['DeviceStreamChannelPacketSize'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set features before streaming\n", "> - Setup width, height, pixel format, exposure auto, exposure time\n", "> - Set maximum stream channel packet size\n", "> > - Maximizing packet size increases frame rate by reducing the amount\n", "      of overhead required between images. This includes both extra\n", "      header/trailer data per packet as well as extra time from\n", "      intra-packet spacing (the time between packets). In order to grab\n", "      images at the maximum packet size, the ethernet adapter must be\n", "      configured appropriately: 'Jumbo packet' must be set to its\n", "      maximum, 'UDP checksum offload' must be set to 'Rx & Tx Enabled',\n", "      and 'Received Buffers' must be set to its maximum."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def setup(width, height, pixel_format, exposure_auto):\n", "    if nodes['Width'].is_readable and nodes['Width'].is_writable:\n", "        nodes['Width'].value = width\n", "    if nodes['Height'].is_readable and nodes['Height'].is_writable:\n", "        nodes['Height'].value = height\n", "    if nodes['PixelFormat'].is_readable and nodes['PixelFormat'].is_writable:\n", "        nodes['PixelFormat'].value = pixel_format\n", "    if nodes['ExposureAuto'].is_readable and nodes['ExposureAuto'].is_writable:\n", "        nodes['ExposureAuto'].value = exposure_auto\n", "    if nodes['ExposureTime'].is_readable and nodes['ExposureTime'].is_writable:\n", "        nodes['ExposureTime'].value = nodes['ExposureTime'].min\n", "    if nodes['DeviceStreamChannelPacketSize'].is_readable \\\n", "       and nodes['DeviceStreamChannelPacketSize'].is_writable:\n", "        stream_packet_size_max = nodes['DeviceStreamChannelPacketSize'].max\n", "        nodes['DeviceStreamChannelPacketSize'].value = stream_packet_size_max\n", "\n", "    else:\n", "        raise Exception('Read/Write access to devices nodes not available. Reconnecting device recommended')\n", "\n", "    stream_nodemap = device.tl_stream_nodemap\n", "    stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "    stream_nodemap['StreamPacketResendEnable'].value = True "]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Returns the nodes to their initial value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def return_original():\n", "    nodes['Width'].value = width_initial\n", "    nodes['Height'].value = height_initial\n", "    nodes['PixelFormat'].value = pixel_format_initial\n", "    nodes['ExposureTime'].value = exposure_time_initial\n", "    nodes['ExposureAuto'].value = exposure_auto_initial\n", "    nodes['DeviceStreamChannelPacketSize'].value = stream_packet_size_initial\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set low width and height\n", ">    Reducing the size of an image reduces the amount of bandwidth required\n", "    for each image. The less bandwidth required per image, the more images\n", "    can be sent over the same bandwidth.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["width = 100\n", "height = 100\n", "print(f'{TAB1}Set low width and height ({width}x{height})')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set small pixel format\n", ">    Similar to reducing the ROI, reducing the number of bits per pixel also\n", "    reduces the bandwidth required for each image. The smallest pixel\n", "    formats are 8-bit bayer and 8-bit mono (i.e. BayerRG8 and Mono8)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pixel_format = 'Mono8'\n", "print(f'{TAB1}Set small pixel format ({pixel_format})')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set low exposure time\n", ">    Decreasing exposure time can increase frame rate by reducing the amount\n", "    of time it takes to grab an image. Reducing the exposure time past\n", "    certain thresholds can cause problems related to not having enough\n", "    light. In certain situations this can be mitigated by increasing gain\n", "    and/or environmental light."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exposure_auto = 'Off'\n", "\n", "exposure_min = nodes['ExposureTime'].value\n", "print(f'{TAB1}Set minimum exposure time ({exposure_min})')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Start stream with high number of buffers\n", ">    Increasing the number of buffers can increase speeds by reducing the\n", "    amount of time taken to requeue buffers. In this example, one buffer is\n", "    used for each image. Of course, the amount of buffers that can be used\n", "    is limited by the amount of space in memory.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_buffers = 500"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates configuration for high frame rates\n", "> - lowers image size\n", "> - maximizes packet size\n", "> - minimizes exposure time\n", "> - sets high number of buffers\n", "> - waits until after acquisition to requeue buffers"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true, "tags": []}, "outputs": [], "source": ["setup(width, height, pixel_format, exposure_auto)\n", "\n", "with device.start_stream(num_buffers):\n", "    print(f'{TAB1}Stream started with 500 buffers')\n", "    \n", "    # Grab images --------------------------------------------------------\n", "    buffers = device.get_buffer(num_buffers)\n", "\n", "    # Print image buffer info\n", "    for count, buffer in enumerate(buffers):\n", "        if (count % 250 == 0 or count == num_buffers-1):\n", "            print(f'{TAB2}Buffer {count} received')\n", "\n", "    device.requeue_buffer(buffers)\n", "    print(f'{TAB1}Requeued {num_buffers} buffers')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Restore initial values to the device.\n", "> - Stop stream.\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["return_original()\n", "\n", "device.stop_stream()\n", "print(f'{TAB1}Stream stopped')\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}