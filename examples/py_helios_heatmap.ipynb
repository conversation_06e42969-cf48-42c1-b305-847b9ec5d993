{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import ctypes\n", "import sys\n", "import time\n", "\n", "from arena_api.__future__.save import Writer\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.enums import PixelFormat\n", "from arena_api.system import system\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Helios Heat Map: Introduction\n", "> This example demonstrates the transformation of 3-dimensional data to produce 2D and 3D heat maps. It uses a 3D-compatible camera. After verifying the camera, we snap an image, and generate a 2D heat map based on the z-coordinate data. It then saves the image as a JPEG file. It then also generates a 3D heat map based on the same data, and saves it as a PLY file."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "RGB_MIN = 0\n", "RGB_MAX = 255\n", "COLOR_BORDER_RED = 0\n", "COLOR_BORDER_YELLOW = 375\n", "COLOR_BORDER_GREEN = 750\n", "COLOR_BORDER_CYAN = 1125\n", "COLOR_BORDER_BLUE = 1500\n", "\n", "# check if Helios2 camera used for the example\n", "isHelios2 = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Check if <PERSON><PERSON>s camera is being used"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# validate if Scan3dCoordinateSelector node exists.\n", "# If not, it is (probably) not a Helios Camera running the example\n", "try:\n", "\tscan_3d_operating_mode_node = device. \\\n", "\t\tnodemap['Scan3dOperatingMode'].value\n", "except KeyError:\n", "\tprint('Scan3dCoordinateSelector node is not found. '\n", "\t'Please make sure that Helios device is used for the example.\\n')\n", "\tsys.exit()\n", "\n", "# validate if Scan3dCoordinateOffset node exists.\n", "# If not, it is (probably) that Helios Camera has an old firmware\n", "try:\n", "\tscan_3d_coordinate_offset_node = device. \\\n", "\t\tnodemap['Scan3dCoordinateOffset'].value\n", "except KeyError:\n", "\tprint('Scan3dCoordinateOffset node is not found. '\n", "\t\t'Please update Helios firmware.\\n')\n", "\tsys.exit()\n", "\n", "# check if Helios2 camera used for the example\n", "device_model_name_node = device.nodemap['DeviceModelName'].value\n", "if 'HLT' in device_model_name_node:\n", "\tisHelios2 = True\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get device stream nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Store nodes' initial values ---------------------------------------------\n", "nodemap = device.nodemap\n", "\n", "# get node values that will be changed in order to return their values at\n", "# the end of the example\n", "pixelFormat_initial = nodemap['PixelFormat'].value\n", "operating_mode_initial = nodemap['Scan3dOperatingMode'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Set nodes\n", "- pixelformat to Coord3D_ABCY16\n", "- 3D operating mode to Distance1500mm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('\\nSettings nodes:')\n", "pixel_format = PixelFormat.Coord3D_ABCY16  # unsigned data\n", "print(f'\\tSetting pixelformat to { pixel_format.name}')\n", "nodemap.get_node('PixelFormat').value = pixel_format\n", "\n", "print('\\tSetting 3D operating mode')\n", "if isHelios2 is True:\n", "\tnodemap['Scan3dOperatingMode'].value = 'Distance3000mmSingleFreq'\n", "else:\n", "\tnodemap['Scan3dOperatingMode'].value = 'Distance1500mm'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Maps a z-distance value to an RGB colour"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_rgb_colors_of_point_at_distance(z):\n", "\n", "\t# distance between red and yellow\n", "\tif COLOR_BORDER_RED <= z < COLOR_BORDER_YELLOW:\n", "\t\tyellow_percentage = z / COLOR_BORDER_YELLOW\n", "\t\tred = RGB_MAX\n", "\t\tgreen = RGB_MAX * yellow_percentage\n", "\t\tblue = RGB_MIN\n", "\n", "\t# distance between yellow and green\n", "\telif <PERSON>L<PERSON>_BORDER_YELLOW <= z < COLOR_BORDER_GREEN:\n", "\t\tgreen_percentage = (z - COLOR_BORDER_YELLOW) / COLOR_BORDER_YELLOW\n", "\t\tred = RGB_MAX - (RGB_MAX * green_percentage)\n", "\t\tgreen = RGB_MAX\n", "\t\tblue = RGB_MIN\n", "\n", "\t# distance between green and cyan\n", "\telif <PERSON>_BORDER_GREEN <= z < COLOR_BORDER_CYAN:\n", "\t\tcyan_percentage = (z - COLOR_BORDER_GREEN) / COLOR_BORDER_YELLOW\n", "\t\tred = RGB_MIN\n", "\t\tgreen = RGB_MAX\n", "\t\tblue = RGB_MAX * cyan_percentage\n", "\n", "\t# distance between cyan and blue\n", "\telif COL<PERSON>_BORDER_CYAN <= z <= COLOR_BORDER_BLUE:\n", "\t\tblue_percentage = (z - COLOR_BORDER_CYAN) / COLOR_BORDER_YELLOW\n", "\t\tred = RGB_MIN\n", "\t\tgreen = RGB_MAX - (RGB_MAX * blue_percentage)\n", "\t\tblue = RGB_MAX\n", "\telse:\n", "\t\tred = RGB_MIN\n", "\t\tgreen = RGB_MIN\n", "\t\tblue = RGB_MIN\n", "\n", "\treturn int(red), int(green), int(blue)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Generates a BGR8 heatmap for the z-values from a buffer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_a_BGR8_distance_heatmap_ctype_array(buffer_3d, scale_z):\n", "\n", "\t# 3D buffer info -------------------------------------------------\n", "\n", "\t# \"Coord3D_ABCY16s\" and \"Coord3D_ABCY16\" pixelformats have 4\n", "\t# channels pre pixel. Each channel is 16 bits and they represent:\n", "\t#   - x position\n", "\t#   - y postion\n", "\t#   - z postion\n", "\t#   - intensity\n", "\t# the value can be dynamically calculated this way:\n", "\t#   int(buffer_3d.bits_per_pixel/16) # 16 is the size of each channel\n", "\tCoord3D_ABCY16_channels_per_pixel = buffer_3d_step_size = 4\n", "\n", "\t# Buffer.pdata is a (uint8, ctypes.c_ubyte) pointer. \"Coord3D_ABCY16\"\n", "\t# pixelformat has 4 channels, and each channel is 16 bits.\n", "\t# It is easier to deal with Buffer.pdata if it is casted to 16bits\n", "\t# so each channel value is read/accessed easily.\n", "\t# \"Coord3D_ABCY16\" might be suffixed with \"s\" to indicate that the data\n", "\t# should be interpereted as signed.\n", "\tpdata_16bit = ctypes.cast(buffer_3d.pdata, ctypes.POINTER(ctypes.c_int16))\n", "\n", "\tnumber_of_pixels = buffer_3d.width * buffer_3d.height\n", "\n", "\t# out array info -------------------------------------------------\n", "\n", "\tBGR8_channels_per_pixel = array_bgr8_step_size = 3  # Blue, <PERSON>, Red\n", "\tBGR8_channel_size_bits = 8\n", "\tBGR8_pixel_size_bytes = BGR8_channel_size_bits * BGR8_channels_per_pixel\n", "\tarray_BGR8_size_in_bytes = BGR8_pixel_size_bytes * number_of_pixels\n", "\n", "\t# array to return\n", "\t# c_byte and not c_ubyte because it is signed data\n", "\tCustomArrayType = (ctypes.c_byte * array_BGR8_size_in_bytes)\n", "\tarray_BGR8_for_jpg = CustomArrayType()\n", "\n", "\t# iterate --------------------------------------------------------\n", "\n", "\t# iterate over two arrays with different internals\n", "\t# buffer_3d  : [x][y][z][a] | [x][y][z][a] | ... (each [] is 16 bit)\n", "\t# buffer_rgp : [b][g][r]    | [b][g][r]    | ... (each [] is 8 bit)\n", "\tbuffer_3d_pixel_index = 0\n", "\tarray_bgr8_pixel_index = 0\n", "\tfor _ in range(number_of_pixels):\n", "\n", "\t\t# Isolate the z channel.\n", "\t\t# In one pixel:\n", "\t\t#   The first channel is the x coordinate,\n", "\t\t#   the second channel is the y coordinate,\n", "\t\t#   the third channel is the z coordinate, and\n", "\t\t#   the fourth channel is intensity.\n", "\t\t# The z coordinate is what used to determine the coloring\n", "\t\tz = pdata_16bit[buffer_3d_pixel_index + 2]\n", "\n", "\t\t# Convert z to millimeters\n", "\t\t#   The z data converts at a specified ratio to mm, so by\n", "\t\t#   multiplying it by the Scan3dCoordinateScale for CoordinateC,  we\n", "\t\t#   are able to convert it to mm and can then compare it to the\n", "\t\t#   maximum distance of 1500mm.\n", "\t\tz = int(z * scale_z)\n", "\n", "\t\t# color respons to the z distance\n", "\t\tred, green, blue = get_rgb_colors_of_point_at_distance(z)\n", "\n", "\t\t# fill into the current pixel in BGR order not RGB\n", "\t\tarray_BGR8_for_jpg[array_bgr8_pixel_index] = blue\n", "\t\tarray_BGR8_for_jpg[array_bgr8_pixel_index + 1] = green\n", "\t\tarray_BGR8_for_jpg[array_bgr8_pixel_index + 2] = red\n", "\n", "\t\t# next pixel index corresping index\n", "\t\tbuffer_3d_pixel_index += buffer_3d_step_size\n", "\t\tarray_bgr8_pixel_index += array_bgr8_step_size\n", "\n", "\treturn array_BGR8_for_jpg\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Generates an RGB8 heatmap for the z-values from a buffer\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_a_RGB_coloring_ctype_array(buffer_3d, scale_z):\n", "\n", "\t# 3D buffer info -------------------------------------------------\n", "\n", "\t# \"Coord3D_ABCY16s\" and \"Coord3D_ABCY16\" pixelformats have 4\n", "\t# channels pre pixel. Each channel is 16 bits and they represent:\n", "\t#   - x position\n", "\t#   - y postion\n", "\t#   - z postion\n", "\t#   - intensity\n", "\t# the value can be dinamically calculated this way:\n", "\t#   int(buffer_3d.bits_per_pixel/16) # 16 is the size of each channel\n", "\tCoord3D_ABCY16_channels_per_pixel = buffer_3d_step_size = 4\n", "\n", "\t# Buffer.pdata is a (uint8, ctypes.c_ubyte) pointer. \"Coord3D_ABCY16\"\n", "\t# pixelformat has 4 channels, and each channel is 16 bits.\n", "\t# It is easier to deal with Buffer.pdata if it is casted to 16bits\n", "\t# so each channel value is read/accessed easily.\n", "\t# \"Coord3D_ABCY16\" might be suffixed with \"s\" to indicate that the data\n", "\t# should be interpereted as signed.\n", "\tpdata_16bit = ctypes.cast(buffer_3d.pdata, ctypes.POINTER(ctypes.c_int16))\n", "\n", "\tnumber_of_pixels = buffer_3d.width * buffer_3d.height\n", "\n", "\t# out array info -------------------------------------------------\n", "\n", "\tRGB8_channels_per_pixel = array_rgb8_step_size = 3  # RED, <PERSON>, <PERSON>\n", "\tRGB8_channel_size_bits = 8\n", "\tRGB8_pixel_size_bytes = RGB8_channel_size_bits * RGB8_channels_per_pixel\n", "\tarray_RGB8_size_in_bytes = RGB8_pixel_size_bytes * number_of_pixels\n", "\n", "\t# array to return\n", "\t# c_byte and not c_ubyte because it is signed data\n", "\tCustomArrayType = (ctypes.c_byte * array_RGB8_size_in_bytes)\n", "\tarray_RGB8_for_ply_coloring = CustomArrayType()\n", "\n", "\t# iterate --------------------------------------------------------\n", "\n", "\t# iterate over two arrays with different internals\n", "\t# buffer_3d  : [x][y][z][a] | [x][y][z][a] | ... (each [] is 16 bit)\n", "\t# buffer_rgp : [r][g][b]    | [r][g][b]    | ... (each [] is 8 bit)\n", "\tbuffer_3d_pixel_index = 0\n", "\tarray_rgb8_pixel_index = 0\n", "\tfor _ in range(number_of_pixels):\n", "\n", "\t\t# Isolate the z channel.\n", "\t\t# In one pixel:\n", "\t\t#   The first channel is the x coordinate,\n", "\t\t#   the second channel is the y coordinate,\n", "\t\t#   the third channel is the z coordinate, and\n", "\t\t#   the fourth channel is intensity.\n", "\t\t# The z coordinate is what used to determine the coloring\n", "\t\tz = pdata_16bit[buffer_3d_pixel_index + 2]\n", "\n", "\t\t# Convert z to millimeters\n", "\t\t#   The z data converts at a specified ratio to mm, so by\n", "\t\t#   multiplying it by the Scan3dCoordinateScale for CoordinateC,  we\n", "\t\t#   are able to convert it to mm and can then compare it to the\n", "\t\t#   maximum distance of 1500mm.\n", "\t\tz = int(z * scale_z)\n", "\n", "\t\t# color respons to the z distance\n", "\t\tred, green, blue = get_rgb_colors_of_point_at_distance(z)\n", "\n", "\t\t# fill into the current pixel in RGB order not BGR\n", "\t\tarray_RGB8_for_ply_coloring[array_rgb8_pixel_index] = red\n", "\t\tarray_RGB8_for_ply_coloring[array_rgb8_pixel_index + 1] = green\n", "\t\tarray_RGB8_for_ply_coloring[array_rgb8_pixel_index + 2] = blue\n", "\n", "\t\t# next pixel index corresping index\n", "\t\tbuffer_3d_pixel_index += buffer_3d_step_size\n", "\t\tarray_rgb8_pixel_index += array_rgb8_step_size\n", "\n", "\treturn array_RGB8_for_ply_coloring"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get z-coordinate scale\n", "> to convert z values to mm\n", "> requires ``Scan3dCoordinateSelector`` to be set to ``CoordinateC``"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Get z coordinate scale from nodemap')\n", "nodemap[\"Scan3dCoordinateSelector\"].value = \"CoordinateC\"\n", "scale_z = nodemap[\"Scan3dCoordinateScale\"].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Grab buffers and generate heatmaps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Starting the stream allocates buffers and begins filling them with data.\n", "with device.start_stream(1):\n", "\n", "\tprint(f'\\nStream started with 1 buffer')\n", "\tprint('\\tGet a buffer')\n", "\n", "\t# get_buffer would timeout or return 1 buffers\n", "\tbuffer_3d = device.get_buffer()\n", "\tprint('\\tbuffer received')\n", "\n", "\t# JPG FILE (2D heat map) -------------------------------------\n", "\n", "\tprint('\\t\\tCreating BGR8 array from buffer')\n", "\tarray_BGR8_for_jpg = get_a_BGR8_distance_heatmap_ctype_array(buffer_3d,\n", "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tscale_z)\n", "\tuint8_ptr = ctypes.POINTER(ctypes.c_ubyte)\n", "\tptr_array_BGR8_for_jpg = uint8_ptr(array_BGR8_for_jpg)\n", "\tbits_per_pixel =  PixelFormat.get_bits_per_pixel(PixelFormat.BGR8)\n", "\tarray_BGR8_for_jpg_size_in_bytes = int(buffer_3d.width * buffer_3d.height * bits_per_pixel / 8)\n", "\theat_buffer = BufferFactory.create(ptr_array_BGR8_for_jpg,\n", "\t\t\t\t\t\t\t\t\tarray_BGR8_for_jpg_size_in_bytes,\n", "\t\t\t\t\t\t\t\t\tbuffer_3d.width,\n", "\t\t\t\t\t\t\t\t\tbuffer_3d.height,\n", "\t\t\t\t\t\t\t\t\tPixelFormat.BGR8)\n", "\n", "\t# create an image writer\n", "\t# The writer, optionally, can take width, height, and bits per pixel\n", "\t# of the image(s) it would save. if these arguments are not passed\n", "\t# at run time, the first buffer passed to the Writer.save()\n", "\t# function will configure the writer to the arguments buffer's width,\n", "\t# height, and bits per pixel\n", "\n", "\t# takes the setting of writer from buffer\n", "\twriter_jpg = Writer.from_buffer(heat_buffer)\n", "\t# save function takes a buffer made with BufferFactory that's why\n", "\t# heat_buffer was created though BufferFactory in the previous\n", "\t# steps\n", "\twriter_jpg.save(heat_buffer, 'heatmap.jpg')\n", "\n", "\t# buffers created with BufferFactory must be destroyed\n", "\tBufferFactory.destroy(heat_buffer)\n", "\n", "\t# PLY FILE (3D heat map)--------------------------------------\n", "\n", "\tprint('\\t\\tCreating RGB8 array from buffer')\n", "\tarray_RGB_colors = get_a_RGB_coloring_ctype_array(buffer_3d, scale_z)\n", "\n", "\tuint8_ptr = ctypes.POINTER(ctypes.c_ubyte)\n", "\tptr_array_RGB_colors = uint8_ptr(array_RGB_colors)\n", "\n", "\twriter_ply = Writer()\n", "\t# save function\n", "\t# buffer :\n", "\t#   buffer to save.\n", "\t# pattern :\n", "\t#   default name for the image is 'image_<count>.jpg' where count\n", "\t#   is a pre-defined tag that gets updated every time a buffer image\n", "\t#   is saved. More custom tags can be added using\n", "\t#   Writer.register_tag() function\n", "\t# kwargs (optional args) ignored if not an .ply image:\n", "\t#   - 'filter_points' default is True.\n", "\t#       Filters NaN points (A = B = C = -32,678)\n", "\t#   - 'is_signed' default is False.\n", "\t#       If pixel format is signed for example PixelFormat.Coord3D_A16s\n", "\t#       then this arg must be passed to the save function else\n", "\t#       the results would not be correct\n", "\t#   - 'scale' default is 0.25.\n", "\t#   - 'offset_a', 'offset_b' and 'offset_c' default to 0.0\n", "\twriter_ply.save(buffer_3d, 'heatmap.ply',\n", "\t\t\t\t\tcolor=ptr_array_RGB_colors,\n", "\t\t\t\t\tfilter_points=True)\n", "\n", "\t# Requeue the chunk data buffers\n", "\tdevice.requeue_buffer(buffer_3d)\n", "\tprint(f'\\tImage buffer requeued')\n", "\n", "# When the scope of the context manager ends, then 'Device.stop_stream()'\n", "# is called automatically\n", "print('Stream stopped')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Clean up"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# restores initial node values\n", "nodemap['PixelFormat'].value = pixelFormat_initial\n", "nodemap['Scan3dOperatingMode'].value = operating_mode_initial"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Destroy device. Optional, implied by closing of module\n", "system.destroy_device()\n", "print(\"Destroyed all created devices\")"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}