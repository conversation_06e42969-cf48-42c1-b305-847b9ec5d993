{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Chunk Data: CRC Validation\n", ">    This example demonstrates the use of chunk data to verify data\n", "    through a Cyclical Redundancy Check (or CRC for short). CRCs are\n", "    meant to check the validity of sent data. It is performed by doing\n", "    a series of calculations on the raw data before and after it is\n", "    sent. If the resultant integer values match, then it is safe to\n", "    assume the integrity of the data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This code block waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Configure devices and store initial values\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device = devices[0]\n", "print(f'Device used in the example:\\n\\t{device}')\n", "\n", "nodemap = device.nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Store initial acquisition mode and set it to Continuous\n", "initial_acquisition_mode = nodemap.get_node(\"AcquisitionMode\").value\n", "nodemap.get_node(\"AcquisitionMode\").value = \"Continuous\"\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Prep device to send chunk data with the buffer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["initial_chunk_mode_active = nodemap.get_node(\"ChunkModeActive\").value\n", "print('Activating chunk data on device')\n", "nodemap.get_node('ChunkModeActive').value = True\n", "\n", "# Use CRC chunk: unnecessary, any chunk works\n", "initial_chunk_selector = nodemap.get_node('ChunkSelector').value\n", "print(f\"\\tSetting 'ChunkSelector' node value to 'CRC'\")\n", "nodemap.get_node(\"ChunkSelector\").value = \"CRC\"\n", "\n", "initial_chunk_enable = nodemap.get_node('ChunkEnable').value\n", "print(f\"\\tEnabling CRC by setting 'ChunkEnable' node value to 'True'\")\n", "nodemap.get_node(\"ChunkEnable\").value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Retrieve and validate chunk data using CRC"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Grab images -------------------------------------------------------------\n", "> - Starting the stream allocates buffers, which can be passed in as\n", " an argument (default: 10), and begins filling them with data.\n", " Buffers must later be requeued to avoid memory leaks.<br>\n", "> - 'Device.get_buffer()' with no arguments returns one buffer(NOT IN A LIST)<br>\n", "> - 'Device.get_buffer(30)' returns 30 buffers(IN A LIST)<br>\n", "> - 'Device.requeue_buffer()' takes a buffer or many buffers in a list or tuple"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with device.start_stream():\n", "    print(f'Stream started with 10 buffers')\n", "    \n", "    print('\\tGet chunk data buffer(s)')\n", "\n", "    # This would timeout or returns all of the 10 buffers\n", "    chunk_data_buffers = device.get_buffer(number_of_buffers=10)\n", "    print(f'\\t{len(chunk_data_buffers)} chunk data buffers received')\n", "\n", "    '''\n", "    Confirm that each chunk is complete, then validate the buffer's CRC\n", "    '''\n", "    for buffer_index, chunk_data_buffer in enumerate(chunk_data_buffers):\n", "        if chunk_data_buffer.is_incomplete:\n", "            print(f'\\t\\t---------------------------------------------')\n", "            print(f'\\t\\tChunk data buffer{buffer_index} is incomplete')\n", "            print(f'\\t\\t---------------------------------------------')\n", "            # Continue\n", "        try:\n", "            '''\n", "            Buffer.is_valid_crc: compares calculated CRC value to the chunk CRC value.\n", "                This only works with buffers retrieved in chunk mode.\n", "            '''\n", "            print(f\"\\t\\tChunk data CRC is valid: {chunk_data_buffer.is_valid_crc}\")\n", "        except ValueError:\n", "            print(f'\\t\\t\\tFailed to get chunks')\n", "            print(f'\\t\\t---------------------------------------------')\n", "            continue\n", "\n", "    device.requeue_buffer(chunk_data_buffers)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Restore initial values to the device.\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap.get_node(\"ChunkEnable\").value = initial_chunk_enable\n", "nodemap.get_node(\"ChunkSelector\").value = initial_chunk_selector\n", "nodemap.get_node(\"ChunkModeActive\").value = initial_chunk_mode_active\n", "\n", "nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode\n", "\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}