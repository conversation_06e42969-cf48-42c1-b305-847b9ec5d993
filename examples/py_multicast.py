# -----------------------------------------------------------------------------
# Copyright (c) 2024, Lucid Vision Labs, Inc.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
# EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
# OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
# NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
# BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
# ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.
# -----------------------------------------------------------------------------

import time
from datetime import datetime

from arena_api.system import system

'''
Multicast
	This example demonstrates multicasting from the master's perspective.
	Multicasting allows for the streaming of images and events to multiple
	destinations. Multicasting requires nearly the same steps for both masters
	and listeners. The only difference, as seen below, is that device features
	can only be set by the master.
'''
TAB1 = "  "
TAB2 = "    "

# Image timeout
TIMEOUT_MILLISEC = 2000

# Length of time to grab images (sec)
#    Note that the listener must be started while the master is still
#    streaming, and that the listener will not receive any more images
#    once the master stops streaming.
NUM_SECONDS = 10


def create_devices_with_tries():
	'''
	This function waits for the user to connect a device before raising
		an exception
	'''

	tries = 0
	tries_max = 6
	sleep_time_secs = 10
	while tries < tries_max:  # Wait for device for 60 seconds
		devices = system.create_device()
		if not devices:
			print(
				f'{TAB1}Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '
				f'secs for a device to be connected!')
			for sec_count in range(sleep_time_secs):
				time.sleep(1)
				print(f'{TAB1}{sec_count + 1 } seconds passed ',
					'.' * sec_count, end='\r')
			tries += 1
		else:
			print(f'{TAB1}Created {len(devices)} device(s)')
			return devices
	else:
		raise Exception(f'{TAB1}No device found! Please connect a device and run '
						f'the example again.')


def example_entry_point():

	# Create a device
	devices = create_devices_with_tries()
	device = system.select_device(devices)

	'''
	Enable multicast
		Multicast must be enabled on both the master and listener. A small number
		of transport layer features will remain writable even though a device's
		access mode might be read-only.
	'''
	print(f'{TAB1}Enable multicast')
	device.tl_stream_nodemap['StreamMulticastEnable'].value = True

	'''
	Enable multicast and configure device
		Multicast must be enabled on both the master and listener. A small number
		of transport layer features will remain writable even though a device's
		access mode might be read-only.
	'''

	device_access_status = device.tl_device_nodemap['DeviceAccessStatus'].value

	# Master
	if device_access_status == 'ReadWrite':

		print(f'{TAB1}Host streaming as "master"')

		# Get node values that will be changed in order to return their values
		# at the end of the example
		acquisition_mode_initial = device.nodemap['AcquisitionMode'].value

		# Set acquisition mode
		print(f'{TAB1}Set acquisition mode to "Continuous"')

		device.nodemap['AcquisitionMode'].value = 'Continuous'

		# Enable stream auto negotiate packet size
		device.tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True

		# Enable stream packet resend
		device.tl_stream_nodemap['StreamPacketResendEnable'].value = True

	# Listener
	else:
		print(f'{TAB1}Host streaming as "listener"\n')

	# Get images
	print(f'{TAB1}Getting images for {NUM_SECONDS} seconds')

	# Define start and latest time for timed image acquisition
	start_time = datetime.now()
	latest_time = datetime.now()

	# Start stream
	with device.start_stream():

		# Define image count to detect if all images are not received
		image_count = 0
		unreceived_image_count = 0

		print(f'{TAB1}Stream started')

		while (latest_time - start_time).total_seconds() < NUM_SECONDS:

			# update time
			latest_time = datetime.now()

			try:
				image_count = image_count + 1

				# 'Device.get_buffer()' with no arguments returns
				#  only one buffer
				buffer = device.get_buffer(timeout=TIMEOUT_MILLISEC)

				# Print some info about the image in the buffer
				#   Using the frame ID and timestamp allows for the comparison
				#   of images between multiple hosts.
				print(f'{TAB2}Image retrieved ('
					f'frame ID = {buffer.frame_id}, '
					f'timestamp (ns) = {buffer.timestamp_ns}) and requeue')

			except TimeoutError:
				print(f'{TAB2}No image received')
				unreceived_image_count = unreceived_image_count + 1
				continue

			# Requeue the image buffer
			device.requeue_buffer(buffer)

	if unreceived_image_count == image_count:
		print(f'\nNo images were received, this can be caused by firewall, vpn settings or firmware\n')
		print(f'Please add python application to firewall exception')

	# Return node to its initial value
	if device_access_status == "ReadWrite":
		device.nodemap['AcquisitionMode'].value = acquisition_mode_initial

	# Clean up ----------------------------------------------------------------

	# Stop stream and destroy device. This call is optional and will
	# automatically be called for any remaining devices when the system
	# module is unloading.
	system.destroy_device()
	print(f'{TAB1}Destroyed all created devices')


if __name__ == '__main__':
	print('\nWARNING:\nTHIS EXAMPLE MIGHT CHANGE THE DEVICE(S) SETTINGS!')
	print('\nExample started\n')
	example_entry_point()
	print('\nExample finished successfully')
