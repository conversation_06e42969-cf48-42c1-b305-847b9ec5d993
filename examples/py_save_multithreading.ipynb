{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import queue\n", "import threading\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.__future__.save import Writer\n", "from multiprocessing import Value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquiring and Saving Images on Seperate Threads: Introduction\n", "> Saving images can sometimes create a bottleneck in the image acquisition pipeline. By seperating saving onto a separate thread, this bottleneck can be avoided. This example is programmed as a simple producer-consumer problem."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap = device.nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquire thirty images and add them to the queue to be saved.\n", "> after: send the signal that no more images are incoming, so that the other thread knows when to stop."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_multiple_image_buffers(device, buffer_queue, is_more_buffers):\n", "\n", "    number_of_buffers = 30\n", "\n", "    device.start_stream(number_of_buffers)\n", "    print(f'Stream started with {number_of_buffers} buffers')\n", "\n", "    print(f'\\tGet {number_of_buffers} buffers in a list')\n", "    buffers = device.get_buffer(number_of_buffers)\n", "\n", "    # Print image buffer info\n", "    for count, buffer in enumerate(buffers):\n", "        print(f'\\t\\tbuffer{count:{2}} received | '\n", "              f'Width = {buffer.width} pxl, '\n", "              f'Height = {buffer.height} pxl, '\n", "              f'Pixel Format = {buffer.pixel_format.name}')\n", "        buffer_queue.put(BufferFactory.copy(buffer))\n", "        time.sleep(0.1)\n", "\n", "    device.requeue_buffer(buffers)\n", "    print(f'Requeued {number_of_buffers} buffers')\n", "\n", "    device.stop_stream()\n", "    print(f'Stream stopped')\n", "    is_more_buffers.value = 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save images until there are no more images to be saved\n", ">While there are images in the queue, or while images will still be added to the queue, save images until the queue is empty. Then wait one second before checking the earlier conditions. This ensures that all images will be saved."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_image_buffers(buffer_queue, is_more_buffers):\n", "\n", "    count = 0\n", "    while is_more_buffers.value or not buffer_queue.empty():\n", "        while(not buffer_queue.empty()):\n", "            buffer = buffer_queue.get()\n", "            writer = Writer.from_buffer(buffer)\n", "            writer.save(buffer, pattern=f\"Images/image_{count}.jpg\")\n", "            print(f\"Saved image {count}\")\n", "            count = count + 1\n", "        print(\"Queue empty, waiting 1s\")\n", "        time.sleep(1)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Shared objects"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "is_more_buffers: object shared between threads,\n", "    used to indicate if more buffers will be added to the queue.\n", "    The Value class can be shared between threads by default.\n", "    It may be desirable to create a queue object with this variable included.\n", "'''\n", "is_more_buffers = Value('i', 1)\n", "\n", "'''\n", "buffer_queue: consumer-producer queue that holds buffers.\n", "    Python's Queue class can handle access from different threads by default.\n", "    Acquisition thread acquires images and adds to queue, while the main thread\n", "    saves the images to disk.\n", "'''\n", "buffer_queue = queue.Queue()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Threads to acquire and save images"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["acquisition_thread = threading.Thread(\n", "    target=get_multiple_image_buffers, args=(device, buffer_queue, is_more_buffers))\n", "acquisition_thread.start()\n", "save_image_buffers(buffer_queue, is_more_buffers)\n", "acquisition_thread.join()\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3da5db85d9f5f7fce7af321096f4072067948d3445eede98938ef98c3c692858"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}