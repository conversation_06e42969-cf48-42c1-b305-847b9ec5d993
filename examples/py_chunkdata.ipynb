{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Chunk Data: Introduction\n", ">    This example introduces the configuration and retrieval of chunk data.\n", "    Chunk data is data that has been tacked on to the end of image data in\n", "    order to provide useful information on the image. Configuring chunk data\n", "    involves activating chunk mode and enabling desired chunks. Retrieving\n", "    chunk data from an image is similar to retrieving nodes from a node map.\n", "\n", "> -\tActivates chunk mode\n", "> - Enable selected chunks\n", "> - Starts the stream and gets images\n", "> - Retrieves chunk data from image\n", "> - Requeues buffers and stops the stream"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Store initial chunk mode value\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap = device.nodemap\n", "\n", "initial_chunk_mode_active = nodemap.get_node(\"ChunkModeActive\").value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Activate chunk mode and enable selected chunks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Activating chunk data on device')\n", "nodemap.get_node('ChunkModeActive').value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### List of chunk names to send with the buffer and store initial values for the list\n", ">  Use 'ChunkSelector' node to select the chunk to send. This is done\n", "    by setting the value of 'ChunkSelector' node to the name of the chunk,\n", "    then enabling the chunk by setting 'ChunkEnable' to 'True'. <br>\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chunks_to_append_to_buffer = ['PixelFormat', 'CRC',\n", "                              'Width', 'Height',\n", "                              'OffsetX', 'OffsetY']\n", "\n", "chunk_selector_node = nodemap.get_node('ChunkSelector')\n", "chunk_enable_node = nodemap.get_node('ChunkEnable')\n", "\n", "initial_chunk_selector = chunk_selector_node.value\n", "initial_chunk_enable = {}\n", "\n", "for chunk_name in chunks_to_append_to_buffer:\n", "    print(f'\\tsetting \\'ChunkSelector\\' node value to \\'{chunk_name}\\'')\n", "    chunk_selector_node.value = chunk_name\n", "    initial_chunk_enable[chunk_name] = chunk_enable_node.value\n", "    print(f'\\tenabling \\'{chunk_name}\\' by setting \\'ChunkEnable\\' '\n", "          f'node value to \\'True\\'')\n", "    chunk_enable_node.value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Grab images -------------------------------------------------------------\n", "> - Starting the stream allocates buffers, which can be passed in as\n", " an argument (default: 10), and begins filling them with data.\n", " Buffers must later be requeued to avoid memory leaks.<br>\n", "> - 'Device.get_buffer()' with no arguments returns one buffer(NOT IN A LIST)<br>\n", "> - 'Device.get_buffer(30)' returns 30 buffers(IN A LIST)<br>\n", "> - 'Device.requeue_buffer()' takes a buffer or many buffers in a list or tuple"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with device.start_stream():\n", "    print(f'Stream started with 10 buffers')\n", "    \n", "    print('\\tGet chunk data buffer(s)')\n", "\n", "    # This would timeout or returns all of the 10 buffers\n", "    chunk_data_buffers = device.get_buffer(number_of_buffers=10)\n", "    print(f'\\t{len(chunk_data_buffers)} chunk data buffers received')\n", "\n", "    '''\n", "    To access the chunks that are appended to a buffer use buffer\n", "    'Buffer.get_chunk()' method. The chunk name to get is the same as\n", "    the chunk value that was set and enabled but preceded with 'Chunk'.\n", "    '''\n", "    chunk_node_names = ['ChunkPixelFormat', 'ChunkCRC',\n", "                        'ChunkWidth', 'ChunkHeight',\n", "                        'ChunkOffsetX', 'ChunkOffsetY']\n", "\n", "    # Iterate over every chunk data buffer and print chunks' value\n", "    for buffer_index, chunk_data_buffer in enumerate(chunk_data_buffers):\n", "        '''\n", "        Get all chunks in list from buffer\n", "            'Buffer.get_chunk()' may raise an exception because the\n", "            buffer is incomplete\n", "        '''\n", "        if chunk_data_buffer.is_incomplete:\n", "            print(f'\\t\\t---------------------------------------------')\n", "            print(f'\\t\\tChunk data buffer{buffer_index} is incomplete')\n", "            print(f'\\t\\t---------------------------------------------')\n", "            # Continue\n", "        try:\n", "            chunk_nodes_dict = chunk_data_buffer.get_chunk(\n", "                chunk_node_names)\n", "\n", "            # Print the value of the chunks for the current buffer\n", "            print(f'\\t\\tChunk data buffer{buffer_index} chunks value:')\n", "            for chunk_node_name, chunk_node in chunk_nodes_dict.items():\n", "                print(f'\\t\\t\\t{chunk_node_name} = {chunk_node.value}')\n", "        except ValueError:\n", "            print(f'\\t\\t\\tFailed to get chunks')\n", "            print(f'\\t\\t---------------------------------------------')\n", "            continue\n", "\n", "    # Requeue the chunk data buffers\n", "    device.requeue_buffer(chunk_data_buffers)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### When the scope of the context manager ends, then 'Device.stop_stream()' is called automatically"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Restore initial values to the device.\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for chunk_name in chunks_to_append_to_buffer:\n", "    chunk_selector_node.value = chunk_name\n", "    chunk_enable_node.value = initial_chunk_enable[chunk_name]\n", "\n", "chunk_selector_node.value = initial_chunk_selector\n", "nodemap.get_node(\"ChunkModeActive\").value = initial_chunk_mode_active\n", "\n", "system.destroy_device()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}