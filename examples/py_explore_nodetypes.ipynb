{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Explore: Node Types\n", ">    This example explores the different properties of various node's types\n", "    including boolean, string, enumeration, integer, and float nodes. The user\n", "    inputs the node name that they wish to access (leaving out spacing between\n", "    words) in order to retrieve the node properties, or inputs 'x' to exit.\n", "    See Py_Explore_Nodes for a complete list of nodes and their respective types."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Waits for the user to connect a device before raising an exception if it fails\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "devices = None\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "nodemap = device.nodemap\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Explores nodes of boolean type\n", "> retrieves value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def explore_boolean(node):\n", "    print(f\"\\tValue: {node.value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Explores nodes of string type\n", "> - retrieves value\n", "> - retrieves maximum value length"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def explore_string(node):\n", "    print(f\"\\tValue: {node.value}\")\n", "    print(f\"\\tMaximum Length: {node.max}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Explores nodes of type integer\n", "> - retrieves value\n", "> - retrieves maximum and minimum\n", "> - retrieves increment and increment mode\n", "> - retrieves representation\n", "> - retrieves unit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def explore_integer(node):\n", "    print(f\"\\tValue: {node.value}\")\n", "    print(f\"\\tMinimum Length: {node.min}, Maximum Length: {node.max}\")\n", "    print(f\"\\tIncrement Mode: {node.inc} ({str(node.inc_mode)})\")\n", "    print(f\"\\tRepresentation: {str(node.representation)}\")\n", "    print(f\"\\tUnit: {node.unit}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Explores nodes of type integer\n", "> - retrieves value\n", "> - retrieves maximum and minimum\n", "> - retrieves increment and increment mode\n", "> - retrieves representation\n", "> - retrieves unit\n", "> - retrieves display notation\n", "> - retrieves display precision"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def explore_float(node):\n", "    print(f\"\\tValue: {node.value}\")\n", "    print(f\"\\tMinimum: {node.min}, Maximum: {node.max}\")\n", "\n", "    if (node.inc is not None):\n", "        print(f\"\\tIncrement Mode: {node.inc} ({str(node.inc_mode)})\")\n", "    \n", "    print(f\"\\tRepresentation: {str(node.representation)}\")\n", "    print(f\"\\tUnit: {node.unit}\")\n", "    print(f\"\\tDisplay Notation: {str(node.display_notation)}\")\n", "    print(f\"\\tDisplay Precision: {node.display_precision}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Explores nodes of string type\n", "> - retrieves value\n", "> - retrieves entries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def explore_enumeration(node):\n", "    print(f\"\\tCurrent Entry: {node.value}\")\n", "    print(f\"\\tInteger Value: {node.enumentry_nodes[node.value].int_value}\")\n", "    print(f\"\\tEntries: {str(node.enumentry_names)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Controls node exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def explore_nodes(nodemap):\n", "    node_name = input(\"\\nInput node name to explore from Device Nodemap ('x' to exit)\\n\")\n", "\n", "    # stay in loop until exit    \n", "    while True:\n", "        # exit manually on 'x'\n", "        if node_name.__eq__('x'):\n", "            print(\"\\t\\tSuccesfully exited\")\n", "            break\n", "        \n", "        # get node\n", "        node = nodemap.get_node(str(node_name))\n", "\n", "        # explore by type\n", "        if node and node.is_readable:\n", "            if node.interface_type.value == 3:\n", "                explore_boolean(node)\n", "            elif node.interface_type.value == 6:\n", "                explore_string(node)\n", "            elif node.interface_type.value == 9:\n", "                explore_enumeration(node)\n", "            elif node.interface_type.value == 2:\n", "                explore_integer(node)\n", "            elif node.interface_type.value == 5:\n", "                explore_float(node)\n", "            else:\n", "                print(\"\\tType not found\")\n", "        \n", "        else:\n", "            print(\"\\tNode not found\")\n", "        \n", "        # reset input\n", "        node_name = \"\"\n", "        node_name = input(\"\\tInput node name to explore ('x' to exit)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Example Started\")\n", "\n", "explore_nodes(nodemap)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print()\n", "print(\"Destroy Devices\")\n", "system.destroy_device()\n", "print(\"Example Finished\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3.10.7 ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "9a831188c03c7caeebd3251c40fdaa86517461ff286f09cf3194138e4e5369b6"}}}, "nbformat": 4, "nbformat_minor": 2}