{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system\n", "from arena_api.__future__.save import Writer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save: File Name Pattern\n", "> This example demonstrates saving a set of images according to a file name pattern, which uses the \\<count\\> and \\<timestamp\\> tags to differentiate between saved images. The essential points of the example include setting the image writer up with a file name pattern and using the cascading I/O operator (<<) to update the timestamp and save each image."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### File name pattern\n", "> File name patterns can use tags to easily customize your file names. Customizable tags can be added to a file name pattern and later set on the fly. Two tags, \\<count\\> and \\<datetime\\> have been built in to the save library. As seen below, \\<datetime\\> can take an argument to specify output. \\<count\\> also accepts arguments (local, path, and global) to specify what exactly is being counted."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FILE_NAME_PATTERN = \"Images/py_save_file_name_pattern/<vendor>_<model>_<serial>_image<count>-<datetime:yyMMdd_hhmmss_fff>.bmp\"\n", "\n", "# number of images to acquire and save\n", "NUM_IMAGES = 25\n", "\n", "# image timeout (milliseconds)\n", "TIMEOUT = 2000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Generator functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_vendor(device):\n", "    '''\n", "    Generator function for vendor\n", "    '''\n", "    while True:\n", "        yield device.nodemap.get_node(\"DeviceVendorName\").value\n", "\n", "\n", "def get_model(device):\n", "    '''\n", "    Generator function for model name\n", "    '''\n", "    while True:\n", "        yield device.nodemap.get_node(\"DeviceModelName\").value\n", "\n", "\n", "def get_serial(device):\n", "    '''\n", "    Generator function for serial number\n", "    '''\n", "    while True:\n", "        yield device.nodemap.get_node(\"DeviceSerialNumber\").value\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Must register tags with writer before including them in pattern\n", "> Must include a generator function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["writer = Writer()\n", "\n", "print(\"Register tags\")\n", "writer.register_tag(\"vendor\", generator=get_vendor(device))\n", "writer.register_tag(\"model\", generator=get_model(device))\n", "writer.register_tag(\"serial\", generator=get_serial(device))\n", "\n", "print(\"Set file name pattern\")\n", "writer.pattern = FILE_NAME_PATTERN\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_and_save_images(device, writer, num_images):\n", "\n", "    # Starting the stream allocates buffers, which can be passed in as\n", "    # an argument (default: 10), and begins filling them with data.\n", "    # Buffers must later be requeued to avoid memory leaks.\n", "    with device.start_stream():\n", "        print(f'Stream started with 10 buffers')\n", "        for i in range(num_images):\n", "            # 'Device.get_buffer()' with no arguments returns only one buffer\n", "            print('\\tGet one buffer')\n", "            buffer = device.get_buffer()\n", "\n", "            # Print some info about the image in the buffer\n", "            print(f'\\t\\tbuffer received   | '\n", "                  f'Width = {buffer.width} pxl, '\n", "                  f'Height = {buffer.height} pxl, '\n", "                  f'Pixel Format = {buffer.pixel_format.name}')\n", "\n", "            print(f\"Save image {i}\")\n", "            writer.save(buffer)\n", "\n", "            # Requeue the image buffer\n", "            device.requeue_buffer(buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_and_save_images(device, writer, NUM_IMAGES)\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}