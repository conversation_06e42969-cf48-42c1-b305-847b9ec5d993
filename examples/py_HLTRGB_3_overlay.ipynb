{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2023, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import ctypes\n", "from os.path import exists\n", "\n", "import numpy as np # pip3 install numpy\n", "import cv2  # pip3 install opencv-python\n", "# pip3 install tk / or 'sudo apt-get install python3-tk' for linux\n", "from tkinter import *\n", "from enum import Enum\n", "import math\n", "\n", "from arena_api import enums\n", "from arena_api.system import system\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.enums import PixelFormat\n", "from arena_api.__future__.save import Writer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### <PERSON><PERSON><PERSON> RGB: Overlay\n", ">This example is part 3 of a 3-part example on color overlay over 3D images.\n", "    With the system calibrated, we can now remove the calibration target from\n", "    the scene and grab new images with the Helios and Triton cameras, using the\n", "    calibration result to find the RGB color for each 3D point measured with\n", "    the Helios. Based on the output of solvePnP we can project the 3D points\n", "    measured by the Helios onto the RGB camera image using the OpenCV function\n", "    projectPoints. Grab a Helios image with the GetHeliosImage()\n", "    function(output: xyz_mm) and a Triton RGB image with the\n", "    GetTritionRGBImage() function(output: triton_rgb). The following code shows\n", "    how to project the Helios xyz points onto the Triton image, giving a(row,\n", "    col) position for each 3D point. We can sample the Triton image at\n", "    that(row, col) position to find the 3D point’s RGB value."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# image timeout\n", "TIMEOUT = 2000\n", "\n", "# calibration values file name\n", "FILE_NAME_IN = \"orientation.yml\"\n", "\n", "# orientation values file name\n", "FILE_NAME_OUT = \"py_HLTRGB_3_Overlay.ply\"\n", "\n", "TRITON = 'Triton'\n", "HELIOS2 = 'Helios2'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check for input file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not exists(FILE_NAME_IN):\n", "    print(f'File \\'{FILE_NAME_IN}\\' not found. Please run example \\'py_HLTRGB_1_calibration\\' and \\'py_HLTRGB_2_orientation\\' prior to this one.')\n", "    raise FileNotFoundError('Input file not found')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Devices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Wait for the user to connect a device before raising an exception\n", "'''\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "print(devices)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get a Triton and Helios2 device"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Helper functions to verify <PERSON>ton and Helios device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_applicable_device_triton(device):\n", "    '''\n", "    Return True if a device is a Triton camera, False otherwise\n", "    '''\n", "    model_name = device.nodemap.get_node('DeviceModelName').value\n", "    return \"TRI\" in model_name and \"-C\" in model_name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_applicable_device_helios2(device):\n", "    '''\n", "    Return True if a device is a Helios2 camera, False otherwise\n", "    '''\n", "    model_name = device.nodemap.get_node('DeviceModelName').value\n", "    return \"HLT\" in model_name or \"HT\" in model_name "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Helper function to get a list of applicable Triton or Helios2 devices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_applicable_devices(devices, type):\n", "    '''\n", "    Return a list of applicable Triton devices\n", "    '''\n", "    applicable_devices = []\n", "\n", "    for device in devices:\n", "        if type == TRITON and is_applicable_device_triton(device):\n", "            applicable_devices.append(device)\n", "        elif type == HELIOS2 and is_applicable_device_helios2(device):\n", "            applicable_devices.append(device)\n", "    \n", "    if not len(applicable_devices):\n", "        raise Exception(f'No applicable device found! Please connect an Triton and Helios2 device and run '\n", "                        f'the example again.')\n", "\n", "    print(f'Detected {len(applicable_devices)} applicable {type} device(s)')\n", "    return applicable_devices"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get a list of applicable Triton or Helios2 devices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["applicable_devices_triton = get_applicable_devices(devices, TRITON)\n", "applicable_devices_helios2 = get_applicable_devices(devices, HELIOS2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Select a Triton and Helios2 device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device_triton = system.select_device(applicable_devices_triton)\n", "device_helios2 = system.select_device(applicable_devices_helios2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overlay color onto 3D and save"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get initial node values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [" # Get node values that will be changed in order to return their values at the end of the example\n", "nodemap_triton = device_triton.nodemap\n", "nodemap_helios2 = device_helios2.nodemap\n", "pixel_format_initial_triton = nodemap_triton.get_node(\"PixelFormat\").value\n", "pixel_format_initial_helios2 = nodemap_helios2.get_node(\"PixelFormat\").value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Read in camera matrix, distance coefficients, rotation and translation vectors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'Read camera matrix, distance coefficients, rotation and translation vectors from file {FILE_NAME_IN}')\n", "fs = cv2.FileStorage(FILE_NAME_IN, cv2.FileStorage_READ)\n", "camera_matrix = fs.getNode('cameraMatrix').mat()\n", "dist_coeffs = fs.getNode('distCoeffs').mat()\n", "rotation_vector = fs.getNode('rotationVector').mat()\n", "translation_vector = fs.getNode('translationVector').mat()\n", "fs.release()\n", "\n", "print('cameraMatrix')\n", "print(camera_matrix)\n", "print('dist<PERSON>oeffs')\n", "print(dist_coeffs)\n", "print('rotationVector')\n", "print(rotation_vector)\n", "print('translationVector')\n", "print(translation_vector)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get an image from triton\n", "as `image_matrix_RGB`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set nodes --------------------------------------------------------------\n", "# - pixelformat to RGB8\n", "# - 3D operating mode\n", "nodemap = device_triton.nodemap\n", "nodemap.get_node('PixelFormat').value = PixelFormat.RGB8\n", "\n", "# Set device stream nodemap --------------------------------------------\n", "tl_stream_nodemap = device_triton.tl_stream_nodemap\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "# Get image ---------------------------------------------------\n", "device_triton.start_stream()\n", "buffer = device_triton.get_buffer()\n", "buffer_bytes_per_pixel = int(len(buffer.data)/(buffer.width * buffer.height))\n", "image_matrix = np.asarray(buffer.data, dtype=np.uint8)\n", "image_matrix_RGB = image_matrix.reshape(buffer.height, buffer.width, buffer_bytes_per_pixel)\n", "\n", "# Stop stream -------------------------------------------------\n", "device_triton.requeue_buffer(buffer)\n", "device_triton.stop_stream()\n", "\n", "cv2.imwrite(FILE_NAME_OUT.strip('.ply')+'_RGB.jpg', image_matrix_RGB)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get image from Helios2\n", "as `image_matrix_HLT_intensity`, `image_matrix_HLT_XYZ` along with`height`, `width`, `p_image_HLT`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Helper function to convert buffer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def convert_buffer_to_Coord3D_ABCY16(buffer):\n", "    '''\n", "    Convert to Coord3DD_ABCY16 format\n", "    '''\n", "    if buffer.pixel_format == enums.PixelFormat.Coord3D_ABCY16:\n", "        return buffer\n", "    print(f'Converting image buffer pixel format to Coord3D_ABCY16')\n", "    return BufferFactory.convert(buffer, enums.PixelFormat.Coord3D_ABCY16)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set device stream nodemap --------------------------------------------\n", "tl_stream_nodemap = device_helios2.tl_stream_nodemap\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "# Set nodes --------------------------------------------------------------\n", "# - pixelformat to Coord3D_ABCY16\n", "# - 3D operating mode\n", "nodemap = device_helios2.nodemap\n", "nodemap.get_node('PixelFormat').value = PixelFormat.Coord3D_ABCY16\n", "\n", "# Get node values ---------------------------------------------------------\n", "# Read the scale factor and offsets to convert from unsigned 16-bit values \n", "# in the Coord3D_ABCY16 pixel format to coordinates in mm\n", "\n", "    # \"Coord3D_ABCY16s\" and \"Coord3D_ABCY16\" pixelformats have 4\n", "    # channels per pixel. Each channel is 16 bits and they represent:\n", "    #   - x position\n", "    #   - y postion\n", "    #   - z postion\n", "    #   - intensity\n", "\n", "# get the coordinate scale in order to convert x, y and z values to millimeters as\n", "# well as the offset for x and y to correctly adjust values when in an\n", "# unsigned pixel format\n", "print(f'Get xyz coordinate scales and offsets from nodemap')\n", "xyz_scale_mm = nodemap[\"Scan3dCoordinateScale\"].value # Coordinate scale to convert x, y, and z values to mm\n", "nodemap[\"Scan3dCoordinateSelector\"].value = \"CoordinateA\"\n", "x_offset_mm = nodemap[\"Scan3dCoordinateOffset\"].value # offset for x to adjust values when in unsigned pixel format\n", "nodemap[\"Scan3dCoordinateSelector\"].value = \"CoordinateB\"\n", "y_offset_mm = nodemap[\"Scan3dCoordinateOffset\"].value # offset for y\n", "nodemap[\"Scan3dCoordinateSelector\"].value = \"CoordinateC\"\n", "z_offset_mm = nodemap[\"Scan3dCoordinateOffset\"].value # offset for z\n", "\n", "\n", "# Start stream and get image\n", "device_helios2.start_stream()\n", "buffer = device_helios2.get_buffer()\n", "p_image_HLT = BufferFactory.copy(buffer)\n", "\n", "# Copy image buffer into the Coord3d_ABCY16 format\n", "buffer_Coord3D_ABCY16 = convert_buffer_to_Coord3D_ABCY16(buffer)\n", "\n", "# get height and width\n", "height = int(buffer_Coord3D_ABCY16.height)\n", "width = int(buffer_Coord3D_ABCY16.width)\n", "channels_per_pixel = int(buffer_Coord3D_ABCY16.bits_per_pixel / 16)\n", "\n", "image_matrix_XYZ = np.zeros((height, width, 3), dtype=np.float32)\n", "image_matrix_HLT_intensity = np.zeros((height, width), dtype=np.uint16)\n", "\n", "# get input data\n", "# Buffer.pdata is a (uint8, ctypes.c_ubyte) pointer.\n", "# This pixelformat has 4 channels, and each channel is 16 bits.\n", "# It is easier to deal with Buffer.pdata if it is cast to 16bits\n", "# so each channel value is read correctly.\n", "# The pixelformat is suffixed with \"S\" to indicate that the data\n", "# should be interpereted as signed. This one does not have \"S\", so\n", "# we cast it to unsigned.\n", "pdata_as_uint16 = ctypes.cast(buffer_Coord3D_ABCY16.pdata, ctypes.POINTER(ctypes.c_uint16))\n", "\n", "i = 0\n", "\n", "for ir in range(height):\n", "    for ic in range(width):\n", "\n", "        # Get unsigned 16 bit values for X,Y,Z coordinates\n", "        x_u16 = pdata_as_uint16[i]\n", "        y_u16 = pdata_as_uint16[i + 1]\n", "        z_u16 = pdata_as_uint16[i + 2]\n", "\n", "        # Convert 16-bit X,Y,Z to float values in mm\n", "        image_matrix_XYZ[ir, ic][0] = float(x_u16 * xyz_scale_mm + x_offset_mm)\n", "        image_matrix_XYZ[ir, ic][1] = float(y_u16 * xyz_scale_mm + y_offset_mm)\n", "        image_matrix_XYZ[ir, ic][2] = float(z_u16 * xyz_scale_mm + z_offset_mm)\n", "\n", "        image_matrix_HLT_intensity[ir, ic] = pdata_as_uint16[i + 3]\n", "\n", "        i += channels_per_pixel\n", "\n", "\n", "# Stop stream\n", "device_helios2.requeue_buffer(buffer)\n", "device_helios2.stop_stream()\n", "\n", "image_matrix_HLT_intensity, image_matrix_XYZ, height, width, p_image_HLT\n", "\n", "cv2.imwrite(FILE_NAME_OUT.strip('.ply')+'_XYZ.jpg', image_matrix_XYZ)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overlay RGB color data onto 3D XYZ points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Reshape image matrix"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert the Helios xyz values from 640x480 to a Nx1 matrix to feed into projectPoints\n", "size = image_matrix_XYZ.shape[0] * image_matrix_XYZ.shape[1]\n", "xyz_points = np.reshape(image_matrix_XYZ, (size, 3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Project points"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Use projectPoints to find the position in the Triton image (row,col) of each Helios 3d point\n", "project_points_TRI, _ = cv2.projectPoints(xyz_points, rotation_vector, translation_vector, camera_matrix, dist_coeffs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Overlay color"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Finally, loop through the set of points and access the Triton RGB image at the positions\n", "# calculated by projectPoints to find the RGB value of each 3D point\n", "CustomArrayType = (ctypes.c_byte * (height * width * 3))\n", "color_data = CustomArrayType()\n", "\n", "for i in range(height * width):\n", "    col_TRI = round(project_points_TRI[i][0][0])\n", "    row_TRI = round(project_points_TRI[i][0][1])\n", "\n", "    # Only handle appropriate points\n", "    if row_TRI < 0 or col_TRI < 0 or row_TRI >= image_matrix_RGB.shape[0] or col_TRI >= image_matrix_RGB.shape[1]:\n", "        continue\n", "\n", "    # Access corresponding XYZ and RGB data\n", "    r_val = image_matrix_RGB[row_TRI, col_TRI][0]\n", "    g_val = image_matrix_RGB[row_TRI, col_TRI][1]\n", "    b_val = image_matrix_RGB[row_TRI, col_TRI][2]\n", "\n", "    # Now you have the RGB values of a measured 3D Point at location (X,Y,Z).\n", "    # Depending on your application you can do different things with these values,\n", "    # for example, feed them into a point cloud rendering engine to view a 3D RGB image.\n", "\n", "    # Grab RGB data to save colored .ply\n", "    color_data[i*3] = r_val\n", "    color_data[i*3+1] = g_val\n", "    color_data[i*3+2] = b_val"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save calculated orientation information"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'Save image to {FILE_NAME_OUT}')\n", "\n", "# Prepare to save\n", "# create an image writer\n", "# When saving as .ply file, the writer optionally can take width, \n", "# height, and bits per pixel of the image(s) it would save. \n", "# if these arguments are not passed at run time, the first buffer passed \n", "# to the Writer.save() function will configure the writer to the arguments \n", "# buffer's width, height, and bits per pixel\n", "writer_ply = Writer()\n", "\n", "# uint8_ptr = ctypes.POINTER(ctypes.c_ubyte)\n", "# p_color_data = uint8_ptr(color_data)\n", "# Create p_color_data array\n", "p_color_data = (ctypes.c_ubyte * len(color_data)).from_address(ctypes.addressof(color_data))\n", "\n", "# Save .ply with color data\n", "# save.py > Writer > save\n", "# xwriter.py > Save > _SaveWithColor\n", "# const uint8_t* pColor\n", "# Also example in py_helios_heatmap.py\n", "# and py_save_writer_ply.py\n", "writer_ply.save(p_image_HLT, FILE_NAME_OUT, color=p_color_data, filter_points=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Return nodes to their original values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap_triton.get_node(\"PixelFormat\").value = pixel_format_initial_triton\n", "nodemap_helios2.get_node(\"PixelFormat\").value = pixel_format_initial_helios2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Destroy all created device"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()\n", "print(f'Destroyed all created devices')"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}