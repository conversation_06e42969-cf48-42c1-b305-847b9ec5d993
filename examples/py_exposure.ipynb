{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system\n", "from datetime import datetime"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Exposure: Introduction\n", ">    This example introduces the exposure feature. An image's exposure\n", "    time refers to the amount of time that a device's sensor is exposed\n", "    to a scene before the data is collected. The exposure can be\n", "    handled automatically or manually."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "num_images = 25\n", "exposure_time = 4000.0\n", "timeout = 2000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Waits for the user to connect a device before raising an exception if it fails\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "devices = None\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "nodemap = device.nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "print(f'Device used in the example:\\n{TAB1}{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Store initial values\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes = nodemap.get_node(['ExposureAuto', 'ExposureTime'])\n", "\n", "exposure_auto_initial = nodes['ExposureAuto'].value\n", "exposure_time_initial = nodes['ExposureTime'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates: basic exposure configuration\n", "> - disables automatic exposure\n", "> - gets exposure node\n", "> - ensures exposure above min/below max\n", "> - sets exposure\n", "> - acquires images"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Disable automatic exposure\n", ">   Disable automatic exposure before setting an exposure time. <br>\n", "    Automatic exposure controls whether the exposure time is set\n", "    manually or automatically by the device. Setting automatic\n", "    exposure to 'Off' stops the device from automatically updating\n", "    the exposure time while streaming."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{TAB1}Disable automatic exposure\")\n", "nodes['ExposureAuto'].value = 'Off'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Get exposure time node\n", ">   In order to get the exposure time maximum and minimum values,\n", "    get the exposure time node. Failed attempts to get a node\n", "    return null, so check that the node exists. And because we\n", "    expect to set its value, check that the exposure time node is\n", "    writable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{TAB1}Get exposure time node\")\n", "\n", "if nodes['ExposureTime'] is None:\n", "    raise Exception(\"Exposure Time node not found\")\n", "\n", "if nodes['ExposureTime'].is_writable is False:\n", "    raise Exception(\"Exposure Time node not writeable\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set exposure time\n", ">   Before setting the exposure time, check that new exposure time\n", "    is not outside of the exposure time's acceptable range. If\n", "    above the maximum or below the minimum, update value to be\n", "    within range. Lastly, set new exposure time."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if exposure_time > nodes['ExposureTime'].max:\n", "    nodes['ExposureTime'].value = nodes['ExposureTime'].max\n", "elif exposure_time < nodes['ExposureTime'].min:\n", "    nodes['ExposureTime'].value = nodes['ExposureTime'].min\n", "else:\n", "    nodes['ExposureTime'].value = exposure_time\n", "\n", "print(f\"{TAB1}Set expsoure time to {nodes['ExposureTime'].value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Setup stream values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap = device.tl_stream_nodemap\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Start stream and grab images"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{TAB1}Getting {num_images} images\")\n", "\n", "device.start_stream()\n", "\n", "for i in range(0, num_images):\n", "    buffer = device.get_buffer()\n", "\n", "    print(f'{TAB2}Buffer {i} received | '\n", "            f'Timestamp ({buffer.timestamp_ns} ns)')\n", "\n", "    device.requeue_buffer(buffer)\n", "\n", "device.stop_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Return nodes to intial values\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes[\"ExposureTime\"].value = exposure_time_initial\n", "nodes[\"ExposureAuto\"].value = exposure_auto_initial\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}