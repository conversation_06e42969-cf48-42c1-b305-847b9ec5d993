{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import time\n", "import ctypes\n", "\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.system import system\n", "from arena_api.__future__.save import Writer\n", "from arena_api.enums import PixelFormat\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Polarization: <PERSON><PERSON>\n", "> This example introduces the basics of working with the polarized\n", "\tangles pixel format. Specifically, this example retrieves a 4-channel\n", "\tPolarizedAngles_0d_45d_90d_135d_Mono8 or PolarizedAngles_0d_45d_90d_135d_BayerRG8,\n", "\tdepending on the camera. It first saves the tile data into a 2x2 grid and converts\n", "\tit into a BGR image for saving. Then, it saves the tile data into 4 seperate images."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "TAB3 = \"      \""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Created 1 device(s)\n"]}], "source": ["def create_devices_with_tries():\n", "\t'''\n", "\tThis function waits for the user to connect a device before raising\n", "\t\tan exception\n", "\t'''\n", "\n", "\ttries = 0\n", "\ttries_max = 6\n", "\tsleep_time_secs = 10\n", "\twhile tries < tries_max:  # Wait for device for 60 seconds\n", "\t\tdevices = system.create_device()\n", "\t\tif not devices:\n", "\t\t\tprint(\n", "\t\t\t\tf'{TAB1}Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "\t\t\t\tf'secs for a device to be connected!')\n", "\t\t\tfor sec_count in range(sleep_time_secs):\n", "\t\t\t\ttime.sleep(1)\n", "\t\t\t\tprint(f'{TAB1}{sec_count + 1 } seconds passed ',\n", "\t\t\t\t\t'.' * sec_count, end='\\r')\n", "\t\t\ttries += 1\n", "\t\telse:\n", "\t\t\tprint(f'{TAB1}Created {len(devices)} device(s)')\n", "\t\t\treturn devices\n", "\telse:\n", "\t\traise Exception(f'{TAB1}No device found! Please connect a device and run '\n", "\t\t\t\t\t\tf'the example again.')\n", "\t\n", "devices = create_devices_with_tries()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Select device"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Only one device detected:  ('1c:0f:af:cb:e3:13', 'PHX050S1-Q', 'polar-color', '169.254.20.227')\n", "    Automatically selecting this device.\n"]}], "source": ["device = system.select_device(devices)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Get device stream nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Get nodes ---------------------------------------------------------------\n", "nodes = device.nodemap.get_node(['Width', 'Height', 'PixelFormat'])\n", "\n", "# get node values that will be changed in order to return their values at\n", "# the end of the example\n", "pixel_format_initial_value = nodes['PixelFormat'].value"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Retrieve enumeration entries and check which polarized angles pixel format is supported\n", "pixel_format = '';\n", "for pixel_format_name in nodes['PixelFormat'].enumentry_names:\n", "\tif pixel_format_name == 'PolarizedAngles_0d_45d_90d_135d_BayerRG8':\n", "\t\tpixel_format = pixel_format_name\n", "\telif pixel_format_name == 'PolarizedAngles_0d_45d_90d_135d_Mono8':\n", "\t\tpixel_format = pixel_format_name\n", "\n", "if pixel_format == '':\n", "\tprint(\"\\tError - This example requires PolarizedAngles_0d_45d_90d_135d_* pixel formats\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates acquisition and processing of polarized image data to suppress reflections:\n", "> - Configures the camera to a polarized pixel format.\n", "> - Acquires a polarized input image.\n", "> - Splits the polarized input image into 4 separate images.\n", "> - Saves the images into a 2x2 grid image.\n", "> - Saves the 4 separate images to disk."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Setting Pixel Format to PolarizedAngles_0d_45d_90d_135d_BayerRG8\n"]}], "source": ["# Set pixel format to PolarizedAngles_0d_45d_90d_135d_BayerRG8\n", "print(f'{TAB1}Setting Pixel Format to {pixel_format}')\n", "nodes['PixelFormat'].value = pixel_format"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Starting stream\n", "    Acquire image\n"]}], "source": ["print(f'{TAB1}Starting stream')\n", "device.start_stream(1)\n", "\n", "print(f'{TAB2}Acquire image')\n", "image_buffer = device.get_buffer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set up image buffers"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# src info\n", "src_width = image_buffer.width         \n", "src_height = image_buffer.height\n", "src_pixel_format = image_buffer.pixel_format\n", "\n", "if src_pixel_format != PixelFormat.PolarizedAngles_0d_45d_90d_135d_BayerRG8 and src_pixel_format != PixelFormat.PolarizedAngles_0d_45d_90d_135d_Mono8:\n", "\tprint(\"\\tError - Input image pixel format [{}] is a non-polarized format\".format(src_pixel_format))\n", "\n", "# 2x2 info\n", "dst_2x2_width = src_width * 2\n", "dst_2x2_height = src_height * 2\n", "dst_2x2_pixel_format = PixelFormat.BayerRG8 if src_pixel_format == PixelFormat.PolarizedAngles_0d_45d_90d_135d_BayerRG8 else PixelFormat.Mono8\n", "dst_2x2_step = PixelFormat.get_bits_per_pixel(dst_2x2_pixel_format) // 8\n", "\n", "dst_2x2_stride = dst_2x2_width * dst_2x2_step\n", "dst_2x2_data_size = dst_2x2_width * dst_2x2_height * dst_2x2_step\n", "\n", "# Allocate space for 2x2 grid\n", "dst_data = (ctypes.c_ubyte * dst_2x2_data_size)()\n", "\n", "# Reference set up to starting position of each quadrant of\n", "# destination 2x2 grid to write to\n", "dst_top_left = 0;\n", "dst_top_right = dst_top_left + (dst_2x2_stride / 2)\n", "dst_bottom_left = dst_top_left + (dst_2x2_data_size / 2)\n", "dst_bottom_right = dst_top_left + (dst_2x2_data_size / 2) + (dst_2x2_stride / 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Split polarized image"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Splitting 4-channel pixel format into array of images\n"]}], "source": ["# Split the 4 channels into 4 images\n", "print(f'{TAB2}Splitting 4-channel pixel format into array of images')\n", "polarized_images = BufferFactory.split_channels(image_buffer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 2x2 grid"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Writing image buffers to a 2x2 grid\n"]}], "source": ["def write_to_2x2_grid(img, dst, dst_offset, dst_step, dst_half_stride):\n", "\t\"\"\"\n", "\tHelper function to write an individual image to a location\n", "\t\twithin a 2x2 grid\n", "\t\"\"\"\n", "\tsrc = img.data\n", "\tsrc_h = img.height\n", "\tsrc_w = img.width\n", "\tsrc_step = PixelFormat.get_bits_per_pixel(img.pixel_format) // 8\n", "\n", "\tsrc_index = 0\n", "\tdst_index = int(dst_offset)\n", "\n", "\tfor i in range(src_h):\n", "\t\tfor j in range(src_w):\n", "\t\t\tdst[dst_index] = src[src_index]\n", "\t\t\tsrc_index += int(src_step)\n", "\t\t\tdst_index += int(dst_step)\n", "\t\tdst_index += int(dst_half_stride)\n", "\n", "# Write separate images to a 2x2 grid\n", "print(f'{TAB2}Writing image buffers to a 2x2 grid')\n", "\n", "starting_positions = [dst_top_left, dst_top_right, dst_bottom_left, dst_bottom_right]\n", "for i in range(len(polarized_images)):\n", "\t# Grab image from array of polarized images\n", "\timg = polarized_images[i]\n", "\n", "\t# Write image to 2x2 grid\n", "\twrite_to_2x2_grid(img, dst_data, starting_positions[i], dst_2x2_step, dst_2x2_stride / 2)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Save 2x2 image to c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_polarization_angles\\polarized_2x2_tile.jpg\n"]}], "source": ["uint8_ptr = ctypes.POINTER(ctypes.c_ubyte)\n", "dst_data_ptr = ctypes.cast(dst_data, uint8_ptr)\n", "\t\n", "# Save the 2x2\n", "create_buffer = BufferFactory.create(dst_data_ptr, dst_2x2_data_size, dst_2x2_width, dst_2x2_height, dst_2x2_pixel_format)\n", "output_buffer = BufferFactory.convert(create_buffer, PixelFormat.BGR8 if src_pixel_format == PixelFormat.PolarizedAngles_0d_45d_90d_135d_BayerRG8 else PixelFormat.Mono8)\n", "\n", "writer_jpg = Writer.from_buffer(output_buffer)\n", "\t\n", "writer_jpg.save(output_buffer, 'images/py_polarization_angles/polarized_2x2_tile.jpg')\n", "\n", "print(f'{TAB2}Save 2x2 image to {writer_jpg.saved_images[-1]}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Save each image"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Saving each image to disk\n", "      Save image to c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_polarization_angles\\polarized_0.jpg\n", "      Save image to c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_polarization_angles\\polarized_45.jpg\n", "      Save image to c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_polarization_angles\\polarized_90.jpg\n", "      Save image to c:\\Users\\<USER>\\Documents\\repo\\software\\arena_api\\examples\\images\\py_polarization_angles\\polarized_135.jpg\n"]}], "source": ["def save_image(img, filename):\n", "\t\"\"\"\n", "\tHelper function that takes an image and saves\n", "\t\tit to a given path in disk\n", "\t\"\"\"\n", "\twriter_jpg = Writer.from_buffer(img)\n", "            \n", "\twriter_jpg.save(img, filename)\n", "\t\n", "\tprint(f'{TAB3}Save image to {writer_jpg.saved_images[-1]}')\n", "\n", "# Save each buffer as its own image\n", "print(f'{TAB2}Saving each image to disk')\n", "\n", "degrees = ['0', '45', '90', '135']\n", "for i in range(len(polarized_images)):\n", "\t# Convert image to displayable format\n", "\timg = polarized_images[i]\n", "\tconvert_buffer = BufferFactory.convert(img, PixelFormat.BGR8 if src_pixel_format == PixelFormat.PolarizedAngles_0d_45d_90d_135d_BayerRG8 else PixelFormat.Mono8)\n", "\n", "\t# Save image\n", "\tsave_image(convert_buffer, 'images/py_polarization_angles/polarized_' + degrees[i] + '.jpg')\n", "\n", "\t# Clean up\n", "\tBufferFactory.destroy(convert_buffer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Clean up"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Stream stopped\n"]}], "source": ["# Clean up\n", "BufferFactory.destroy(output_buffer)\n", "BufferFactory.destroy(create_buffer)\n", "\n", "device.requeue_buffer(image_buffer)\n", "\n", "device.stop_stream()\n", "print(f'{TAB1}Stream stopped')"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# restores initial node values\n", "nodes['PixelFormat'].value = pixel_format_initial_value"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Destroyed all created devices\n"]}], "source": ["# Destroy device. Optional, implied by closing of module\n", "system.destroy_device()\n", "print(f'{TAB1}Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}