{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import time\n", "\n", "from arena_api.__future__.save import Writer\n", "from arena_api.system import system\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Helios: Smooth Results\n", "> This example demonstrates the acquisition and saving of images from a Helios camera with smooth results. First, we verify that the camera is a Helios camera. Then, we configure the camera setting exposure time, conversion gain, accumulation, spatial filter, confidence threshold) We then snap a buffer, and save it to a PLY file."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Waits for the user to connect a device before raising\n", "an exception if it fails\n", "'''\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "devices = None\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Check if Helios2 camera is being used for the example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["isHelios2 = False\n", "\n", "# validate if Scan3dCoordinateSelector node exists.\n", "# If not, it is (probably) not a Helios Camera running the example\n", "try:\n", "\tscan_3d_operating_mode_node = device. \\\n", "\t\tnodemap['Scan3dOperatingMode'].value\n", "except KeyError:\n", "\tprint('Scan3dCoordinateSelector node is not found. \\\n", "\t\tPlease make sure that Helios device is used for the example.\\n')\n", "\tsys.exit()\n", "\n", "# validate if Scan3dCoordinateOffset node exists.\n", "# If not, it is (probably) that Helios Camera has an old firmware\n", "try:\n", "\tscan_3d_coordinate_offset_node = device. \\\n", "\t\tnodemap['Scan3dCoordinateOffset'].value\n", "except KeyError:\n", "\tprint('Scan3dCoordinateOffset node is not found. \\\n", "\t\tPlease update Helios firmware.\\n')\n", "\tsys.exit()\n", "\n", "# check if Helios2 camera used for the example\n", "device_model_name_node = device.nodemap['DeviceModelName'].value\n", "if 'HLT' in device_model_name_node:\n", "\tisHelios2 = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Store nodes' initial values ---------------------------------------------\n", "nodemap = device.nodemap\n", "\n", "# get node values that will be changed in order to return their values at\n", "# the end of the example\n", "pixelFormat_initial = nodemap['PixelFormat'].value\n", "operating_mode_initial = nodemap['Scan3dOperatingMode'].value\n", "exposure_time_initial = nodemap['ExposureTimeSelector'].value\n", "conversion_gain_initial = nodemap['ConversionGain'].value\n", "image_accumulation_initial = nodemap['Scan3dImageAccumulation'].value\n", "spatial_filter_initial = nodemap['Scan3dSpatialFilterEnable'].value\n", "confidence_threshold_initial = nodemap['Scan3dConfidenceThresholdEnable'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Set nodes\n", "- pixelformat to Coord3D_ABCY16\n", "- 3D operating mode to Distance1500mm\n", "- time selector to Exp1000Us\n", "- conversion gain to low\n", "- accumulation to 4\n", "- spatial filter\n", "- confidence threshold to true"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('\\nSettings nodes for smooth results')\n", "nodemap = device.nodemap\n", "\n", "# set pixel format\n", "print('\\tSetting pixelformat to Coord3D_ABCY16')\n", "nodemap.get_node('PixelFormat').value = 'Coord3D_ABCY16'\n", "\n", "# set operating mode distance\n", "print('\\tSetting 3D operating mode')\n", "if isHelios2 is True:\n", "\tnodemap['Scan3dOperatingMode'].value = 'Distance3000mmSingleFreq'\n", "else:\n", "\tnodemap['Scan3dOperatingMode'].value = 'Distance1500mm'\n", "\n", "# set exposure time\n", "print('\\tSetting time selector to Exp1000Us')\n", "nodemap['ExposureTimeSelector'].value = 'Exp1000Us'\n", "# set gain\n", "print('\\tSetting conversion gain to low')\n", "nodemap['ConversionGain'].value = 'Low'\n", "# set image accumulation\n", "print('\\tSetting accumulation to 4')\n", "nodemap['Scan3dImageAccumulation'].value = 4\n", "# Enable spatial filter\n", "print('\\tEnable spatial filter')\n", "nodemap['Scan3dSpatialFilterEnable'].value = True\n", "# Enable confidence threshold\n", "print('\\tEnable confidence threshold')\n", "nodemap['Scan3dConfidenceThresholdEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Grab buffers "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Starting the stream allocates buffers and begins filling them with data.\n", "with device.start_stream(1):\n", "\tprint(f'\\nStream started with 1 buffer')\n", "\tprint('\\tGet a buffer')\n", "\n", "\t# This would timeout or return 1 buffers\n", "\tbuffer = device.get_buffer()\n", "\tprint('\\tbuffer received')\n", "\n", "\t# create an image writer\n", "\twriter = Writer()\n", "\n", "\t# save function\n", "\t# buffer :\n", "\t#   buffer to save.\n", "\t# pattern :\n", "\t#   default name for the image is 'image_<count>.jpg' where count\n", "\t#   is a pre-defined tag that gets updated every time a buffer image\n", "\t#   is saved. More custom tags can be added using\n", "\t#   Writer.register_tag() function\n", "\t# kwargs (optional args) ignored if not an .ply image:\n", "\t#   - 'filter_points' default is True.\n", "\t#       Filters NaN points (A = B = C = -32,678)\n", "\t#   - 'is_signed' default is False.\n", "\t#       If pixel format is signed for example PixelFormat.Coord3D_A16s\n", "\t#       then this arg must be passed to the save function else\n", "\t#       the results would not be correct\n", "\t#   - 'scale' default is 0.25.\n", "\t#   - 'offset_a', 'offset_b' and 'offset_c' default to 0.0\n", "\twriter.save(buffer, 'my_3d_image_buffer.ply')\n", "\tprint(f'\\tImage saved {writer.saved_images[-1]}')\n", "\n", "\t# Requeue the chunk data buffers\n", "\tdevice.requeue_buffer(buffer)\n", "\tprint(f'\\tImage buffer requeued')\n", "\n", "# When the scope of the context manager ends, then 'Device.stop_stream()'\n", "print('Stream stopped')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Clean up"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# restores initial node values\n", "nodemap['PixelFormat'].value = pixelFormat_initial\n", "nodemap['Scan3dOperatingMode'].value = operating_mode_initial\n", "nodemap['ExposureTimeSelector'].value = exposure_time_initial\n", "nodemap['ConversionGain'].value = conversion_gain_initial\n", "nodemap['Scan3dImageAccumulation'].value = image_accumulation_initial\n", "nodemap['Scan3dSpatialFilterEnable'].value = spatial_filter_initial\n", "nodemap['Scan3dConfidenceThresholdEnable'].value = confidence_threshold_initial"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This function call with no arguments will destroy all of the\n", "# created devices. Having this call here is optional, if it is not\n", "# here it will be called automatically when the system module is unloading.\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}