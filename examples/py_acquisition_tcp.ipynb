{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: TCP\n", ">   Some 10G higher bandwidth LUCID cameras support TCP streaming.The TCP protocol\n", "\timplements a reliable connection-based stream at the hardware level,\n", "\teliminating the need for a software-based packet resend mechanism."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "            tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        device = devices[0]\n", "        nodemap = device.nodemap\n", "        tl_stream_nodemap = device.tl_stream_nodemap\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Store initial values\")\n", "initial_acquisition_mode = nodemap.get_node(\"AcquisitionMode\").value\n", "width_initial = nodemap.get_node(\"Width\").value\n", "height_initial = nodemap.get_node(\"Height\").value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Configure device settings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap.get_node(\"AcquisitionMode\").value = \"Continuous\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tl_stream_nodemap[\"StreamBufferHandlingMode\"].value = \"NewestOnly\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: TCP\n", ">   Some 10G higher bandwidth LUCID cameras support TCP streaming.The TCP protocol\n", "\timplements a reliable connection-based stream at the hardware level,\n", "\teliminating the need for a software-based packet resend mechanism."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Set Transport Stream Protocol to TCP\\n')\n", "\n", "try:\n", "    nodemap[\"TransportStreamProtocol\"].value = \"TCP\"\n", "except:\n", "    print(\"Connected camera does not support TCP stream\")\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'Getting \\'Width\\' and \\'Height\\' Nodes')\n", "nodes = device.nodemap.get_node(['Width', 'Height'])\n", "\n", "print('Setting \\'Width\\' and \\'Height\\' Nodes value to their '\n", "      'max values')\n", "nodes['Width'].value = nodes['Width'].max\n", "nodes['Height'].value = nodes['Height'].max\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["number_of_buffers = 10\n", "\n", "device.start_stream(number_of_buffers)\n", "print(f'Stream started with {number_of_buffers} buffers')\n", "\n", "print(f'Get {number_of_buffers} buffers in a list')\n", "buffers = device.get_buffer(number_of_buffers)\n", "print(\"Success\")\n", "\n", "'''\n", "Print image buffer info\n", "    Buffers contain image data.\n", "    Image data can also be copied and converted using BufferFactory.\n", "    That is necessary to retain image data, as we must also requeue the buffer.\n", "'''\n", "for count, buffer in enumerate(buffers):\n", "    print(f'\\tbuffer{count:{2}} received | '\n", "        f'Width = {buffer.width} pxl, '\n", "        f'Height = {buffer.height} pxl, '\n", "        f'Pixel Format = {buffer.pixel_format.name}')\n", "\n", "device.requeue_buffer(buffers)\n", "print(f'Requeued {number_of_buffers} buffers')\n", "\n", "device.stop_stream()\n", "print(f'Stream stopped')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode\n", "nodemap.get_node(\"Width\").value = width_initial\n", "nodemap.get_node(\"Height\").value = height_initial\n", "\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}