{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------\n", "# -----------------------------------------------------------------------------\n", "# Warning:\n", "#\n", "# EVS examples support only on windows at the moment\n", "# -----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "from arena_api.system import system\n", "from arena_api.__future__.save import Writer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save: <PERSON><PERSON> Save\n", "\n", "> This example demonstrates how to acquire and save an image using the Event Stream (EVS)format.\n", "> It covers setting up acquisition mode, configuring the camera for EVS,\n", "> and saving the acquired image in the PLY format using the save library.\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "TAB3 = \"\t \"\n", "FILE_NAME = \"Images/Py_EVS_Save_Ply/Py_EVS_Save_Ply.ply\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'{TAB1}Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{TAB1}{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "            tries += 1\n", "    else:\n", "        print(f'{TAB1}Created {len(devices)} device(s)')\n", "        device = system.select_device(devices)\n", "        nodemap = device.nodemap\n", "        tl_stream_nodemap = device.tl_stream_nodemap\n", "        break\n", "else:\n", "    raise Exception(f'{TAB1}No device found! Please connect a device and run '\n", "                    f'the example again.')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["width = nodemap.get_node(\"Width\").value\n", "height = nodemap.get_node(\"Height\").value\n", "print(f'{TAB1}Image (w, h) = ({width} , {height} )')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Configure device settings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Set acquisition mode to \\'Continuous\\'')\n", "initial_acquisition_mode = nodemap.get_node(\"AcquisitionMode\").value\n", "nodemap.get_node(\"AcquisitionMode\").value = \"Continuous\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Set buffer handling mode to \\'NewestOnly\\'')\n", "tl_stream_nodemap[\"StreamBufferHandlingMode\"].value = \"NewestOnly\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: EVS\n", ">\tThe EventFormat node determines whether the camera can use the EVS datastream engine. \n", ">   When set to EVS, Arena switches to the EVS engine. If EVS is not supported, \n", ">   the acquisition mode is restored to its original setting, and the process is exited."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Set Event Format to EVT3.0')\n", "\n", "try:\n", "    event_format_initial = nodemap.get_node('EventFormat').value\n", "    nodemap[\"EventFormat\"].value = \"EVT3_0\"\n", "except:\n", "    print(f'{TAB1}Connected camera does not support any EventFormats\\n')\n", "    nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode\n", "    system.destroy_device()\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set camera event rate to 10 Mev/s"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Set Camera Event Rate to 10 Mev/s')\n", "erc_enable_initial = nodemap.get_node('ErcEnable').value\n", "nodemap[\"ErcEnable\"].value = True\n", "\n", "camera_event_rate_initial = nodemap.get_node('ErcRateLimit').value\n", "nodemap[\"ErcRateLimit\"].value = 10.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#####  Set evs output format to XYTPFrame"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Set EVS output format to XYTPFrame')\n", "tl_stream_nodemap[\"StreamEvsOutputFormat\"].value = \"XYTPFrame\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Image Save Function\n", "\n", " Prepare image parameters and save the image into ply format using writer provide by save library"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["def save_image(image_buffer, filepath):\n", "\tprint(f'{TAB2}Prepare image parameters')\n", "\twidth = image_buffer.width\n", "\theight = image_buffer.height\n", "\tbits_per_pixel = image_buffer.bits_per_pixel\n", "\n", "\t'''\n", "\tThe buffer will be the size of the full image but we need to specify the number of \n", "\tactual vertices stored in the buffer.\n", "\t'''\n", "\tsize_filled = image_buffer.size_filled\n", "\tnum_vertices = int(size_filled / (bits_per_pixel / 8))\n", "\n", "\tprint(f'{TAB2}Prepare image writer')\n", "\t\n", "\twriter = Writer(width,height,bits_per_pixel, num_vertices)\n", "\n", "\twriter.save(image_buffer, filepath)\n", "\tprint(f'{TAB1}Image saved {writer.saved_images[-1]}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Start stream')\n", "device.start_stream(1)\n", "\n", "print(f'{TAB1}Get one image')\n", "\n", "buffer = device.get_buffer()\n", "\n", "\n", "'''\n", "Print image buffer info\n", "    Buffers contain image data.\n", "    Image data can also be copied and converted using BufferFactory.\n", "    That is necessary to retain image data, as we must also requeue the buffer.\n", "'''\n", "\n", "if buffer.is_incomplete:\n", "\tprint(f'{TAB3}Image {buffer.frame_id} is incomplete')\n", "else:\n", "\tsave_image(buffer,FILE_NAME)\n", "\t\n", "device.requeue_buffer(buffer)\n", "print(f'{TAB1}Image buffer requeued')\n", "\n", "device.stop_stream()\n", "print(f'{TAB1}Stream stopped')"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["nodemap.get_node(\"ErcEnable\").value = erc_enable_initial\n", "nodemap.get_node(\"ErcRateLimit\").value = camera_event_rate_initial\n", "nodemap.get_node(\"EventFormat\").value = event_format_initial"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode\n", "\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}