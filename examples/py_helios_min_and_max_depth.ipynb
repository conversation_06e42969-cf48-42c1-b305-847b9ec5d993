{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import ctypes\n", "import sys\n", "import time\n", "\n", "from arena_api.enums import PixelFormat\n", "from arena_api.system import system\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>: Introduction\n", ">This example demonstrates the examination of 3D data. It requires a 3D-capable camera. It verifies the camera, configures its nodes, and then snaps an image. Afterwards, it searches over the image for the pixels with the lowest and highest z-values (depth), and prints it out to the console."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UNSIGNED_16BIT_MAX = 65535\n", "SIGNED_16BIT_MAX = 32767\n", "\n", "# check if Helios2 camera used for the example\n", "isHelios2 = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Check if <PERSON><PERSON>s camera is being used"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# validate if Scan3dCoordinateSelector node exists.\n", "# If not, it is (probably) not a Helios Camera running the example\n", "try:\n", "    scan_3d_operating_mode_node = device. \\\n", "        nodemap['Scan3dOperatingMode'].value\n", "except KeyError:\n", "    print('Scan3dCoordinateSelector node is not found. '\n", "        f'Please make sure that Helios device is used for the example.\\n')\n", "    sys.exit()\n", "\n", "# validate if Scan3dCoordinateOffset node exists.\n", "# If not, it is (probably) that Helios Camera has an old firmware\n", "try:\n", "    scan_3d_coordinate_offset_node = device. \\\n", "        nodemap['Scan3dCoordinateOffset'].value\n", "except KeyError:\n", "    print('Scan3dCoordinateOffset node is not found. '\n", "        f'Please update Helios firmware.\\n')\n", "    sys.exit()\n", "\n", "# check if Helios2 camera used for the example\n", "device_model_name_node = device.nodemap['DeviceModelName'].value\n", "if 'HLT' in device_model_name_node:\n", "    isHelios2 = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PointData:\n", "\t'''\n", "\tstore x, y, z data in mm and intensity for a given point\n", "\t'''\n", "\n", "\tdef __init__(self, x, y, z, intensity):\n", "\t\tself.x = x\n", "\t\tself.y = y\n", "\t\tself.z = z\n", "\t\tself.intensity = intensity\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Find the points with the minimum and maximum z-values\n", "> for signed z-values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_min_and_max_z_for_signed(pdata_16bit, total_number_of_channels,\n", "\t\t\t\t\t\t\t\tchannels_pre_pixel, scale_x, scale_y, scale_z):\n", "\n", "\t# min_depth z value is set to SIGNED_16BIT_MAX to guarantee closer points\n", "\t#    exist as this is the largest value possible\n", "\tmin_depth = PointData(x=0, y=0, z=SIGNED_16BIT_MAX, intensity=0)\n", "\tmax_depth = PointData(x=0, y=0, z=0, intensity=0)\n", "\n", "\tfor i in range(0, total_number_of_channels, channels_pre_pixel):\n", "\n", "\t\t# Extract channels from point/pixel\n", "\t\t#   The first channel is the x coordinate,\n", "\t\t#   the second channel is the y coordinate,\n", "\t\t#   the third channel is the z coordinate, and\n", "\t\t#   the fourth channel is intensity.\n", "\t\t# We offset by 1 for each channel because pdata_16bit is 16 bit\n", "\t\t#  integer\n", "\t\tx = pdata_16bit[i]\n", "\t\ty = pdata_16bit[i + 1]\n", "\t\tz = pdata_16bit[i + 2]\n", "\t\tintensity = pdata_16bit[i + 3]\n", "\n", "\t\tx = int(x * scale_x)\n", "\t\ty = int(y * scale_y)\n", "\t\tz = int(z * scale_z)\n", "\n", "\t\tif 0 < z < min_depth.z:\n", "\t\t\tmin_depth.x = x\n", "\t\t\tmin_depth.y = y\n", "\t\t\tmin_depth.z = z\n", "\t\t\tmin_depth.intensity = intensity\n", "\n", "\t\telif z > max_depth.z:\n", "\t\t\tmax_depth.x = x\n", "\t\t\tmax_depth.y = y\n", "\t\t\tmax_depth.z = z\n", "\t\t\tmax_depth.intensity = intensity\n", "\n", "\treturn min_depth, max_depth\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Find the points with the minimum and maximum z-values\n", "> for unsigned z-values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_min_and_max_z_for_unsigned(pdata_16bit, total_number_of_channels,\n", "\t\t\t\t\t\t\t\t\tchannels_pre_pixel, scale_x, scale_y, scale_z,\n", "\t\t\t\t\t\t\t\t\toffset_x, offset_y):\n", "\t# min_depth z value is set to SIGNED_16BIT_MAX to guarantee closer points\n", "\t# exist as this is the largest value possible\n", "\tmin_depth = PointData(x=0, y=0, z=SIGNED_16BIT_MAX, intensity=0)\n", "\tmax_depth = PointData(x=0, y=0, z=0, intensity=0)\n", "\n", "\tfor i in range(0, total_number_of_channels, channels_pre_pixel):\n", "\n", "\t\t# Extract channels from point/pixel\n", "\t\t#   The first channel is the x coordinate,\n", "\t\t#   the second channel is the y coordinate,\n", "\t\t#   the third channel is the z coordinate, and\n", "\t\t#   the fourth channel is intensity.\n", "\t\t# We offset by 1 for each channel because pdata_16bit is 16 bit\n", "\t\t#  integer\n", "\t\tx = pdata_16bit[i]\n", "\t\ty = pdata_16bit[i + 1]\n", "\t\tz = pdata_16bit[i + 2]\n", "\t\tintensity = pdata_16bit[i + 3]\n", "\n", "\t\t# if z is less than max value, as invalid values get\n", "\t\t# filtered to UNSIGNED_16BIT_MAX\n", "\t\tif z < UNSIGNED_16BIT_MAX:\n", "\t\t\t# Convert x, y and z to millimeters\n", "\t\t\t#   Using each coordinates' appropriate scales,\n", "\t\t\t#   convert x, y and z values to mm. For the x and y\n", "\t\t\t#   coordinates in an unsigned pixel format, we must then\n", "\t\t\t#   add the offset to our converted values in order to\n", "\t\t\t#   get the correct position in millimeters.\n", "\t\t\tx = int((x * scale_x) + offset_x)\n", "\t\t\ty = int((y * scale_y) + offset_y)\n", "\t\t\tz = int(z * scale_y)\n", "\n", "\t\t\tif 0 < z < min_depth.z:\n", "\t\t\t\tmin_depth.x = x\n", "\t\t\t\tmin_depth.y = y\n", "\t\t\t\tmin_depth.z = z\n", "\t\t\t\tmin_depth.intensity = intensity\n", "\n", "\t\t\telif z > max_depth.z:\n", "\t\t\t\tmax_depth.x = x\n", "\t\t\t\tmax_depth.y = y\n", "\t\t\t\tmax_depth.z = z\n", "\t\t\t\tmax_depth.intensity = intensity\n", "\n", "\treturn min_depth, max_depth"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get device stream nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "# Store nodes' initial values ---------------------------------------------\n", "nodemap = device.nodemap\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get node values that will be changed in order to return their values at\n", "# the end of the example\n", "pixelFormat_initial = nodemap['PixelFormat'].value\n", "operating_mode_initial = nodemap['Scan3dOperatingMode'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Set nodes \n", "- pixelformat to Coord3D_ABCY16\n", "- 3D operating mode"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('\\nSettings nodes:')\n", "pixel_format = PixelFormat.Coord3D_ABCY16\n", "print(f'\\tSetting pixelformat to { pixel_format.name}')\n", "nodemap.get_node('PixelFormat').value = pixel_format\n", "\n", "print('\\tSetting 3D operating mode')\n", "if isHelios2 is True:\n", "\tnodemap['Scan3dOperatingMode'].value = 'Distance3000mmSingleFreq'\n", "else:\n", "\tnodemap['Scan3dOperatingMode'].value = 'Distance1500mm'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Get node values \n", "> get the coordinate scale in order to convert x, y and z values to mm as well as the offset for x and y to correctly adjust values when in an unsigned pixel format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Get xyz coordinate scales and offsets from nodemap')\n", "nodemap[\"Scan3dCoordinateSelector\"].value = \"CoordinateA\"\n", "scale_x = nodemap[\"Scan3dCoordinateScale\"].value\n", "offset_x = nodemap[\"Scan3dCoordinateOffset\"].value\n", "nodemap[\"Scan3dCoordinateSelector\"].value = \"CoordinateB\"\n", "scale_y = nodemap[\"Scan3dCoordinateScale\"].value\n", "offset_y = nodemap[\"Scan3dCoordinateOffset\"].value\n", "nodemap[\"Scan3dCoordinateSelector\"].value = \"CoordinateC\"\n", "scale_z = nodemap[\"Scan3dCoordinateScale\"].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Grab buffers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Starting the stream allocates buffers and begins filling them with data.\n", "with device.start_stream(1):\n", "\n", "\tprint(f'\\nStream started with 1 buffer')\n", "\tprint('\\tGet a buffer')\n", "\n", "\t# This would timeout or return 1 buffers\n", "\tbuffer = device.get_buffer()\n", "\tprint('\\tbuffer received')\n", "\n", "\t# buffer info ------------------------------------------------\n", "\n", "\t# \"Coord3D_ABCY16s\" and \"Coord3D_ABCY16\" pixelformats have 4\n", "\t# channels per pixel. Each channel is 16 bits and they represent:\n", "\t#   - x position\n", "\t#   - y postion\n", "\t#   - z postion\n", "\t#   - intensity\n", "\tchannels_per_pixel = int(buffer.bits_per_pixel / 16)\n", "\ttotal_number_of_channels = buffer.width * buffer.height * channels_per_pixel\n", "\n", "\t# find points with min and max z values\n", "\tprint('Finding points with min and max z values')\n", "\n", "\tif buffer.pixel_format == PixelFormat.Coord3D_ABCY16s:\n", "\n", "\t\t# Buffer.pdata is a (uint8, ctypes.c_ubyte) pointer.\n", "\t\t# This pixelformat has 4 channels, and each channel is 16 bits.\n", "\t\t# It is easier to deal with Buffer.pdata if it is casted to 16bits\n", "\t\t# so each channel value is read correctly.\n", "\t\t# The pixelformat is suffixed with \"S\" to indicate that the data\n", "\t\t# should be interpereted as signed.\n", "\t\tpdata_as_int16 = ctypes.cast(buffer.pdata,\n", "\t\t\t\t\t\t\t\t\tctypes.POINTER(ctypes.c_int16))\n", "\n", "\t\t# offset is needed to generate the negative coordinates in the\n", "\t\t# unsigned integer only\n", "\t\tmin_depth, max_depth = find_min_and_max_z_for_signed(pdata_as_int16,\n", "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttotal_number_of_channels,\n", "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tchannels_per_pixel,\n", "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tscale_x, scale_y, scale_z)\n", "\n", "\telif buffer.pixel_format == PixelFormat.Coord3D_ABCY16:\n", "\n", "\t\t# Buffer.pdata is a (uint8, ctypes.c_ubyte) poniter.\n", "\t\t# This pixelformat has 4 channels, and each channel is 16 bits.\n", "\t\t# It is easier to deal with Buffer.pdata if it is casted to 16bits\n", "\t\t# so each channel value is read correctly.\n", "\t\t# The pixelformat is suffixed with \"S\" to indicate that the data\n", "\t\t# should be interpereted as signed. This One does not have \"S\" so\n", "\t\t# we cast it to unsigned\n", "\t\tpdata_as_uint16 = ctypes.cast(buffer.pdata,\n", "\t\t\t\t\t\t\t\t\tctypes.POINTER(ctypes.c_uint16))\n", "\n", "\t\t# offset is needed to generate the negative coordinates in the\n", "\t\t# unsigned integer only\n", "\t\tmin_depth, max_depth = find_min_and_max_z_for_unsigned(pdata_as_uint16,\n", "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttotal_number_of_channels,\n", "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tchannels_per_pixel,\n", "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tscale_x, scale_y, scale_z,\n", "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toffset_x, offset_y)\n", "\n", "\telse:\n", "\t\traise Exception('This example requires the camera to be in either '\n", "\t\t\t\t\t\tf'3D image format Coord3D_ABCY16 or '\n", "\t\t\t\t\t\tf'Coord3D_ABCY16s')\n", "\n", "\t# display data\n", "\tprint(f'\\tMinimum depth point found with '\n", "\t\tf'z distance of {min_depth.z} mm and '\n", "\t\tf'intensity {min_depth.intensity} at coordinates '\n", "\t\tf'( {min_depth.x} mm, {min_depth.y } mm )')\n", "\n", "\tprint(f'\\tMaximum depth point found with '\n", "\t\tf'z distance of {max_depth.z} mm and '\n", "\t\tf'intensity {max_depth.intensity} at coordinates '\n", "\t\tf'( {max_depth.x} mm, {max_depth.y } mm )')\n", "\n", "\t# Requeue the chunk data buffers\n", "\tdevice.requeue_buffer(buffer)\n", "\tprint(f'\\tImage buffer requeued')\n", "\n", "# When the scope of the context manager ends, then 'Device.stop_stream()'\n", "# is called automatically\n", "print('Stream stopped')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Clean up"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# restores initial node values\n", "nodemap['PixelFormat'].value = pixelFormat_initial\n", "nodemap['Scan3dOperatingMode'].value = operating_mode_initial"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Destroy all created devices. This call is optional and will\n", "# automatically be called for any remaining devices when the system module\n", "# is unloading.\n", "system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}