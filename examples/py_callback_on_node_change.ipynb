{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.callback import callback, callback_function\n", "from arena_api.system import system\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Callbacks: On Node Change\n", ">    This example demonstrates configuring a callback to be invoked when a node\n", "    is invalidated. A node is invalidated when its value changes or can be\n", "    invalidated manually. In this example, a callback is registered on\n", "    PayloadSize. The example shows two ways to invoke a callback: first by\n", "    changing the value of a dependent node (Height) and then by invalidating\n", "    PayloadSize manually. Whenever the callback is triggered, the callback\n", "    function prints the updated value of the invalidated node."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "height_one = 256\n", "height_two = 512"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Waits for the user to connect a device before \n", "raising an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                    '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Must have the decorator on the callback function\n", "> node.on_update requires node as its first parameter<br>\n", "This function is triggered when the callback event is triggered"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@callback_function.node.on_update\n", "def print_node(node, *args, **kwargs):\n", "\n", "    print(f'{TAB1}{TAB2}Message from callback')\n", "    print(f'{TAB2}{TAB2}{node.name} : {str(node.value)}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Store initial values\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap = device.nodemap\n", "nodes = nodemap.get_node([\"Height\", \"PayloadSize\"])\n", "\n", "if (nodes[\"PayloadSize\"].is_readable is False):\n", "    raise Exception(\"PaytloadSize not readable\")\n", "\n", "height_initial = nodes[\"Height\"].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates callbacks invoked on node changes\n", "> - registers callback on node PayloadSize\n", "> - changes Height twice to invalidate PayloadSize, invoking callback\n", "> - invalidates PayloadSize manually\n", "> - deregisters callback\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Register the callback on event node\n", "> - Register PayloadSize for callbacks\n", "> > - Callbacks are registered with a node and a function. This example demonstrates callbacks being invoked when the node is invalidated. This could be when the node value changes, either manually or by the device, or when the node is invalidated manually."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{TAB1}Register Callback on PayloadSize\")\n", "handle = callback.register(nodes[\"PayloadSize\"], print_node)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Invoke callbacks\n", "> - Modify Height to invoke callback on PayloadSize\n", "> > - The value of PayloadSize depends on a number of other nodes. This includes Height. Therefore, changing the value of Height changes the value of and invalidates PayloadSize, which then invokes the callback.\n", "> - Manually invalidate PayloadSize for callback\n", "> > - Apart from changing the value of a node, nodes can be invalidated manually by calling InvalidateNode. This also invokes the callback."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{TAB2}Change Height Once\")\n", "nodes[\"Height\"].value = height_one\n", "\n", "print(f\"{TAB2}Change Height Twice\")\n", "nodes[\"Height\"].value = height_two\n", "\n", "print(f\"{TAB2}Invalidate PayloadSize\")\n", "nodes[\"PayloadSize\"].invalidate_node()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### <PERSON><PERSON><PERSON> callback\n", "> - Failing to deregister a callback results in a memory leak. Once a callback has been registered, it will no longer be invoked when a node is invalidated"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB2}Deregister Callback')\n", "callback.deregister(handle)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Restore initial values to the device.\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodes[\"Height\"].value = height_initial\n", "\n", "system.destroy_device(device)"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}