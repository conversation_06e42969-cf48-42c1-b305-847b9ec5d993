{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import threading\n", "from arena_api.system import system\n", "\n", "MAX_IMAGES = 100"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Enumeration: Handling Disconnections\n", ">    This example demonstrates a multi-threaded approach to handling\n", "    device disconnections. It spawns two threads, each with a different\n", "    responsibility. First, the acquisition thread is responsible for\n", "    acquiring images when the device is connected. Second, the\n", "    enumeration thread handles disconnections by reconnecting the\n", "    device and notifying the acquisition thread."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Create global device for acquisition, store its serial number so that we can identify it later.\n", "'''\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "g_device = system.select_device(devices)\n", "g_device_serial = g_device.nodemap.get_node(\"DeviceSerialNumber\").value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Enumeration Thread\n", "> This thread waits until signalled, which happens on device disconnect or completion of acquisition. <br> If the device is disconnected, it will repeatedly wait and check if it has reconnected. <br> Once reconnected, it will recreate the device and signal the acqusition thread. <br> On completion of acquisition, it will simply clean up and terminate."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def enumeration_thread():\n", "    global g_device\n", "    global g_device_serial\n", "    global g_is_running\n", "\n", "    found_device = True\n", "    wait_between_searches_time = 10\n", "\n", "    cv.acquire()\n", "    while g_is_running:\n", "        '''\n", "        While device is active: wait\n", "        '''\n", "        if found_device:\n", "            print(\"\\tSignalling acquisition thread\")\n", "            cv.notify()\n", "            cv.wait()\n", "            # Device disconnected\n", "            print(\"Enumeration thread active\")\n", "        system.destroy_device()\n", "\n", "        '''\n", "        Check for device\n", "        '''\n", "        cv.acquire()\n", "        if g_is_running:\n", "            found_device = False\n", "\n", "            '''\n", "            Get device infos, check for correct serial number\n", "            '''\n", "            print(\"Checking for device\")\n", "            device_infos = system.device_infos\n", "            for device_info in device_infos:\n", "                if (\"serial\", g_device_serial) in device_info.items():\n", "                    print(\"Found device\")\n", "                    found_device = True\n", "\n", "            if found_device:\n", "                devices = system.create_device()\n", "                for device in devices:\n", "                    if device.nodemap.get_node(\"DeviceSerialNumber\").value == g_device_serial:\n", "                        print(\"Connected device\")\n", "                        g_device = device\n", "            else:\n", "                print(\"Failed to find device\")\n", "                print(f\"Waiting {wait_between_searches_time} seconds\")\n", "                time.sleep(wait_between_searches_time)\n", "                print(\"Finished waiting\")\n", "                print()\n", "        else:\n", "            system.destroy_device()\n", "            cv.release()\n", "            print(\"Terminating enumeration thread\")\n", "            continue\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Acquisition Thread\n", ">   Configure global device, then continually retrieve images. <br> Our device configurations persist through disconnects.<br>If the device is disconnected, the thread is locked and we signal the enumeration thread to reconnect it"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def acquisition_thread():\n", "    global g_device\n", "    global g_is_running\n", "\n", "    nodemap = g_device.nodemap\n", "    tl_stream_nodemap = g_device.tl_device_nodemap\n", "\n", "\n", "    initial_acquisition_mode = nodemap.get_node(\"AcquisitionMode\").value\n", "\n", "    # Set acquisition mode to continuous\n", "    nodemap.get_node(\"AcquisitionMode\").value = \"Continuous\"\n", "\n", "    # Get device stream nodemap\n", "    tl_stream_nodemap = g_device.tl_stream_nodemap\n", "\n", "    # Set buffer handling mode to \"Newest First\"\n", "    tl_stream_nodemap[\"StreamBufferHandlingMode\"].value = \"NewestOnly\"\n", "\n", "    # Enable stream auto negotiate packet size\n", "    tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "    # Enable stream packet resend\n", "    tl_stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "    num_images = 0\n", "    g_is_running = True\n", "\n", "    cv.acquire()\n", "\n", "    while g_is_running:\n", "        '''\n", "        While running: lock device across threads, to prevent simultaneous read/writes\n", "            Given timeout: device has become disconnected, we release the condition,\n", "            destroy the device, and wait until the device is reconnected\n", "        '''\n", "        try:\n", "            g_device.start_stream()\n", "\n", "            while g_is_running and num_images <=  MAX_IMAGES:\n", "                print(f\"\\tGet image {num_images + 1}\")\n", "                buffer = g_device.get_buffer(timeout=1000)\n", "                num_images = num_images + 1\n", "                g_device.requeue_buffer(buffer)\n", "\n", "        except TimeoutError:\n", "            print(\"<PERSON><PERSON> disconnected\")\n", "            cv.notify()\n", "            cv.wait()\n", "            print(\"Acquisition thread active\")\n", "\n", "        if num_images > MAX_IMAGES:\n", "            print(\"Acquisition completed\")\n", "            g_is_running = False\n", "            g_device.stop_stream()\n", "            cv.notify()\n", "            cv.release()\n", "        \n", "        \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Start acquisition and enumeration threads and wait for completion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["g_is_running = True\n", "cv = threading.Condition()\n", "threads = []\n", "threads.append(threading.Thread(target = enumeration_thread, args = []))\n", "\n", "\n", "threads[0].start()\n", "\n", "acquisition_thread()\n", "\n", "threads[0].join()"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}