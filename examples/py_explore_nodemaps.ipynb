{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Explore: Node Maps\n", ">    This example explores the 5 available node maps of Arena, 4 retrieved from\n", "    any devices and 1 from the system. It demonstrates traversing nodes\n", "    retrieved as a complete vector.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### This function gets and prints all the features of the nodemap into the terminal and a file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def explore_nodemap(nodemap, nodemap_name):\n", "    feature_nodes_names = nodemap.feature_names\n", "    print(f'{TAB2}Number of nodes: {len(feature_nodes_names)}')\n", "\n", "    list = []\n", "\n", "    for node_name in feature_nodes_names:\n", "        if (nodemap[node_name].interface_type.name == 'CATEGORY') and nodemap[node_name].is_readable:\n", "            list.append(nodemap[node_name].name)\n", "\n", "    with open(f'arena_api_node_exploration_{nodemap_name}.txt', 'w') as f:\n", "        for node_name in feature_nodes_names:\n", "            # print to file\n", "            print(nodemap[node_name], file=f)\n", "\n", "    print(f'{TAB2}Category nodes: ' + str(list))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Explores: node maps\n", "> - retrieves node map from device\n", "> - retrieves node maps from corresponding transport layer modules\n", "> - explores the device node map\n", "> - explores the transport layer device node map\n", "> - explores the transport layer stream node map\n", "> - explores the transport layer interface node map\n", "> - explores the transport layer system node map"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["explore_device = True\n", "explore_tl_device = True\n", "explore_tl_stream = True\n", "explore_tl_interface = True\n", "explore_tl_system = True\n", "\n", "# Explore nodemaps --------------------------------------------------------\n", "if explore_device:\n", "    print(f'{TAB1}Exploring Device nodemap')\n", "    explore_nodemap(device.nodemap, 'device_nodemap')\n", "\n", "if explore_tl_device:\n", "    print(f'{TAB1}Exploring Transport Layer Device nodemap')\n", "    explore_nodemap(device.tl_device_nodemap, 'TL_device_nodemap')\n", "\n", "if explore_tl_stream:\n", "    print(f'{TAB1}Exploring Transport Layer Stream nodemap')\n", "    explore_nodemap(device.tl_stream_nodemap, 'TL_stream_nodemap')\n", "\n", "if explore_tl_interface:\n", "    print(f'{TAB1}Exploring Transport Layer Interface nodemap')\n", "    explore_nodemap(device.tl_interface_nodemap, 'TL_interface_nodemap')\n", "\n", "if explore_tl_system:\n", "    print(f'{TAB1}Exploring Transport Layer System nodemap')\n", "    explore_nodemap(system.tl_system_nodemap, 'TL_system_nodemap')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}