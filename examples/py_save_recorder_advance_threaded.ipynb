{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import queue\n", "import threading\n", "import time\n", "\n", "from arena_api.__future__.save import Recorder\n", "from arena_api.buffer import BufferFactory\n", "from arena_api.enums import PixelFormat\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save Recorder: Advanced Threaded\n", "> This example demonstrates creating a threaded save recorder to save videos by dividing the acquisition and processing tasks on different threads, with max fps and custom names from image buffer data. This includes, configuring and initializing save recorder, setting stream nodes to maximize fps and minimize frame drops getting and appending buffers to the recorder, and saving the video."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Setup stream nodes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "create_device function with no arguments would create a list of\n", "device objects from all connected devices\n", "\"\"\"\n", "nodemap = device.nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "print(f'Device used in the example:\\n\\t{device}')    \n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Configure camera nodes\n", "\"\"\"\n", "\n", "\"\"\"\n", "set width and height to max values might make the video frame rate low\n", "The larger the height of the buffer the lower the fps\n", "\"\"\"\n", "width_node = nodemap['Width']\n", "width = nodemap['Width'].max // 3\n", "\n", "# get a value that aligned with node increments\n", "while width % width_node.inc:\n", "    width -= 1\n", "nodemap['Width'].value = width\n", "\n", "height_node = nodemap['Height']\n", "height = nodemap['Height'].max // 3\n", "\n", "# get a value that aligned with node increments\n", "while height % height_node.inc:\n", "    height -= 1\n", "nodemap['Height'].value = height\n", "\n", "\"\"\"\n", "if the images from the device are already in the pixel format expected\n", "by the recorder then no need to convert the received buffers. This\n", "would result in a better performance\n", "    ``nodemap['PixelFormat'].value = PixelFormat.BGR8``\n", "HOWEVER\n", "For demonstration lets change pixel format so we can show where the\n", "recorder knows how to convert the buffers\n", "\"\"\"\n", "nodemap['PixelFormat'].value = PixelFormat.Mono8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Set up nodes for performance\n", "\"\"\"\n", "# make sure the device sends images continuously\n", "device.nodemap['AcquisitionMode'].value = 'Continuous'\n", "\n", "# automate the calculation of max FPS whenever the device settings change\n", "nodemap['AcquisitionFrameRateEnable'].value = True\n", "\n", "\"\"\"\n", "set FPS node to max FPS which was set to be automatically calculated\n", "base on current device settings\n", "\"\"\"\n", "nodemap['AcquisitionFrameRate'].value = nodemap['AcquisitionFrameRate'].max\n", "\n", "# max FPS according to the current settings\n", "nodemap['DeviceStreamChannelPacketSize'].value = nodemap['DeviceStreamChannelPacketSize'].max\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### demonstrates Save Recorder: Advanced Threaded\n", "1. Setup stream nodes\n", "2. Configure device nodes to maximize fps and minimize frame drop\n", "3. Create a recorder object and configure its width, height, acquisition frame rate and output video name and set threaded to true\n", "4. Open recorder, start stream and get buffers\n", "5. Append buffers to the recorder\n", "6. Close the recorder to save the video"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_images = 100\n", "\n", "with device.start_stream(1):\n", "    print('Stream started')\n", "\n", "    # recorder -------------------------------------------------------------\n", "\n", "    \"\"\"\n", "    creation / configuration -------------------------\n", "    The recorder, takes width, height, and frames per seconds.\n", "    These argument can be deferred until Recorder.open is called.\n", "    \n", "    NOTE:\n", "    `threaded` parameter, None by default, would allow the call to\n", "    `recorder.append()` to return right a way after putting the\n", "    buffer in a share queue to be processed on separate thread.\n", "    It might be faster but it means calling `recorder.close()`\n", "    would have to wait for all appended buffers to be added to the\n", "    video file.\n", "    \"\"\"\n", "    recorder = Recorder(nodemap['Width'].value,\n", "                        nodemap['Height'].value,\n", "                        nodemap['AcquisitionFrameRate'].value,\n", "                        threaded=True)\n", "\n", "\n", "    \"\"\"\n", "    codec expects a tuple that contains\n", "        (video_coding, expected_buffer_pixelformat, file_extension)\n", "    the recorder will convert the buffers to the pixelformat in\n", "    the codec. In this case it will convert Mono8 buffer to BGR8 then\n", "    append them to the video. In fact this conversion happens in the\n", "    background. Here just showing where the recorder knows what format\n", "    to convert to\n", "    \"\"\"\n", "    recorder.codec = ('h264', 'mp4', 'bgr8')  # order does not matter\n", "\n", "    # set video name -----------------------------------\n", "    recorder.pattern = 'My_vid<count>.mp4'\n", "\n", "    \"\"\"\n", "    recorder settings can not be changed after open is called util\n", "    Recorder.close() is called\n", "    \"\"\"\n", "\n", "    \"\"\"\n", "    After recorder.open() add image to the open recorder by\n", "        appending buffers to the video. the recorder expects buffers to be\n", "        in the pixel format specified in Recorder.codec therefor,\n", "        BufferFactory.convert() is used\n", "    The default name for the video is 'video<count>.mp4' where count\n", "        is a pre-defined tag that gets updated every time open()\n", "        is called. More custom tags can be added using\n", "        Recorder.register_tag() function\n", "    \"\"\"\n", "    recorder.open()\n", "    print('recorder opened')\n", "\n", "    # Get buffers --------------------------------------\n", "\n", "    for itr_count in range(total_images):\n", "\n", "        buffer = device.get_buffer()\n", "\n", "        \"\"\"\n", "        append to the open recorder\n", "        this will append the buffer to the video after converting the\n", "        buffer to the format in recorder.codec\n", "        \"\"\"\n", "        recorder.append(buffer)\n", "        print(f'Image buffer {itr_count} appended to video')\n", "\n", "        # requeue buffer back to device\n", "        device.requeue_buffer(buffer)\n", "\n", "    # finalize -----------------------------------------\n", "\n", "    \"\"\"\n", "    must be called after finish adding the buffers\n", "    \"\"\"\n", "    recorder.close() \n", "    print('recorder closed')\n", "\n", "    # video path\n", "    print(f'video saved {recorder.saved_videos[-1]}')\n", "\n", "    # approximate length of the video\n", "    video_length_in_secs = total_images / \\\n", "        nodemap['AcquisitionFrameRate'].value\n", "\n", "    print(f'video length ~= {video_length_in_secs: .3} seconds')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"language_info": {"name": "python"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}