{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os  # os.getcwd()\n", "import time\n", "from pathlib import Path\n", "\n", "import numpy as np  # pip install numpy\n", "from PIL import Image as PIL_Image  # pip install Pillow\n", "\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save: Mono8 to PNG\n", ">\tThis example demonstrates saving the images retrieved\n", "\tby the camera in an easy to share PNG format using PIL.\n", "\tThis includes configuring camera nodes, getting and converting\n", "\tthe buffers into a numpy array, and saving the numpy array \n", "\tusing PIL as a PNG image."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set up stream values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get device stream nodemap\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "# Enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n", "\n", "# Get/Set nodes -----------------------------------------------------------\n", "nodes = device.nodemap.get_node(['Width', 'Height', 'PixelFormat'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Configure camera nodes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Setting Width to its maximum value')\n", "nodes['Width'].value = nodes['Width'].max\n", "\n", "print('Setting Height to its maximum value')\n", "height = nodes['Height']\n", "height.value = height.max"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set pixel format to Mono8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pixel_format_name = 'Mono8'\n", "print(f'Setting Pixel Format to {pixel_format_name}')\n", "nodes['PixelFormat'].value = pixel_format_name"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates Save: Mono8 to PNG\n", "> - setup stream nodemap\n", "> - configure camera nodes to required dimensions\n", "> - start the stream and acquire buffers\n", "> - convert the buffer to numpy array that can be used by PIL\n", "> - convert numpy array to image using PIL\n", "> - save the image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Starting stream')\n", "device.start_stream(1)\n", "\n", "\n", "print('Grabbing an image buffer')\n", "image_buffer = device.get_buffer()\n", "print(f' Width X Height = ' \n", "    f'{image_buffer.width} x {image_buffer.height}')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Saving image with PIL\n", "> To save an image Pi<PERSON> needs an array that is shaped to\n", "    (height, width). In order to obtain such an array we use numpy library"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Buffer Config\n", "> - Buffer.pdata is a (uint8, ctypes.c_ubyte)\n", "> - Buffer.data is a list of elements each represents one byte. <br>\n", "> Therefore for Mono8 each element represents a pixel."]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Method 1 (from Buffer.data)\n", "> dtype is uint8 because Buffer.data returns a list or bytes and pixel\n", "    format is also Mono8.\n", "> > NOTE: <br>\n", "    if 'ChunkModeActive' node value is True then the Buffer.data is\n", "    a list of (image data + the chunkdata) so data list needs to be\n", "    truncated to have image data only. <br>\n", "    can use either :\n", "> >  - device.nodemap['ChunkModeActive'].value   (expensive) \n", "> >  - buffer.has_chunkdata                 (less expensive)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image_only_data = None\n", "if image_buffer.has_chunkdata:\n", "    # 8 is the number of bits in a byte\n", "    bytes_pre_pixel = int(image_buffer.bits_per_pixel / 8)\n", "\n", "    image_size_in_bytes = image_buffer.height * \\\n", "        image_buffer.width * bytes_pre_pixel\n", "\n", "    image_only_data = image_buffer.data[:image_size_in_bytes]\n", "else:\n", "    image_only_data = image_buffer.data\n", "\n", "nparray = np.asarray(image_only_data, dtype=np.uint8)\n", "# Reshape array for pillow\n", "nparray_reshaped = nparray.reshape((\n", "    image_buffer.height,\n", "    image_buffer.width\n", "))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Method 2 (from Buffer.pdata)\n", ">   A more general way (not used in this simple example) <br> <br>\n", "    Creates an already reshaped array to use directly with\n", "    pillow. <br>\n", "    np.ctypeslib.as_array() detects that Buffer.pdata is (uint8, c_ubyte)\n", "    type so it interprets each byte as an element. <br>\n", "    For 16Bit images Buffer.pdata must be cast to (uint16, c_ushort)\n", "    using ctypes.cast(). After casting, np.ctypeslib.as_array() can\n", "    interpret every two bytes as one array element (a pixel). <br> <br>\n", "    \n", "> > Code: <br>\n", "    nparray_reshaped = np.ctypeslib.as_array( image_buffer.pdata, <br>\n", "    (image_buffer.height, image_buffer.width))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Save image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Saving image')\n", "png_name = f'from_{pixel_format_name}_to_png_with_pil.png'\n", "png_array = PIL_Image.fromarray(nparray_reshaped)\n", "png_array.save(png_name)\n", "print(f'Saved image path is: {Path(os.getcwd()) / png_name}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Requeue buffer and stop stream to avoid memory leaks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.requeue_buffer(image_buffer)\n", "device.stop_stream()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()\n", "print('Destroyed all created devices')"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}