{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system\n", "\n", "# Settings\n", "number_of_buffers = 30"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Trigger: Overlapping Trigger on Exposure End Events\n", ">This example demonstrates the use of trigger and exposure end events to minimize the amount of unused exposure time between images. This is done by setting the device to start exposing (or trigger) right when the last exposure has just finished (or exposure end event). After receiving each exposure end event notification, the next trigger is executed to acquire the next image. Once all triggers have executed, the images are retrieved from the output queue. This example shows that there is little time between triggering images, and that the exposure time is close to the time between triggers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Initialize events\n", "> Event nodes aren't available until the events engine has been initialized."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.initialize_events()\n", "\n", "nodemap = device.nodemap\n", "\n", "trigger_selector_node = nodemap.get_node(\"TriggerSelector\")\n", "trigger_mode_node = nodemap.get_node(\"TriggerMode\")\n", "trigger_source_node = nodemap.get_node(\"TriggerSource\")\n", "trigger_overlap_node = nodemap.get_node(\"TriggerOverlap\")\n", "\n", "acquisition_mode = nodemap.get_node(\"AcquisitionMode\")\n", "\n", "event_selector_node = nodemap.get_node(\"EventSelector\")\n", "event_notification_node = nodemap.get_node(\"EventNotification\")\n", "exposure_auto_node = nodemap.get_node(\"ExposureAuto\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Store initial values\n", "initial_acquisition_mode = nodemap.get_node(\"AcquisitionMode\").value\n", "\n", "initial_trigger_selector = trigger_selector_node.value\n", "initial_trigger_mode = trigger_mode_node.value\n", "initial_trigger_source =  trigger_source_node.value\n", "initial_trigger_overlap = trigger_overlap_node.value\n", "\n", "initial_event_selector = event_selector_node.value\n", "inital_event_notification = event_notification_node.value\n", "\n", "initial_exposure_auto = exposure_auto_node.value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure device for image acquisition\n", "nodemap.get_node(\"AcquisitionMode\").value = \"Continuous\"\n", "\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Configure device trigger\n", "1. Set trigger selector\n", "2. Set trigger mode\n", "3. Set trigger source\n", "4. Set trigger overlap\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", " Set trigger selector\n", "    Set the trigger selector to FrameStart. When triggered, the device will\n", "    start acquiring a single frame. This can also be set to\n", "    AcquisitionStart or FrameBurstStart.\n", "'''\n", "\n", "trigger_selector_node.value = \"FrameStart\"\n", "\n", "'''\n", " Set trigger mode\n", "    Enable trigger mode before setting the source and selector and before\n", "    starting the stream. Trigger mode cannot be turned on and off while the\n", "    device is streaming.\n", "'''\n", "\n", "trigger_mode_node.value = \"On\"\n", "\n", "'''\n", " Set trigger source\n", "    Set the trigger source to software in order to trigger images without\n", "    the use of any additional hardware. Lines of the GPIO can also be used\n", "    to trigger.\n", "'''\n", "\n", "trigger_source_node.value = \"Software\"\n", "\n", "'''\n", "Set trigger overlap\n", "    Trigger overlap defines when a trigger can start accepting a new frame.\n", "    Setting trigger to overlap with the previous frame allows the camera to\n", "    being exposing the new frame while the camera is still reading out the\n", "    sensor data acquired from the previous frame.\n", "    std::cout << TAB1 << \"Set trigger overlap to PreviousFrame\\n\";\n", "'''\n", "\n", "trigger_overlap_node.value = \"PreviousFrame\"\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "'''\n", "Set event selector\n", "    We want to trigger and wait to be notified as soon as a certain event\n", "    occurs while making the image. Here we choose to be notified at the end\n", "    of the exposure of an image.\n", "'''\n", "\n", "event_selector_node.value = \"ExposureEnd\"\n", "\n", "'''\n", "Set event notification\n", "    After choosing which event to be notified about, enabling the\n", "    EventNotification node will turn on the notification for the event\n", "    selected in the EventSelector node.\n", "'''\n", "\n", "event_notification_node.value = \"On\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Retrieve exposure time\n", "    The exposure time is similar to the time between triggering images.\n", "    This is shown by turning off automatic exposure, retrieving the\n", "    exposure time and converting to nanoseconds.\n", "'''\n", "\n", "exposure_auto_node.value = \"Off\"\n", "exposure_time = nodemap.get_node(\"ExposureTime\").value\n", "print(f\"Exposure time in nanoseconds: {exposure_time * 1000}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Repeatedly trigger images when the trigger is armed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Trigger an image\n", "    Trigger an image manually, since trigger mode is enabled. This triggers\n", "    the camera to acquire a single image. A buffer is then filled and moved\n", "    to the output queue, where it will wait to be retrieved.\n", "'''\n", "trigger_software_node = nodemap.get_node(\"TriggerSoftware\")\n", "wait_on_event_timeout = 3000\n", "\n", "device.start_stream(number_of_buffers)\n", "for i in range(number_of_buffers):\n", "    '''\n", "    <PERSON><PERSON>\n", "        Continually check until trigger is armed. Once the trigger is\n", "        armed, it is ready to be executed.\n", "    '''\n", "    print(\"Image triggered\", end=\"\")\n", "    is_trigger_armed = False\n", "\n", "    while not is_trigger_armed:\n", "        is_trigger_armed = nodemap.get_node(\"TriggerArmed\")\n", "\n", "    trigger_software_node.execute()\n", "\n", "    '''\n", "    Wait on event\n", "        Wait on event to process before continuing. The data is created\n", "        from the event generation, not from waiting on it.\n", "    '''\n", "    device.wait_on_event(wait_on_event_timeout)\n", "    print(\" and ExposureEnd Event notification arrived\")\n", "\n", "print(f\"Grabbed {number_of_buffers} buffers\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Retrieve images and timestamps\n", ">Images were acquired earlier <br>Must be retrieved from device<br>In buffers that we allocated earlier"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "timestamp_ns = []\n", "\n", "for i in range(number_of_buffers): \n", "    print(\"Getting buffer\")\n", "    buffer = device.get_buffer()\n", "    timestamp_ns.append(buffer.timestamp_ns)\n", "\n", "    print(f\"Image {i} timestamp: {timestamp_ns[i]}\", end=\"\")\n", "\n", "    if i > 0:\n", "        trigger_timestamp_difference = timestamp_ns[i] - timestamp_ns[i - 1]\n", "        print(f\"({trigger_timestamp_difference} ns since last trigger)\")\n", "    else:\n", "        print()\n", "\n", "    device.requeue_buffer(buffer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device.stop_stream()\n", "\n", "# Restore initial values\n", "trigger_source_node.value = initial_trigger_source\n", "trigger_overlap_node.value = initial_trigger_overlap\n", "trigger_selector_node.value = initial_trigger_selector\n", "trigger_mode_node.value = initial_trigger_mode\n", "\n", "event_notification_node.value = inital_event_notification\n", "event_selector_node.value = initial_event_selector\n", "\n", "exposure_auto_node.value = initial_exposure_auto\n", "\n", "nodemap.get_node(\"AcquisitionMode\").value = initial_acquisition_mode\n", "\n", "device.deinitialize_events()\n", "\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "3da5db85d9f5f7fce7af321096f4072067948d3445eede98938ef98c3c692858"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}