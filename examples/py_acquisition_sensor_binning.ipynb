{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Acquisition: Sensor Binning\n", ">    This example demonstrates how to configure device settings to enable binning\n", "    at the sensor level, so that the sensor will combine rectangles of pixels into\n", "    larger \"bins\". This results in reduced resolution of images, but also reduces\n", "    the amount of data sent to the software and networking layers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Check if sensor binning is supported"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nodemap = device.nodemap\n", "binning_selector = nodemap.get_node(\"BinningSelector\")\n", "\n", "print(\"Checking if sensor binning is supported\")\n", "if(\"Sensor\" not in binning_selector.enumentry_names or\n", "        not binning_selector.enumentry_nodes.get(\"Sensor\").is_readable):\n", "    print(\"Sensor binning not supported by device: not available from BinningSelector\")\n", "    quit()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Configure devices and store initial values\n", "> These initial values are restored to the device after the example is completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["acquisitionModeInitial = nodemap.get_node(\"AcquisitionMode\").value\n", "binningSelectorInitial = nodemap.get_node(\"BinningSelector\").value\n", "\n", "binningVerticalModeInitial = nodemap.get_node(\"BinningHorizontalMode\").value\n", "binningHorizontalModeInitial = nodemap.get_node(\"BinningHorizontalMode\").value\n", "\n", "binningVerticalInitial = nodemap.get_node(\"BinningVertical\").value\n", "binningHorizontalInitial = nodemap.get_node(\"BinningHorizontal\").value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Activate sensor binning and maximize bin size"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### This is the entry we will use for BinningVerticalMode and BinningHorizontalMode. \n", ">    Sum will result in a brighter image, compared to Average."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BINTYPE = \"Sum\"\n", "\n", "binning_selector.value = \"Sensor\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set BinningHorizontal and BinningVertical to their maxes.\n", ">    This sets width and height of the bins: the number of pixels along each axis.\n", "    Maximizes compression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["binning_vertical_node = nodemap.get_node(\"BinningVertical\")\n", "binning_horizontal_node = nodemap.get_node(\"BinningHorizontal\")\n", "\n", "if (not nodemap.get_node(\"BinningVertical\").is_writable or\n", "        not nodemap.get_node(\"BinningVertical\").is_writable):\n", "    print(\"Sensor binning is not supported: \"\n", "          \"BinningVertical or BinningHorizontal not available\")\n", "    quit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Find max bin height and width\")\n", "bin_height = binning_vertical_node.max\n", "bin_width = binning_horizontal_node.max"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Set BinningHorizontal and BinningVertical to their maxes.\n", ">    This sets width and height of the bins: the number of pixels along each axis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Set bin height and width to {bin_height} and {bin_width} respectively\")\n", "binning_vertical_node.value = bin_height\n", "binning_horizontal_node.value = bin_width"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Sets binning mode\n", ">    Sets binning mode for horizontal and vertical axes.\n", "    Generally, they're set to the same value."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Set binning mode to {BINTYPE}\")\n", "nodemap.get_node(\"BinningVerticalMode\").value = BINTYPE\n", "nodemap.get_node(\"BinningHorizontalMode\").value = BINTYPE"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Setup stream values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "tl_stream_nodemap = device.tl_stream_nodemap\n", "\n", "nodemap.get_node(\"AcquisitionMode\").value = \"Continuous\"\n", "\n", "# enable stream auto negotiate packet size\n", "tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# enable stream packet resend\n", "tl_stream_nodemap['StreamPacketResendEnable'].value = True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Grab images -------------------------------------------------------------\n", "> - Starting the stream allocates buffers, which can be passed in as\n", " an argument (default: 10), and begins filling them with data.\n", " Buffers must later be requeued to avoid memory leaks.<br>\n", "> - 'Device.get_buffer()' with no arguments returns one buffer(NOT IN A LIST)<br>\n", "> - 'Device.get_buffer(30)' returns 30 buffers(IN A LIST)<br>\n", "> - 'Device.requeue_buffer()' takes a buffer or many buffers in a list or tuple"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["number_of_buffers = 30\n", "\n", "device.start_stream(number_of_buffers)\n", "print(f'Stream started with {number_of_buffers} buffers')\n", "\n", "print(f'\\tGet {number_of_buffers} buffers in a list')\n", "buffers = device.get_buffer(number_of_buffers)\n", "\n", "# Print image buffer info\n", "for count, buffer in enumerate(buffers):\n", "    print(f'\\t\\tbuffer{count:{2}} received | '\n", "          f'Width = {buffer.width} pxl, '\n", "          f'Height = {buffer.height} pxl, '\n", "          f'Pixel Format = {buffer.pixel_format.name}')\n", "\n", "device.requeue_buffer(buffers)\n", "print(f'Requeued {number_of_buffers} buffers')\n", "\n", "device.stop_stream()\n", "print(f'Stream stopped')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Clean up ----------------------------------------------------------------\n", "> - Restore initial values to the device.\n", "> - Destroy device. This call is optional and will automatically be\n", "  called for any remaining devices when the system module is unloading."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Restoring initial configurations\")\n", "nodemap.get_node(\"BinningVertical\").value = binningVerticalInitial\n", "nodemap.get_node(\"BinningHorizontal\").value = binningHorizontalInitial\n", "\n", "nodemap.get_node(\"BinningHorizontalMode\").value = binningVerticalModeInitial\n", "nodemap.get_node(\"BinningHorizontalMode\").value = binningHorizontalModeInitial\n", "\n", "nodemap.get_node(\"BinningSelector\").value = binningSelectorInitial \n", "nodemap.get_node(\"AcquisitionMode\").value = acquisitionModeInitial"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.destroy_device()\n", "print(\"Destroyed all created devices\")"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.9"}}, "nbformat": 4, "nbformat_minor": 4}