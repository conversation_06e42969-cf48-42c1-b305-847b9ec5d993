{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### -----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.\n", "##### THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  ME<PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COPYRIGHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN  THE  SOFTWARE.<br>-----------------------------------------------------------------------------"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from arena_api.system import system\n", "from pprint import pprint\n", "import json"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Force IP\n", ">    This example demonstrates how to force network settings. It does this by\n", "    adding 1 to the final octet of the IP address. It leaves the subnet mask\n", "    and default gateway as is although the same method is used to change these\n", "    as well."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def add_one_to_ip(ip):\n", "    octet0, octet1, octet2, octet3 = ip.split(\".\")\n", "    if octet3 == \"254\":  # Avoid 255\n", "        octet3 = \"1\"\n", "    else:\n", "        octet3 = str(int(octet3) + 1)\n", "    return f\"{octet0}.{octet1}.{octet2}.{octet3}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_formatted_device_info(elem):\n", "\tif elem is not None:\n", "\t\tdict_str = json.dumps(elem, indent=2)\n", "\t\tformatted_str = ''.join([line for line in dict_str.splitlines(True)])\n", "\t\tprint(formatted_str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def select_device_info(device_infos):\n", "\n", "\tif isinstance(device_infos, list):\n", "\t\tif len(device_infos) == 0:\n", "\t\t\traise ValueError('input can not be an empty list')\n", "\t\telif not all(isinstance(d, dict) for d in device_infos):\n", "\t\t\traise TypeError(f'Expected list of dictionary '\n", "                            f'instead of {type(device_infos).__name__}')\n", "\telse:\n", "\t\traise TypeError(f'Expected list of dictionary '\n", "                        f'instead of {type(device_infos).__name__}')\n", "\t\t\t\n", "\tnum_device_infos = len(device_infos)\n", "\tselection = 0\n", "\t\n", "\tif num_device_infos == 1:\n", "\t\tprint_formatted_device_info(device_infos[0])\n", "\t\tprint(f'\\tAutomatically selecting this device info.')\n", "\t\treturn device_infos[0]\n", "        \n", "\tprint(f'Select device info:')\n", "\tfor num, device_info in enumerate(device_infos):\n", "\t\tprint(f'\\t{num+1}.')\n", "\t\tprint_formatted_device_info(device_info)\n", "\t\t\n", "\twhile True:\n", "\t\ttry:\n", "\t\t\tselection = int(input('Make selection (1-' + str(num_device_infos) + '): '))\n", "\t\texcept ValueError:\n", "\t\t\tprint(f'\\tInvalid device info selected, please select a device in range.')\n", "\t\t\tcontinue\n", "\n", "\t\tif 0 < selection and selection <= num_device_infos:\n", "\t\t\tselected_device_info = device_infos[selection-1]\n", "\t\t\tbreak\n", "\t\telse:\n", "\t\t\tprint(f'\\tInvalid device info selected, please select a device in range.')\n", "\n", "\treturn selected_device_info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Discover devices --------------------------------------------------------\n", "\n", "print('Discover devices on network')\n", "device_infos = system.device_infos\n", "print(f'{len(device_infos)} devices found')\n", "\n", "if not device_infos:\n", "    raise BaseException('No device is found!')\n", "\n", "# Choose the first device for this example\n", "device_info = select_device_info(device_infos)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Demonstrates: Forcing an IP address\n", "> - Forcing the IP address requires a device's MAC address to specify the\n", "  device.\n", "> - Grabs the IP address, subnet mask, and default gateway as well to display changes and return the device to its original IP address."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Device info: ')\n", "pprint(device_info, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Create new IP address"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('Current IP = ', device_info['ip'])\n", "new_ip = add_one_to_ip(device_info['ip'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### System.force_ip will used on 'mac' ,'ip' ,'subnetmask' , and 'defaultgateway'."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device_info_new = {\n", "    'mac': device_info['mac'],\n", "    'ip': new_ip,\n", "    'subnetmask': device_info['subnetmask'],\n", "    'defaultgateway': device_info['defaultgateway']\n", "}\n", "print('New IP = ', device_info_new['ip'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Force the new IP address\n", "> Note: The force_ip function can also take a list of device infos to\n", "    force new IP addesses for multiple devices."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print('New IP is being forced')\n", "system.force_ip(device_info_new)\n", "print('New IP was forced successfully')"]}], "metadata": {"interpreter": {"hash": "3620b89036c69a489b14ec4722691b63ae49f46a05ab29903dce938fdeb7f627"}, "kernelspec": {"display_name": "Python 3.9.7 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}