{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<h4>-----------------------------------------------------------------------------<br>Copyright (c) 2024, Lucid Vision Labs, Inc.</h4>\n", "<h5> THE  SOFTWARE  IS  PROVIDED  \"AS IS\",  WITHOUT  WARRANTY  OF  ANY  KIND,<br>EXPRESS  OR  IMPLIED,  INCLUDING  BUT  NOT  LIMITED  TO  THE  WARRANTIES<br>OF  <PERSON><PERSON><PERSON>NTABILITY,  FITNESS  FOR  A  PARTICULAR  PURPOSE  AND<br><PERSON><PERSON><PERSON>RINGEMENT.  IN  NO  EVENT  SHALL  THE  AUTHORS  OR  COP<PERSON><PERSON>GHT  HOLDERS<br>BE  LIABLE  FOR  ANY  CLAIM,  DAMAGES  OR  OTHER  LIABILITY,  WHETHER  IN  AN<br>ACTION  OF  CONTRACT,  TORT  OR  OTHERWISE,  ARISING  FROM,  OUT  OF  OR  IN<br>CONNECTION  WITH  THE  SOFTWARE  OR  THE  USE  OR  OTHER  DEALINGS  IN <br> THE  SOFTWARE.<br>-----------------------------------------------------------------------------</h5>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from arena_api.system import system"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Trigger: <PERSON><PERSON>or<PERSON>extLeader\n", "> WaitForNextLeader feature uses a first packet of every incoming image to inform users that the camera is done integrating. This is an approximation of what the Exposure End event does, but it simplifies the process because we don't need to start a whole new event channel, and reuses data that has to be transmitted already for the purpose of delivering the image to the user."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TAB1 = \"  \"\n", "TAB2 = \"    \"\n", "# Waiting for first packet (leader) of next image buffer timeout\n", "TIMEOUT_MILLISEC = 2000\n", "# Number of image buffers to capture\n", "NUMBER_OF_BUFFERS = 10\n", "\n", "\"\"\"\n", "Wait for first packet of each triggered image buffer.\n", "   In some cases user might need to reset WaitForNextleader state.\n", "   This will run if user set this value to False. I this case\n", "   example will wait for next leader for each 3rd buffer.\n", "\"\"\"\n", "WAIT_FOR_FIRST_PACKET_OF_EVERY_IMAGE_BUFFER = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This function waits for the user to connect a device before raising\n", "an exception\n", "\"\"\"\n", "tries = 0\n", "tries_max = 6\n", "sleep_time_secs = 10\n", "while tries < tries_max:  # Wait for device for 60 seconds\n", "    devices = system.create_device()\n", "    if not devices:\n", "        print(\n", "            f'Try {tries+1} of {tries_max}: waiting for {sleep_time_secs} '\n", "            f'secs for a device to be connected!')\n", "        for sec_count in range(sleep_time_secs):\n", "            time.sleep(1)\n", "            print(f'{sec_count + 1 } seconds passed ',\n", "                  '.' * sec_count, end='\\r')\n", "        tries += 1\n", "    else:\n", "        print(f'Created {len(devices)} device(s)\\n')\n", "        break\n", "else:\n", "    raise Exception(f'No device found! Please connect a device and run '\n", "                    f'the example again.')\n", "\n", "device = system.select_device(devices)\n", "print(f'Device used in the example:\\n\\t{device}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Get node values that will be changed in order to return their values at\n", "the end of the example\n", "\"\"\"\n", "triggerSelector_initial = device.nodemap['TriggerSelector'].value\n", "triggerMode_initial = device.nodemap['TriggerMode'].value\n", "triggerSource_initial = device.nodemap['TriggerSource'].value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### demonstrates trigger configuration and use of NextLeader\n", "1. sets trigger mode, source, and selector\n", "2. starts stream\n", "3. waits until trigger is armed\n", "4. triggers image\n", "5. gets image\n", "6. requeues buffer\n", "7. stops stream"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "Get node values that will be changed in order to return their values at\n", "the end of the example\n", "\"\"\"\n", "triggerSelector_initial = device.nodemap['TriggerSelector'].value\n", "triggerMode_initial = device.nodemap['TriggerMode'].value\n", "triggerSource_initial = device.nodemap['TriggerSource'].value\n", "\n", "\"\"\"\n", "Set trigger selector\n", "    Set the trigger selector to FrameStart. When triggered, the device will\n", "    start acquiring a single frame. This can also be set to\n", "    AcquisitionStart or FrameBurstStart.\n", "\"\"\"\n", "print(f'{TAB1}Set trigger selector to \"FrameStart\"\\t')\n", "device.nodemap['TriggerSelector'].value = 'FrameStart'\n", "\n", "\"\"\"\n", "Set trigger mode\n", "    Trigger mode needs to be set after setting TriggerSelector and before\n", "    starting the stream. Trigger mode cannot be turned on and off while the\n", "    device is streaming.\n", "\"\"\"\n", "print(f'{TAB1}Enable trigger mode\\t')\n", "device.nodemap['TriggerMode'].value = 'On'\n", "\n", "\"\"\"\n", "Set trigger source\n", "    Set the trigger source to software in order to trigger buffers without\n", "    the use of any additional hardware. Lines of the GPIO can also be used\n", "    to trigger.\n", "\"\"\"\n", "print(f'{TAB1}Set trigger source to \"Software\"\\t')\n", "device.nodemap['TriggerSource'].value = 'Software'\n", "\n", "# Enable stream auto negotiate packet size\n", "device.tl_stream_nodemap['StreamAutoNegotiatePacketSize'].value = True\n", "\n", "# Enable stream packet resend\n", "device.tl_stream_nodemap['StreamPacketResendEnable'].value = True\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Start stream\n", ">When trigger mode is off and the acquisition mode is set to stream continuously, starting the stream will have the camera begin acquiring a steady stream of image buffers. However, with trigger mode enabled, the device will wait for the trigger before acquiring any."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f'{TAB1}Start stream\\n')\n", "with device.start_stream(NUMBER_OF_BUFFERS):\n", "\n", "    for buffer in range(NUMBER_OF_BUFFERS):\n", "\n", "        \"\"\"\n", "        <PERSON><PERSON>\n", "            Continually check until trigger is armed. Once the trigger is\n", "            armed, it is ready to be executed.\n", "        \"\"\"\n", "        print(f'{TAB2}Wait until trigger is armed')\n", "\n", "        \"\"\"\n", "        Continually check until trigger is armed. Once the trigger is\n", "        armed, it is ready to be executed.\n", "        \"\"\"\n", "        while not device.nodemap['TriggerArmed'].value:\n", "            continue\n", "\n", "        # This waits for the next leader for each triggered buffer\n", "        if WAIT_FOR_FIRST_PACKET_OF_EVERY_IMAGE_BUFFER:\n", "            \"\"\"\n", "            Trigger an image buffer manually, since trigger mode\n", "            is enabled.\n", "                This triggers the camera to acquire a single image buffer.\n", "                A buffer is then filled and moved to the output queue,\n", "                where it will wait to be retrieved. Before the image\n", "                buffer is sent, the exposure end event will occur.\n", "                This will happen on every iteration\n", "            \"\"\"\n", "            print(f'{TAB2}Trigger Image {buffer + 1}')\n", "            device.nodemap['TriggerSoftware'].execute()\n", "\n", "            \"\"\"\n", "            Wait for next leader\n", "                This will return when the leader for the next buffer\n", "                arrives at the host if it arrives before the timeout.\n", "                Otherwise it will throw a timeout exception\n", "            \"\"\"\n", "            print(f'{TAB2}Wait for leader to arrive')\n", "            device.wait_for_next_leader(TIMEOUT_MILLISEC)\n", "            print(f'{TAB2}Leader has arrived for buffer {buffer + 1}')\n", "\n", "        else:\n", "            # This waits for the leader of every 3rd buffer.\n", "            if buffer % 3 == 0:\n", "                \"\"\"\n", "                Since \"wait\" is not called for the other buffers,\n", "                we call \"reset\" to clear the current \"wait\" state\n", "                before continuing. If we do not do a \"reset\", then\n", "                the next \"wait\" would return immediately for\n", "                the last leader.\n", "                \"\"\"\n", "                print(f'{TAB2}Resetting WaitForNextLeader state')\n", "                device.reset_wait_for_next_leader()\n", "\n", "            device.nodemap['TriggerSoftware'].execute()\n", "\n", "            if buffer % 3 == 0:\n", "                print(f'{TAB2}Wait for leader to arrive')\n", "                device.wait_for_next_leader(TIMEOUT_MILLISEC)\n", "                print(f'{TAB2}Leader has arrived for buffer {buffer + 1}')\n", "        \"\"\"\n", "        Get image buffer\n", "            Once a buffer has been triggered, it can be retrieved.\n", "            If no buffer has been triggered, trying to retrieve\n", "            a buffer will hang for the duration of the timeout and\n", "            then throw an exception.\n", "            Because the device is in a trigger mode calling \"get_buffer()\"\n", "            with any non default argument (ex. \"get_buffer(2)\") will cause\n", "            infinite hang. The device will be waiting for second image\n", "            infinitely.\n", "        \"\"\"\n", "        buffer = device.get_buffer()\n", "\n", "        # Print some info about the image in the buffer\n", "        print(f'{TAB2}Image retrieved ('\n", "                f'frame ID = {buffer.frame_id}, '\n", "                f'timestamp (ns) = {buffer.timestamp_ns})\\n')\n", "\n", "        # Requeue the image buffer\n", "        device.requeue_buffer(buffer)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Clean up"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Return nodes to its initial values\n", "device.nodemap['TriggerSource'].value = triggerSource_initial\n", "device.nodemap['TriggerMode'].value = triggerMode_initial\n", "device.nodemap['TriggerSelector'].value = triggerSelector_initial\n", "\n", "# Clean up ----------------------------------------------------------------\n", "\n", "# Destroy Device\n", "system.destroy_device()"]}], "metadata": {"interpreter": {"hash": "520c5ad4575fadc5aeff8f7fe0ecb965d054482e0492acfe5440edeef6b5197d"}, "kernelspec": {"display_name": "Python 3.6.8 64-bit ('ve_win_dev_py64': venv)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}