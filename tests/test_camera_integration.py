"""
Integration tests for the HeliosCamera class
Tests the camera with real hardware (requires connected Helios camera)
"""

import pytest
import numpy as np
import open3d as o3d
import time
import os
from camera import Helios2Camera

# Mark all tests in this module as requiring hardware
hardware = pytest.mark.skipif("not config.getoption('hardware')")


class TestHeliosCameraIntegration:
    """Integration tests with real camera hardware"""
    
    @pytest.fixture(scope="class")
    def camera(self):
        """Create a camera instance for integration tests"""
        camera = Helios2Camera()
        yield camera
        # Cleanup after all tests in this class
        try:
            camera.stop_capture()
        except:
            pass
    
    @hardware
    def test_camera_initialization(self, camera):
        """Test that camera can be initialized"""
        assert camera is not None
        assert camera.device is None
        assert camera.is_streaming is False
    
    @hardware
    def test_start_capture_real_hardware(self, camera):
        """Test starting capture with real hardware"""
        result = camera.start_capture()
        
        # Should succeed if camera is connected
        assert result is True
        assert camera.is_streaming is True
        assert camera.device is not None
        
        # Check that scales were properly set
        assert camera.scale_x > 0
        assert camera.scale_y > 0
        assert camera.scale_z > 0
    
    @hardware
    def test_stop_capture_real_hardware(self, camera):
        """Test stopping capture with real hardware"""
        # Ensure camera is started first
        camera.start_capture()
        
        result = camera.stop_capture()
        
        assert result is True
        assert camera.is_streaming is False
    
    @hardware
    def test_get_pointcloud_single_capture(self, camera):
        """Test getting a single point cloud"""
        pcd = camera.get_pointcloud()
        
        assert pcd is not None
        assert isinstance(pcd, o3d.geometry.PointCloud)
        assert len(pcd.points) > 0
        assert len(pcd.colors) > 0
        assert len(pcd.points) == len(pcd.colors)
        
        # Verify point cloud properties
        points = np.asarray(pcd.points)
        colors = np.asarray(pcd.colors)
        
        # Check dimensions
        assert points.shape[1] == 3  # X, Y, Z
        assert colors.shape[1] == 3  # R, G, B
        
        # Check that we have reasonable number of points
        assert len(points) > 1000  # Should have many points
        
        # Check coordinate ranges (should be in mm)
        assert np.all(np.isfinite(points))  # No NaN or inf values
        assert np.all(points[:, 2] > 0)  # Z coordinates should be positive
        
        # Check color ranges
        assert np.all(colors >= 0.0)
        assert np.all(colors <= 1.0)
    
    @hardware
    def test_multiple_captures_continuous(self, camera):
        """Test multiple captures with continuous streaming"""
        camera.start_capture()
        
        point_counts = []
        for i in range(5):
            pcd = camera.get_pointcloud()
            assert pcd is not None
            assert len(pcd.points) > 0
            point_counts.append(len(pcd.points))
            time.sleep(0.1)  # Small delay between captures
        
        camera.stop_capture()
        
        # Should have captured multiple point clouds
        assert len(point_counts) == 5
        assert all(count > 1000 for count in point_counts)
        
        # Point counts should be relatively consistent
        mean_count = np.mean(point_counts)
        std_count = np.std(point_counts)
        assert std_count / mean_count < 0.1  # Less than 10% variation
    
    @hardware
    def test_auto_start_stop_behavior(self, camera):
        """Test auto start/stop behavior"""
        # Ensure camera is stopped
        camera.stop_capture()
        assert camera.is_streaming is False
        
        # Get point cloud (should auto-start and stop)
        pcd = camera.get_pointcloud()
        
        assert pcd is not None
        assert camera.is_streaming is False  # Should be stopped after capture
    
    @hardware
    def test_pointcloud_save_and_load(self, camera):
        """Test saving and loading point clouds"""
        pcd = camera.get_pointcloud()
        assert pcd is not None
        
        # Save point cloud
        filename = "integration_test.ply"
        success = o3d.io.write_point_cloud(filename, pcd)
        assert success is True
        assert os.path.exists(filename)
        
        # Load point cloud back
        pcd_loaded = o3d.io.read_point_cloud(filename)
        assert len(pcd_loaded.points) == len(pcd.points)
        assert len(pcd_loaded.colors) == len(pcd.colors)
        
        # Compare points (should be identical)
        points_orig = np.asarray(pcd.points)
        points_loaded = np.asarray(pcd_loaded.points)
        np.testing.assert_array_almost_equal(points_orig, points_loaded, decimal=3)
    
    @hardware
    def test_pointcloud_statistics(self, camera):
        """Test point cloud statistics and properties"""
        pcd = camera.get_pointcloud()
        assert pcd is not None
        
        points = np.asarray(pcd.points)
        colors = np.asarray(pcd.colors)
        
        # Basic statistics
        print(f"\nPoint cloud statistics:")
        print(f"  Number of points: {len(points)}")
        print(f"  X range: {points[:, 0].min():.1f} to {points[:, 0].max():.1f} mm")
        print(f"  Y range: {points[:, 1].min():.1f} to {points[:, 1].max():.1f} mm")
        print(f"  Z range: {points[:, 2].min():.1f} to {points[:, 2].max():.1f} mm")
        print(f"  Color range: {colors.min():.3f} to {colors.max():.3f}")
        
        # Verify reasonable ranges
        assert len(points) > 100000  # Should have many points
        assert points[:, 2].max() > 100  # Should have some depth
        assert points[:, 2].min() >= 0   # No negative depths
    
    @hardware
    def test_performance_timing(self, camera):
        """Test capture performance timing"""
        # Warm up
        camera.start_capture()
        camera.get_pointcloud()
        
        # Time multiple captures
        times = []
        for i in range(10):
            start_time = time.time()
            pcd = camera.get_pointcloud()
            end_time = time.time()
            
            assert pcd is not None
            times.append(end_time - start_time)
        
        camera.stop_capture()
        
        # Analyze timing
        mean_time = np.mean(times)
        max_time = np.max(times)
        
        print(f"\nPerformance timing:")
        print(f"  Mean capture time: {mean_time:.3f} seconds")
        print(f"  Max capture time: {max_time:.3f} seconds")
        
        # Should be reasonably fast
        assert mean_time < 1.0  # Should capture in less than 1 second
        assert max_time < 2.0   # Even worst case should be under 2 seconds


class TestHeliosCameraRobustness:
    """Test camera robustness and error recovery"""
    
    @hardware
    def test_multiple_start_stop_cycles(self):
        """Test multiple start/stop cycles"""
        camera = Helios2Camera()
        
        for i in range(5):
            # Start camera
            result = camera.start_capture()
            assert result is True
            assert camera.is_streaming is True
            
            # Get a point cloud
            pcd = camera.get_pointcloud()
            assert pcd is not None
            
            # Stop camera
            result = camera.stop_capture()
            assert result is True
            assert camera.is_streaming is False
            
            time.sleep(0.1)  # Small delay between cycles
    
    @hardware
    def test_redundant_start_stop_calls(self):
        """Test redundant start/stop calls don't cause issues"""
        camera = Helios2Camera()
        
        # Multiple start calls
        assert camera.start_capture() is True
        assert camera.start_capture() is True  # Should be safe
        assert camera.start_capture() is True  # Should be safe
        
        # Multiple stop calls
        assert camera.stop_capture() is True
        assert camera.stop_capture() is True  # Should be safe
        assert camera.stop_capture() is True  # Should be safe
    
    @hardware
    def test_capture_without_explicit_start(self):
        """Test that capture works without explicit start"""
        camera = Helios2Camera()
        
        # Should auto-start and capture
        pcd = camera.get_pointcloud()
        assert pcd is not None
        
        # Should auto-stop after capture
        assert camera.is_streaming is False
