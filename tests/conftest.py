"""
Pytest configuration and fixtures for camera tests
"""

import pytest
import sys
import os
from unittest.mock import Mock, MagicMock
import numpy as np
import open3d as o3d

# Add parent directory to path so we can import camera module
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def pytest_addoption(parser):
    parser.addoption('--hardware', action='store_true', dest="hardware",
                 default=False, help="enable hardware decorated tests")

@pytest.fixture
def mock_device():
    """Create a mock device for testing"""
    device = Mock()
    device.nodemap = {
        'DeviceModelName': Mock(value='HTW003S-001'),
        'PixelFormat': Mock(value='Coord3D_ABCY16'),
        'Scan3dOperatingMode': Mock(
            value='Distance3000mmSingleFreq',
            enumentry_names=['Distance1250mmSingleFreq', 'Distance3000mmSingleFreq', 'Distance4000mmSingleFreq']
        ),
        'AcquisitionMode': <PERSON><PERSON>(value='Continuous'),
        'Scan3dCoordinateSelector': <PERSON><PERSON>(value='CoordinateA'),
        'Scan3dCoordinateScale': Mock(value=0.25),
        'Scan3dCoordinateOffset': Mock(value=0.0)
    }
    device.start_stream = Mock()
    device.stop_stream = Mock()
    device.get_buffer = Mock()
    device.requeue_buffer = Mock()
    return device


@pytest.fixture
def mock_buffer():
    """Create a mock buffer with 3D data"""
    buffer = Mock()
    buffer.width = 640
    buffer.height = 480
    
    # Create fake 3D data (ABCY format)
    # A=X, B=Y, C=Z, Y=Intensity
    num_pixels = buffer.width * buffer.height
    
    # Create some realistic test data
    x_data = np.random.randint(1000, 5000, num_pixels, dtype=np.uint16)  # X coordinates
    y_data = np.random.randint(1000, 5000, num_pixels, dtype=np.uint16)  # Y coordinates
    z_data = np.random.randint(100, 3000, num_pixels, dtype=np.uint16)   # Z coordinates (depth)
    intensity_data = np.random.randint(1000, 60000, num_pixels, dtype=np.uint16)  # Intensity
    
    # Interleave ABCY data
    data_3d = np.zeros(num_pixels * 4, dtype=np.uint16)
    data_3d[0::4] = x_data  # A channel (X)
    data_3d[1::4] = y_data  # B channel (Y)
    data_3d[2::4] = z_data  # C channel (Z)
    data_3d[3::4] = intensity_data  # Y channel (Intensity)
    
    buffer.data = data_3d.tobytes()
    return buffer


@pytest.fixture
def mock_system(mock_device):
    """Mock the arena_api.system module"""
    system_mock = Mock()
    system_mock.create_device.return_value = [mock_device]
    system_mock.destroy_device = Mock()
    return system_mock


@pytest.fixture
def sample_pointcloud():
    """Create a sample Open3D point cloud for testing"""
    points = np.random.rand(1000, 3) * 1000  # Random points in 1000mm cube
    colors = np.random.rand(1000, 3)  # Random colors
    
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd.colors = o3d.utility.Vector3dVector(colors)
    return pcd


@pytest.fixture(autouse=True)
def cleanup_files():
    """Cleanup test files after each test"""
    yield
    # Clean up any test files created during tests
    test_files = [
        'test_output.ply',
        'test_capture.ply',
        'integration_test.ply'
    ]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
