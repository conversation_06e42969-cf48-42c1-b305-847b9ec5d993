"""
Unit tests for the HeliosCamera class
Tests individual methods and functionality in isolation using mocks
"""

import pytest
import numpy as np
import open3d as o3d
from unittest.mock import Mock, patch, MagicMock
from camera import Helios2Camera


class TestHeliosCameraInit:
    """Test camera initialization"""
    
    def test_init_default_values(self):
        """Test that camera initializes with correct default values"""
        camera = Helios2Camera()
        
        assert camera.device is None
        assert camera.is_streaming is False
        assert camera.scale_x == 1.0
        assert camera.scale_y == 1.0
        assert camera.scale_z == 1.0
        assert camera.offset_x == 0.0
        assert camera.offset_y == 0.0
        assert camera.offset_z == 0.0


class TestHeliosCameraConfiguration:
    """Test camera configuration methods"""
    
    @patch('camera.system')
    def test_configure_optimized_settings(self, mock_system, mock_device):
        """Test that camera configures optimized settings correctly"""
        camera = Helios2Camera()
        camera.device = mock_device
        
        # Call the configuration method
        camera._configure_optimized_settings()
        
        # Verify pixel format was set
        assert mock_device.nodemap['PixelFormat'].value == 'Coord3D_ABCY16'
        
        # Verify operating mode was set
        assert mock_device.nodemap['Scan3dOperatingMode'].value == 'Distance3000mmSingleFreq'
        
        # Verify acquisition mode was set
        assert mock_device.nodemap['AcquisitionMode'].value == 'Continuous'
        
        # Verify coordinate scales were retrieved
        assert camera.scale_x == 0.25
        assert camera.scale_y == 0.25
        assert camera.scale_z == 0.25
    
    def test_configure_without_device_raises_error(self):
        """Test that configuration fails when no device is available"""
        camera = Helios2Camera()
        
        with pytest.raises(RuntimeError, match="No device available"):
            camera._configure_optimized_settings()


class TestHeliosCameraStartStop:
    """Test camera start and stop functionality"""
    
    @patch('camera.system')
    def test_start_capture_success(self, mock_system, mock_device):
        """Test successful camera start"""
        mock_system.create_device.return_value = [mock_device]
        
        camera = Helios2Camera()
        result = camera.start_capture()
        
        assert result is True
        assert camera.is_streaming is True
        assert camera.device is not None
        mock_device.start_stream.assert_called_once()
    
    @patch('camera.system')
    def test_start_capture_no_devices(self, mock_system):
        """Test start capture when no devices are found"""
        mock_system.create_device.return_value = []
        
        camera = Helios2Camera()
        result = camera.start_capture()
        
        assert result is False
        assert camera.is_streaming is False
        assert camera.device is None
    
    @patch('camera.system')
    def test_start_capture_already_streaming(self, mock_system, mock_device):
        """Test start capture when already streaming"""
        camera = Helios2Camera()
        camera.device = mock_device
        camera.is_streaming = True
        
        result = camera.start_capture()
        
        assert result is True
        assert camera.is_streaming is True
        # Should not call start_stream again
        mock_device.start_stream.assert_not_called()
    
    def test_stop_capture_success(self, mock_device):
        """Test successful camera stop"""
        camera = Helios2Camera()
        camera.device = mock_device
        camera.is_streaming = True
        
        result = camera.stop_capture()
        
        assert result is True
        assert camera.is_streaming is False
        mock_device.stop_stream.assert_called_once()
    
    def test_stop_capture_not_streaming(self):
        """Test stop capture when not streaming"""
        camera = Helios2Camera()
        camera.is_streaming = False
        
        result = camera.stop_capture()
        
        assert result is True
        assert camera.is_streaming is False
    
    def test_stop_capture_no_device(self):
        """Test stop capture when no device"""
        camera = Helios2Camera()
        camera.device = None
        camera.is_streaming = False

        result = camera.stop_capture()

        # Should return True when not streaming (no-op)
        assert result is True


class TestHeliosCameraDataProcessing:
    """Test 3D data processing functionality"""
    
    def test_process_3d_buffer_valid_data(self, mock_buffer):
        """Test processing of valid 3D buffer data"""
        camera = Helios2Camera()
        camera.scale_x = 0.25
        camera.scale_y = 0.25
        camera.scale_z = 0.25
        camera.offset_x = 0.0
        camera.offset_y = 0.0
        camera.offset_z = 0.0
        
        points, colors = camera._process_3d_buffer(mock_buffer)
        
        # Check that we got some points
        assert len(points) > 0
        assert len(colors) > 0
        assert len(points) == len(colors)
        
        # Check point dimensions
        assert points.shape[1] == 3  # X, Y, Z
        assert colors.shape[1] == 3  # R, G, B
        
        # Check that coordinates are in reasonable range (scaled)
        assert np.all(points >= 0)  # All coordinates should be positive after scaling
        
        # Check that colors are in valid range [0, 1]
        assert np.all(colors >= 0.0)
        assert np.all(colors <= 1.0)
    
    def test_process_3d_buffer_missing_scales(self, mock_buffer):
        """Test that processing fails when scales are not set"""
        camera = Helios2Camera()
        # Simulate uninitialized scales by setting to None (bypass type checking)
        camera.__dict__['scale_x'] = None

        with pytest.raises(RuntimeError, match="Camera scales and offsets not properly initialized"):
            camera._process_3d_buffer(mock_buffer)


class TestHeliosCameraGetPointcloud:
    """Test point cloud capture functionality"""
    
    @patch('camera.system')
    def test_get_pointcloud_auto_start_stop(self, mock_system, mock_device, mock_buffer):
        """Test get_pointcloud with auto start/stop"""
        mock_system.create_device.return_value = [mock_device]
        mock_device.get_buffer.return_value = mock_buffer
        
        camera = Helios2Camera()
        pcd = camera.get_pointcloud()
        
        # Should have created a point cloud
        assert pcd is not None
        assert isinstance(pcd, o3d.geometry.PointCloud)
        assert len(pcd.points) > 0
        assert len(pcd.colors) > 0
        
        # Should have started and stopped streaming
        mock_device.start_stream.assert_called_once()
        mock_device.stop_stream.assert_called_once()
        mock_device.get_buffer.assert_called_once()
        mock_device.requeue_buffer.assert_called_once_with(mock_buffer)
        
        # Should not be streaming after auto-capture
        assert camera.is_streaming is False
    
    def test_get_pointcloud_already_streaming(self, mock_device, mock_buffer):
        """Test get_pointcloud when camera is already streaming"""
        mock_device.get_buffer.return_value = mock_buffer
        
        camera = Helios2Camera()
        camera.device = mock_device
        camera.is_streaming = True
        camera.scale_x = 0.25
        camera.scale_y = 0.25
        camera.scale_z = 0.25
        
        pcd = camera.get_pointcloud()
        
        # Should have created a point cloud
        assert pcd is not None
        assert isinstance(pcd, o3d.geometry.PointCloud)
        
        # Should still be streaming
        assert camera.is_streaming is True
        
        # Should not have called start/stop stream
        mock_device.start_stream.assert_not_called()
        mock_device.stop_stream.assert_not_called()
    
    @patch('camera.system')
    def test_get_pointcloud_start_failure(self, mock_system):
        """Test get_pointcloud when camera fails to start"""
        mock_system.create_device.return_value = []  # No devices
        
        camera = Helios2Camera()
        pcd = camera.get_pointcloud()
        
        assert pcd is None
        assert camera.is_streaming is False
    
    def test_get_pointcloud_no_device(self):
        """Test get_pointcloud when device is None"""
        camera = Helios2Camera()
        camera.device = None
        camera.is_streaming = True  # Inconsistent state for testing
        
        pcd = camera.get_pointcloud()
        
        assert pcd is None


class TestHeliosCameraCleanup:
    """Test camera cleanup functionality"""
    
    @patch('camera.system')
    def test_destructor_cleanup(self, mock_system, mock_device):
        """Test that destructor properly cleans up resources"""
        camera = Helios2Camera()
        camera.device = mock_device
        camera.is_streaming = True
        
        # Call destructor
        camera.__del__()
        
        # Should have stopped streaming and destroyed device
        mock_device.stop_stream.assert_called_once()
        mock_system.destroy_device.assert_called_once()
    
    def test_destructor_no_device(self):
        """Test destructor when no device is present"""
        camera = Helios2Camera()
        camera.device = None
        
        # Should not raise any exceptions
        camera.__del__()


class TestHeliosCameraErrorHandling:
    """Test error handling scenarios"""
    
    @patch('camera.system')
    def test_start_capture_exception(self, mock_system):
        """Test start capture when an exception occurs"""
        mock_system.create_device.side_effect = Exception("Device error")
        
        camera = Helios2Camera()
        result = camera.start_capture()
        
        assert result is False
        assert camera.is_streaming is False
    
    def test_stop_capture_exception(self, mock_device):
        """Test stop capture when an exception occurs"""
        mock_device.stop_stream.side_effect = Exception("Stop error")
        
        camera = Helios2Camera()
        camera.device = mock_device
        camera.is_streaming = True
        
        result = camera.stop_capture()
        
        assert result is False
        # Should still update streaming status despite error
        assert camera.is_streaming is False
    
    @patch('camera.system')
    def test_get_pointcloud_exception(self, mock_system, mock_device):
        """Test get_pointcloud when an exception occurs during capture"""
        mock_system.create_device.return_value = [mock_device]
        mock_device.get_buffer.side_effect = Exception("Buffer error")
        
        camera = Helios2Camera()
        pcd = camera.get_pointcloud()
        
        assert pcd is None
        # Should have cleaned up (stopped streaming)
        assert camera.is_streaming is False
