#!/usr/bin/env python3
"""
Final Working Helios 3D Camera Processing Example
Creates same output as C examples but in Python
"""

import numpy as np
import matplotlib.pyplot as plt
from arena_api.system import system
from arena_api.__future__.save import Writer
from arena_api.buffer import BufferFactory
import ctypes

def process_3d_data(buffer):
    """
    Process Coord3D_ABCY16 data format properly
    """
    print("  Processing 3D data...")
    
    width = buffer.width
    height = buffer.height
    
    # Convert buffer data (list) to bytes then to numpy array
    data_bytes = bytes(buffer.data)
    data = np.frombuffer(data_bytes, dtype=np.uint16)
    
    # Reshape to (height, width, 4) for ABCY channels
    image_3d = data.reshape((height, width, 4))
    
    # Extract channels
    A_channel = image_3d[:, :, 0]  # X coordinate
    B_channel = image_3d[:, :, 1]  # Y coordinate  
    C_channel = image_3d[:, :, 2]  # Z coordinate (depth)
    Y_channel = image_3d[:, :, 3]  # Intensity
    
    print(f"    Image dimensions: {width}x{height}")
    print(f"    A (X) range: {A_channel.min()} - {A_channel.max()}")
    print(f"    B (Y) range: {B_channel.min()} - {B_channel.max()}")
    print(f"    C (Z) range: {C_channel.min()} - {C_channel.max()}")
    print(f"    Y (Intensity) range: {Y_channel.min()} - {Y_channel.max()}")
    
    return A_channel, B_channel, C_channel, Y_channel

def create_heatmap(depth_data, intensity_data, filename):
    """
    Create depth heatmap similar to C example
    """
    print(f"  Creating heatmap: {filename}")
    
    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Depth heatmap
    im1 = ax1.imshow(depth_data, cmap='hot', interpolation='nearest')
    ax1.set_title('Depth (Z) Data - Python Generated')
    ax1.set_xlabel('X pixels')
    ax1.set_ylabel('Y pixels')
    plt.colorbar(im1, ax=ax1, label='Depth value')
    
    # Intensity image
    im2 = ax2.imshow(intensity_data, cmap='gray', interpolation='nearest')
    ax2.set_title('Intensity (Y) Data - Python Generated')
    ax2.set_xlabel('X pixels')
    ax2.set_ylabel('Y pixels')
    plt.colorbar(im2, ax=ax2, label='Intensity value')
    
    plt.tight_layout()
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    print(f"    Heatmap saved as {filename}")

def find_min_max_depth(depth_data, intensity_data):
    """
    Find points with minimum and maximum depth values
    Similar to C example output
    """
    print("  Finding min/max depth points...")
    
    # Mask out invalid points (typically 0 values)
    valid_mask = depth_data > 0
    
    if not np.any(valid_mask):
        print("    No valid depth data found!")
        return
        
    valid_depth = depth_data[valid_mask]
    
    min_depth = np.min(valid_depth)
    max_depth = np.max(valid_depth)
    
    # Find indices of min and max
    min_idx = np.where(depth_data == min_depth)
    max_idx = np.where(depth_data == max_depth)
    
    if len(min_idx[0]) > 0:
        min_y, min_x = min_idx[0][0], min_idx[1][0]
        min_intensity = intensity_data[min_y, min_x]
        print(f"    Minimum depth point found with z distance of {min_depth} and intensity {min_intensity} at coordinates ({min_x}, {min_y})")
    
    if len(max_idx[0]) > 0:
        max_y, max_x = max_idx[0][0], max_idx[1][0]
        max_intensity = intensity_data[max_y, max_x]
        print(f"    Maximum depth point found with z distance of {max_depth} and intensity {max_intensity} at coordinates ({max_x}, {max_y})")

def get_rgb_colors_for_ply(depth_data, intensity_data):
    """
    Create RGB color array for PLY file based on intensity values
    """
    print("  Creating RGB colors for PLY file based on intensity...")

    height, width = depth_data.shape
    number_of_pixels = width * height

    # Create RGB array for PLY coloring
    RGB8_channels_per_pixel = 3  # RED, Green, Blue
    RGB8_channel_size_bits = 8
    array_RGB8_size_in_bytes = (RGB8_channel_size_bits * RGB8_channels_per_pixel * number_of_pixels) // 8

    # Create array for RGB colors
    CustomArrayType = (ctypes.c_ubyte * array_RGB8_size_in_bytes)
    array_RGB8_for_ply = CustomArrayType()

    # Flatten intensity data for processing
    intensity_values = intensity_data.flatten()
    depth_values = depth_data.flatten()

    # Find valid intensity range (excluding zero/invalid points)
    valid_mask = depth_values > 0
    if np.any(valid_mask):
        valid_intensities = intensity_values[valid_mask]
        intensity_min = np.min(valid_intensities)
        intensity_max = np.max(valid_intensities)
        intensity_range = intensity_max - intensity_min
        print(f"    Intensity range for coloring: {intensity_min} - {intensity_max}")
    else:
        print("    No valid intensity data found!")
        intensity_min = 0
        intensity_max = 65535  # Max for 16-bit
        intensity_range = intensity_max

    # Avoid division by zero
    if intensity_range == 0:
        intensity_range = 1

    for i in range(number_of_pixels):
        # Skip invalid points (where depth is 0 or negative)
        if depth_values[i] <= 0:
            # Set invalid points to black
            idx = i * 3
            array_RGB8_for_ply[idx] = ctypes.c_ubyte(0)      # Red
            array_RGB8_for_ply[idx + 1] = ctypes.c_ubyte(0)  # Green
            array_RGB8_for_ply[idx + 2] = ctypes.c_ubyte(0)  # Blue
            continue

        # Normalize intensity to 0-255 range
        intensity = intensity_values[i]
        normalized_intensity = (intensity - intensity_min) / intensity_range
        normalized_intensity = max(0.0, min(1.0, normalized_intensity))  # Clamp to [0,1]

        # Convert to grayscale (0-255)
        gray_value = int(normalized_intensity * 255)

        # Store RGB values (grayscale, so R=G=B)
        idx = i * 3
        array_RGB8_for_ply[idx] = ctypes.c_ubyte(gray_value)      # Red
        array_RGB8_for_ply[idx + 1] = ctypes.c_ubyte(gray_value)  # Green
        array_RGB8_for_ply[idx + 2] = ctypes.c_ubyte(gray_value)  # Blue

    return array_RGB8_for_ply

def main():
    print("\nFinal Helios 3D Python Example")
    print("Creating same output as C examples")
    print("=" * 45)
    
    try:
        # Create device
        print("  Creating device...")
        devices = system.create_device()
        if not devices:
            print("  No devices found!")
            return
            
        device = devices[0]
        device_info = device.nodemap['DeviceModelName'].value
        print(f"  Only one device detected, automatically selecting this device.")
        print(f"  Device: {device_info}")
        
        # Set to 3D format
        print("  Setting Coord3D_ABCY16 to pixel format")
        device.nodemap['PixelFormat'].value = 'Coord3D_ABCY16'
        
        # Set operating mode
        print("  Set 3D operating mode to Distance3000mm")
        device.nodemap['Scan3dOperatingMode'].value = 'Distance3000mmSingleFreq'
        
        # Get z coordinate scale for PLY conversion
        print("  Get z coordinate scale from nodemap")
        device.nodemap["Scan3dCoordinateSelector"].value = "CoordinateC"
        scale_z = device.nodemap["Scan3dCoordinateScale"].value

        # Start stream
        device.start_stream()
        
        # Get an image
        print("  Acquire image")
        buffer = device.get_buffer()
        
        # Process 3D data
        A, B, C, Y = process_3d_data(buffer)
        
        # Find min/max depth (similar to C example)
        find_min_max_depth(C, Y)
        
        # Create heatmap
        create_heatmap(C, Y, 'Python_Helios_HeatMap.png')
        
        # Save as PLY file
        print("  Saving 3D data as PLY file...")
        rgb_colors = get_rgb_colors_for_ply(C, Y)
        
        # Create pointer to RGB colors
        ptr_array_RGB_colors = ctypes.cast(rgb_colors, ctypes.POINTER(ctypes.c_ubyte))
        
        # Save PLY file
        writer_ply = Writer()
        writer_ply.save(buffer, 'Python_Helios_3D.ply',
                       color=ptr_array_RGB_colors,
                       filter_points=True)
        print("  PLY file saved as Python_Helios_3D.ply")
        
        # Save image info similar to C example
        with open("Python_Helios_Summary.txt", "w") as f:
            f.write(f"Python Helios 3D Capture Summary\n")
            f.write(f"=================================\n")
            f.write(f"Device: {device_info}\n")
            f.write(f"Resolution: {buffer.width}x{buffer.height}\n")
            f.write(f"Pixel format: Coord3D_ABCY16\n")
            f.write(f"Timestamp: {buffer.timestamp_ns} ns\n")
            valid_depth = C[C>0]
            if len(valid_depth) > 0:
                f.write(f"Depth range: {valid_depth.min()} - {valid_depth.max()}\n")
            f.write(f"Intensity range: {Y.min()} - {Y.max()}\n")
            f.write(f"Valid depth points: {len(valid_depth)} / {C.size}\n")
            
        print(f"  Save image as Python_Helios_HeatMap.png")
        print("  Summary saved to Python_Helios_Summary.txt")
        
        # Requeue buffer and cleanup
        device.requeue_buffer(buffer)
        device.stop_stream()
        
    except Exception as e:
        print(f"  Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        system.destroy_device()
        
    print("\nExample complete")
    print("Python capture finished successfully!")

if __name__ == "__main__":
    main()

